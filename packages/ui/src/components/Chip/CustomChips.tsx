'use-client'
import {
  Chip,
  ChipProps as MuiChipProps,
  Stack,
  styled,
  Typography,
} from '@mui/material'
import CircleIcon from '@mui/icons-material/Circle'
import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined'
import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined'
import RestoreIcon from '@mui/icons-material/Restore'

import { CHIPCOLORS } from '../../const'

export interface ChipProps extends MuiChipProps {
  // Add any additional props here if needed
}

export type SeverityStatus =
  | 'success'
  | 'error'
  | 'info'
  | 'warn'
  | 'processing'
  | 'default'
  | 'neutral'
export type IStatus =
  | 'ACTIVE'
  | 'DEACTIVATED'
  | 'PENDING'
  | 'NEW'
  | 'REJECTED'
  | 'RESTRICTED'

type StatusChipProps = ChipProps & {
  status?: SeverityStatus
}

export const CustomStatusChip = styled(Chip)<ChipProps>(() => ({
  border: 'none',
  fontWeight: '600',
  padding: '2px 8px 2px 6px',
  justifyContent: 'center',
  alignItems: 'center',
  gap: '6px',
}))

CustomStatusChip.displayName = 'CustomStatusChip'

export const CustomSuccessChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={<CircleIcon sx={{ fontSize: '8px' }} color="success" />}
      variant="outlined"
      sx={{ color: '#027A48', backgroundColor: '#ECFDF3' }}
      {...props}
    />
  )
}
export const CustomBlockedChip = styled(Chip)<ChipProps>(() => ({
  backgroundColor: '#FFFF',
  color: '#D92D20',
  border: `2px solid #D92D20`,
  fontWeight: 500,
}))

CustomBlockedChip.displayName = 'CustomBlockedChip'

export const CustomPendingOrgApprovalChip = styled(Chip)<ChipProps>(() => ({
  backgroundColor: '#FFFF',
  color: '#D92D20',
  border: `2px solid #D92D20`,
  fontWeight: 500,
}))

CustomPendingOrgApprovalChip.displayName = 'CustomPendingOrgApprovalChip'

export const CustomActiveBrokerChip = styled(Chip)<ChipProps>(() => ({
  backgroundColor: '#FFFF',
  color: '#079455',
  border: `2px solid #079455`,
  fontWeight: 500,
}))

CustomActiveBrokerChip.displayName = 'CustomActiveBrokerChip'

export const CustomInactiveBrokerChip = styled(Chip)<ChipProps>(() => ({
  backgroundColor: '#FFFF',
  color: '#1570EF',
  border: `2px solid #1570EF`,
  fontWeight: 500,
}))

CustomInactiveBrokerChip.displayName = 'CustomInactiveBrokerChip'

export const CustomPendingApprovalChip = styled(Chip)<ChipProps>(() => ({
  backgroundColor: '#FFFF',
  color: '#E04F16',
  border: `2px solid #E04F16`,
  fontWeight: 500,
}))

CustomPendingApprovalChip.displayName = 'CustomPendingApprovalChip'

export const StatusChip = ({
  status = 'success',
  sx,
  ...props
}: StatusChipProps) => {
  const styles = {
    warn: {
      color: '#B93815',
      backgroundColor: '#FEF6EE',
      border: '1px solid #F9DBAF',
    },
    info: {
      color: '#1570EF',
      backgroundColor: 'rgba(21,112,239,0.1)',
      border: '1px solid #1570EF',
    },
    success: {
      color: '#067647',
      backgroundColor: '#ECFDF3',
      border: '1px solid #ABEFC6',
    },
    error: {
      color: '#B93815',
      backgroundColor: '#FEF6EE',
      border: '1px solid #F9DBAF',
    },
    neutral: {
      color: '#000000',
      backgroundColor: '#FFFFFF',
      border: '1px solid #CCCCCC',
    },
    processing: {
      color: '#5925DC',
      backgroundColor: '#F4F3FF',
      border: '1px solid #D9D6FE',
    },
    default: {
      color: '#344054',
      backgroundColor: '#F9FAFB',
      border: '1px solid #E4E7EC',
    },
  }

  return (
    <CustomStatusChip
      variant="outlined"
      sx={{
        borderRadius: '6px',
        fontSize: '12px',
        height: '25px',
        ...sx,
        ...styles[status],
      }}
      {...props}
    />
  )
}

export const CustomActiveChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={
        <CircleIcon sx={{ fontSize: '8px', color: '#12B76A !important' }} />
      }
      variant="outlined"
      sx={{
        color: '#027A48',
        backgroundColor: '#ECFDF3',
        fontWeight: 500,
        fontSize: '12px',
        height: '20px',
      }}
      {...props}
    />
  )
}
export const CustomPaymentStatusChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={<CircleIcon sx={{ fontSize: '8px' }} color={'success'} />}
      variant="outlined"
      sx={{
        border: '1px solid #D0D5DD',
        borderRadius: '8px',
        fontWeight: '500',
      }}
      {...props}
    />
  )
}
export const CustomWarningChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={
        <CircleIcon sx={{ fontSize: '8px', color: '#E16012 !important' }} />
      }
      variant="outlined"
      sx={{
        color: '#E16012',
        backgroundColor: '#FFF6ED',
        fontWeight: 500,
        fontSize: '12px',
        height: '20px !important',
      }}
      {...props}
    />
  )
}

export const CustomErrorChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={<CircleIcon sx={{ fontSize: '8px' }} color="error" />}
      variant="outlined"
      sx={{
        color: '#B42318',
        backgroundColor: '#FEE4E2',
        fontWeight: 500,
        fontSize: '12px',
        height: '20px !important',
      }}
      {...props}
    />
  )
}

export const CustomLoanSuccessChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={
        <CheckCircleOutlinedIcon sx={{ fontSize: '12px' }} color={'info'} />
      }
      variant="outlined"
      sx={{ color: '#175CD3', backgroundColor: '#EFF8FF' }}
      {...props}
    />
  )
}

export const CustomLoanFailedChip = (props: ChipProps) => {
  return (
    <CustomStatusChip
      icon={
        <ErrorOutlineOutlinedIcon sx={{ fontSize: '12px' }} color={'error'} />
      }
      variant="outlined"
      sx={{ color: '#B42318', backgroundColor: '#FEF3F2' }}
      {...props}
    />
  )
}

export const CustomDrawerChip = styled(Chip)<ChipProps>(() => ({
  border: '1px solid #EAECF0',
  borderRadius: '6px',
  fontWeight: '500',
  padding: '2px 4px 2px 6px',
  justifyContent: 'center',
  alignItems: 'center',
  gap: '6px',
  background: '#FFF',
  color: '##344054',
  fontSize: '12px',
  fontStyle: 'normal',
  textAlign: 'center',
  minWidth: '56px',
  height: '22px',
}))

CustomDrawerChip.displayName = 'CustomDrawerChip'

export const CustomerStatusChip = styled(({ ...other }: ChipProps) => {
  const label: IStatus = other.label as IStatus
  const defaultStyles = {
    background: 'grey',
    color: 'white',
  }
  const chipStyles = CHIPCOLORS[label] || defaultStyles

  return (
    <Chip
      {...other}
      sx={{
        background: chipStyles.background,
        padding: '2px 6px 2px 8px',
        maxHeight: '20px',
        minWidth: '52px',
      }}
      label={
        <Stack
          sx={{
            gap: '6px',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}
        >
          {' '}
          <Stack
            sx={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              background: chipStyles.color,
            }}
          ></Stack>
          <Typography
            variant="label2"
            sx={{
              color: chipStyles.color,
              fontSize: '12px',
              fontStyle: 'normal',
              fontWeight: 500,
              lineHeight: '16px',
            }}
          >
            {other.label &&
              (other.label as string).charAt(0) +
                (other.label as string).slice(1).toLowerCase()}
          </Typography>
        </Stack>
      }
    />
  )
})(() => ({
  padding: 0,
  textAlign: 'center',
  fontSize: '12px',
  fontStyle: 'normal',
  fontWeight: 500,
  lineHeight: '16px',
}))

CustomerStatusChip.displayName = 'CustomerStatusChip'

export interface CustomerInfoChipProps {
  label: string
  requests: string[]
}

export const CustomerInfoChip = ({
  label,
  requests,
}: CustomerInfoChipProps) => {
  return (
    <Chip
      icon={
        <RestoreIcon sx={{ fontSize: '16px', color: '#555C61 !important' }} />
      }
      variant="outlined"
      sx={{
        color: '#555C61',
        backgroundColor: '#F8F9FC',
        border: '1px solid #D5D9EB',
        padding: '6px 6px 6px 8px',
        maxHeight: '30px',
        minWidth: '52px',
      }}
      label={
        <Stack
          sx={{
            gap: '6px',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}
        >
          <Typography
            variant="label2"
            sx={{
              fontSize: '14px',
              fontStyle: 'normal',
              fontWeight: 500,
              lineHeight: '16px',
              color: '#555C61',
            }}
          >
            {label &&
              (label as string).charAt(0) +
                (label as string).slice(1).toLowerCase()}
          </Typography>
          {requests.map((item, index) => (
            <Typography
              key={index}
              variant="label2"
              sx={{
                fontSize: '14px',
                fontStyle: 'normal',
                fontWeight: 500,
                lineHeight: '16px',
                textDecoration: 'underline',
                color: '#555C61',
              }}
            >
              {item}
            </Typography>
          ))}
        </Stack>
      }
    />
  )
}

export const CustomChip = styled(Chip)(() => {
  return {
    padding: '2px 6px',
    display: 'flex',
    alignItems: 'center',
    borderRadius: '6px',
    border: '1px solid #EAECF0',
    height: '20px',
    fontSize: '12px',
    fontWeight: 400,
    color: '#344054',
    backgroundColor: '#FFFFFF',
  }
})

CustomChip.displayName = 'CustomChip'
