/**
 * <AUTHOR> on 25/04/2025
 */
import { IDecodeToken } from '@dtbx/store/interfaces'
import { jwtDecode } from 'jwt-decode'
import { refreshUserToken } from '@/store/actions'

// Schedule token refresh 10 seconds before it expires
export const scheduleTokenRefresh = () => {
  const token = localStorage.getItem('accessToken')
  if (!token) {
    return
  }
  const decodedToken: IDecodeToken = jwtDecode(token)
  const expiresIn = decodedToken.exp * 1000 - Date.now() - 10000
  setTimeout(refreshUserToken, expiresIn)
}
