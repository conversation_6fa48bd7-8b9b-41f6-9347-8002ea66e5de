/**
 * <AUTHOR> on 16/12/2024
 */

import { refreshTokenIfNeeded, secureapi } from '@dtbx/store/utils'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { refreshUserToken } from '@/store/actions/eattaAuth'
import { AxiosInstance } from 'axios'

const client: AxiosInstance = secureapi

const isBackOffice = checkIfBackOffice()
if (!isBackOffice) {
  client.interceptors.request.clear()
  client.interceptors.request.use(async (config) => {
    const token = await refreshTokenIfNeeded(refreshUserToken)

    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  })
}

export const apiclient = client
