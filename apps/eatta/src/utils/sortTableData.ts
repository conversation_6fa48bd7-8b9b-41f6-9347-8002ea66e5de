export function sortData<T>(
  data: T[],
  orderBy: keyof T,
  order: 'asc' | 'desc'
): T[] {
  return [...data].sort((a, b) => {
    const aValue = a[orderBy]
    const bValue = b[orderBy]

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return order === 'asc' ? aValue - bValue : bValue - aValue
    }
    const dateA = new Date(aValue as any).getTime()
    const dateB = new Date(bValue as any).getTime()

    if (!isNaN(dateA) && !isNaN(dateB)) {
      return order === 'asc' ? dateA - dateB : dateB - dateA
    }
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return order === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue)
    }

    return 0
  })
}
