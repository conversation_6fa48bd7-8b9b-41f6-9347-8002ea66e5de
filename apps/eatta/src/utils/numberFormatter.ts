export const numberFormatter = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  })

  export const generateYears = (startYear = 2025): string[] => {
    const currentYear = new Date().getFullYear()
    const endYear = currentYear + 1
    const numYears = endYear - startYear + 1
    const years = new Array(numYears)
    for (let i = 0; i < numYears; i++) {
      years[i] = String(startYear + i)
    }
    return years
  }
