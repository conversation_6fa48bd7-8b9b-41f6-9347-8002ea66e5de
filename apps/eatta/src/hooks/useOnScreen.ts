import { useCallback, useEffect, useRef, useState } from 'react'

const useOnScreen = ({
  root = null,
  rootMargin = '0px',
  threshold = 0,
} = {}) => {
  const [isIntersecting, setIntersecting] = useState(false)
  const observerRef = useRef<IntersectionObserver | null>(null)

  const measureRef = useCallback(
    (node: HTMLElement | null) => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }

      if (node) {
        observerRef.current = new IntersectionObserver(
          ([entry]) => {
            setIntersecting(entry.isIntersecting)
          },
          { root, rootMargin, threshold }
        )

        observerRef.current.observe(node)
      }
    },
    [root, rootMargin, threshold]
  )

  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect()
      }
    }
  }, [])

  return { measureRef, isIntersecting, observer: observerRef.current }
}

export default useOnScreen
