/**
 * <AUTHOR> on 24/06/2025
 */

import { SetStateAction, Dispatch, useEffect, useRef } from 'react'
import { PageFilters } from '@/store/interfaces'

export function useResetPageOnFilterChange<T>(
  filters: T,
  paginationOptions: PageFilters,
  setPaginationOptions: Dispatch<SetStateAction<PageFilters>>
) {
  const resetPageRef = useRef(false)

  useEffect(() => {
    if (paginationOptions.page !== 1) {
      resetPageRef.current = true
      setPaginationOptions((prev) => ({ ...prev, page: 1 }))
    }
  }, [filters])

  return resetPageRef
}
