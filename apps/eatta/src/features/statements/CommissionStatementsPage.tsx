'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { Stack, Divider } from '@mui/material'
import { CatalogueFilters } from '@/store/interfaces'
import { MainPageHeader } from '@/components/MainPageHeader'
import { DEFAULT_FILTER_CONFIG, PageFilters } from '@/components/PageFilters'
import { getAuctionWeek } from '@dtbx/store/utils'
import { useAppDispatch } from '@/store'
import {
  downloadCatalogues,
  getBrokerCommissionStatements,
} from '@/store/actions'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { CommissionStatementsTable } from './CommissionStatementsTable'

export const CommissionStatementsPage = () => {
  const dispatch = useAppDispatch()
  const isBackOffice = checkIfBackOffice()
  const week = getAuctionWeek().toString()
  const [filters, setFilters] = useState<CatalogueFilters>({
    producer: '',
    amount: 0,
    lotNo: '',
    broker: '',
    invoiceNo: '',
    buyerName: '',
    totalPrice: 0,
    saleDate: week,
    dateCreated: '',
    invoiceStatus: '',
    buyer: '',
    buyerCode: '',
    factory: '',
    wareHouse: '',
    manufacturedDate: '',
    fromSaleDate: '',
    toSaleDate: '',
    page: 1,
    size: 10,
    year: new Date().getFullYear(),
  })

  const handleSearch = useCallback((newFilters: CatalogueFilters) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
      page: 1,
    }))
  }, [])

  const handlePageChange = useCallback((page: number, size: number) => {
    setFilters((prev) => ({
      ...prev,
      page,
      size,
    }))
  }, [])

  const handleExport = useCallback(() => {
    downloadCatalogues(dispatch, isBackOffice, filters)
  }, [dispatch, isBackOffice, filters])

  useEffect(() => {
    if (!filters.saleDate) return
    getBrokerCommissionStatements(dispatch, {
      ...filters,
    })
  }, [filters])

  return (
    <Stack sx={{ height: '100%' }}>
      <MainPageHeader />

      <Divider />

      <PageFilters
        title="Statement"
        subtitle="Here’s a record of commissions earned from the selected auction."
        onSearch={handleSearch}
        filters={filters}
        filterConfig={{
          ...DEFAULT_FILTER_CONFIG,
          showBrokerFilter: false,
          showStatus: false,
          showExport: true,
          showFactory: false,
          showSearchBox: true,
        }}
        searchByValues={[
          { filterLabel: 'Lot Number', filterKey: 'lotNo', type: 'numeric' },
          { filterLabel: 'Buyer', filterKey: 'buyer', type: 'string' },
          {
            filterLabel: 'Invoice Number',
            filterKey: 'invoiceNo',
            type: 'string',
          },
        ]}
        onExport={handleExport}
      />
      <CommissionStatementsTable
        filters={filters}
        onPageChange={handlePageChange}
      />
    </Stack>
  )
}
