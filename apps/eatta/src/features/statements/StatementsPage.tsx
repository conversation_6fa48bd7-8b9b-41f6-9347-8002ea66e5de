'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { Stack, Divider, Typography } from '@mui/material'
import { ProducerInvoiceEntryFilters } from '@/store/interfaces'
import { MainPageHeader } from '@/components/MainPageHeader'
import { DEFAULT_FILTER_CONFIG, PageFilters } from '@/components/PageFilters'
import { getAuctionWeek } from '@dtbx/store/utils'
import { useAppDispatch, useAppSelector } from '@/store'
import { downloadCatalogues, getProducerInvoiceEntries } from '@/store/actions'
import { Elipse } from '@/components/SvgIcons/Elipse'
import { StatementsTable } from './StatementsTable'
import { checkIfBackOffice } from '@/utils/appTypeChecker'

export const StatementsPage = () => {
  const dispatch = useAppDispatch()
  const isBackOffice = checkIfBackOffice()
  const { auctionScheduleResponse } = useAppSelector(
    (state) => state.salesSchedule
  )
  const { data } = auctionScheduleResponse
  const week = getAuctionWeek().toString()
  const [filters, setFilters] = useState<ProducerInvoiceEntryFilters>({
    producer: '',
    amount: 0,
    lotNo: '',
    broker: '',
    invoiceNo: '',
    buyerName: '',
    totalPrice: 0,
    saleDate: week,
    dateCreated: '',
    invoiceStatus: '',
    buyer: '',
    buyerCode: '',
    factory: '',
    wareHouse: '',
    manufacturedDate: '',
    fromSaleDate: '',
    toSaleDate: '',
    brokerInvoiceId: '',
    promptDate: '',
    page: 1,
    size: 10,
    year: new Date().getFullYear(),
  })

  const handleSearch = useCallback(
    (newFilters: ProducerInvoiceEntryFilters) => {
      setFilters((prev) => ({
        ...prev,
        ...newFilters,
        page: 1,
      }))
    },
    []
  )

  const handlePageChange = useCallback((page: number, size: number) => {
    setFilters((prev) => ({
      ...prev,
      page,
      size,
    }))
  }, [])

  const handleExport = useCallback(() => {
    downloadCatalogues(dispatch, isBackOffice, filters)
  }, [dispatch, isBackOffice, filters])

  const promptDate = data.find(
    (schedule) => schedule.saleCode === filters.saleDate
  )

  useEffect(() => {
    if (!filters.saleDate) return

    if (filters.broker && filters.factory) {
      getProducerInvoiceEntries(dispatch, {
        ...filters,
      })
    }
  }, [filters])

  return (
    <Stack sx={{ height: '100%' }}>
      <MainPageHeader />

      <Divider />

      <PageFilters
        title="Statement"
        subtitle="This is a view of expected income from all factories for the selected auction. Only shows amount settled and invoiced within DTB."
        onSearch={handleSearch}
        filters={filters}
        filterConfig={{
          ...DEFAULT_FILTER_CONFIG,
          showBrokerFilter: true,
          showStatus: false,
          showExport: true,
          showFactory: true,
          showSearchBox: false,
        }}
        onExport={handleExport}
      />
      <Stack
        direction="row"
        spacing={3}
        sx={{
          backgroundColor: '#f2f4f7',
          paddingInline: '1.5rem',
          paddingBlock: '1rem',
        }}
      >
        <Typography
          sx={{
            color: '#000A12',
            fontSize: '1rem',
            fontWeight: 500,
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
          }}
        >
          <Elipse /> {`Auction Number: ${filters.year}/${filters.saleDate}`}
        </Typography>
        <Typography
          sx={{
            color: '#000A12',
            fontSize: '1rem',
            fontWeight: 500,
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
          }}
        >
          <Elipse /> {`Prompt Date: ${promptDate?.promptDate || ''}`}
        </Typography>
      </Stack>
      <StatementsTable filters={filters} onPageChange={handlePageChange} />
    </Stack>
  )
}
