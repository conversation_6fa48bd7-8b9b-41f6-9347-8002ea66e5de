'use client'
import React, { FC, useEffect, useState } from 'react'
import { OnboardingStep } from '@/store/interfaces'
import { useRouter } from 'next/navigation'
import { Box, Divider, Stack, Typography } from '@mui/material'
import { BackButton } from '@dtbx/ui/components/Button'
import Grid from '@mui/material/Grid2'
import { CustomStepper } from '@/components/CustomStepper'
import UserDetailsVerification from '@/features/companies/users/create/UserDetailsVerification'
import Complete from '@/features/companies/users/create/Complete'
import { getApprovalById } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { setOnboardingCompany, setOnboardingUser } from '@/store/reducers'
import ApprovalSkeleton from '@/components/ApprovalSkeleton'

const STEPS: OnboardingStep[] = [
  {
    title: 'Checker Verification',
    description: 'User added successfully.Received activation email.',
  },
  {
    title: 'Complete',
    description:
      'User is successfully onboarded and receives an activation email.',
  },
]

export enum ApproveMemberSteps {
  CHECKER = 1,
  COMPLETE,
}

interface ApproveMemberCreationPageProps {
  approvalId: string
}

const ApproveMemberCreationPage: FC<ApproveMemberCreationPageProps> = ({
  approvalId,
}) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const [currentStep, setCurrentStep] = useState<ApproveMemberSteps>(1)

  const { onboardingUser } = useAppSelector((state) => state.companies)

  const getApprovals = async () => {
    await getApprovalById(dispatch, approvalId, (approvalRequest) => {
      dispatch(setOnboardingUser(approvalRequest))
    })
  }

  useEffect(() => {
    getApprovals()
  }, [approvalId])

  return (
    <Stack
      sx={{
        backgroundColor: '#F2F4F7',
        height: '100%',
      }}
    >
      <Stack
        sx={{ backgroundColor: '#FFFFFF' }}
        direction="row"
        paddingInline={3}
        paddingBlock={2}
        spacing={2}
        alignItems="flex-start"
      >
        <BackButton onClick={() => router.back()} />
        <Stack>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            Approve Member
          </Typography>
          <Typography variant="body1" sx={{ color: '#475467' }}>
            Once approved, they will be able to log in
          </Typography>
        </Stack>
      </Stack>

      <Divider />

      <Box sx={{ flexGrow: 1, height: '100%', overflow: 'hidden' }}>
        <Grid
          container
          spacing={2}
          sx={{ height: '100%', overflowY: { sm: 'auto', md: 'hidden' } }}
        >
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 4, lg: 3 }}
            sx={{ height: { md: '100%' }, overflowY: 'auto' }}
          >
            <CustomStepper
              steps={STEPS}
              currentStep={currentStep}
              setStep={(step: ApproveMemberSteps) => setCurrentStep(step)}
            />
          </Grid>
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 8, lg: 9 }}
            sx={{ height: { md: '100%' }, overflowY: { md: 'auto' } }}
          >
            <Box width={{ sm: '100%', md: '80%', lg: '60%' }}>
              {!onboardingUser ? (
                <ApprovalSkeleton />
              ) : (
                (() => {
                  switch (currentStep) {
                    case ApproveMemberSteps.CHECKER:
                      return (
                        <UserDetailsVerification
                          actionType={'CHECKER'}
                          setStep={setCurrentStep}
                        />
                      )
                    case ApproveMemberSteps.COMPLETE:
                      return <Complete />
                  }
                })()
              )}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Stack>
  )
}

export default ApproveMemberCreationPage
