'use client'
import {
  Button,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'

import React, { useEffect, useState } from 'react'

import { useCustomRouter } from '@dtbx/ui/hooks'
import { Order, RemoveInvoiceEntryPayload } from '@/store/interfaces'
import { CustomTableHeader } from '@dtbx/ui/components/Table'
import InvoiceDetails from './InvoiceDetails'
import { ExportButton } from '@/components/ExportButton'
import { BackButton } from '@dtbx/ui/components/Button'
import { AccessWrapper } from '@/components/AccessHelper'
import { RemoveIcon } from '@/components/SvgIcons/RemoveIcon'
import { downloadEslip, getInvoiceEslipsById, removeInvoiceEslipEntry } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { CheckoutSkeleton } from '@/features/checkout/CheckoutSkeleton'
import CopyButton from '@/components/CopyButton'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { STATUS_COLOR } from '@/utils/statusChips'

interface CheckoutPageProps {
  eslipId: string
}

const TABLE_HEADERS = [
  { id: 'lotNumber', label: 'Lot No' },
  { id: 'gdnInvoice', label: 'Garden Invoice' },
  { id: 'warehouse', label: 'Warehouse' },
  { id: 'broker', label: 'Broker' },
  { id: 'grade', label: 'Grade' },
  { id: 'netWeight', label: 'Net Weight' },
  { id: 'finalPrice', label: 'Final Price' },
  { id: 'value', label: 'Value' },
  { id: 'brokerCommission', label: 'Broker 0.5%' },
  { id: 'withholdingTax', label: '5% Withholding Tax' },
  { id: 'amount', label: 'Amount to pay' },
  { id: 'actions', label: 'Remove' },
]

export const CheckoutPage: React.FC<CheckoutPageProps> = ({ eslipId }) => {
  const { invoiceEslip, isLoading ,isDownloadingEslip} = useAppSelector((state) => state.invoices)
  const router = useCustomRouter()
  const dispatch = useAppDispatch()

  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('datecreated')

  const handleRemove = (entryId: string, invoiceNumber: string) => {
    const payload: RemoveInvoiceEntryPayload = {
      entryIds: [entryId],
      invoiceNumber: invoiceNumber,
    }
    removeInvoiceEslipEntry(dispatch, payload)
  }

  const handleSave = () => {
    router.back()
  }

  const handleDownloadEslip =  async () => {
    await downloadEslip(dispatch, eslipId)
  }

  useEffect(() => {
    const fetchEslip = async () => {
      getInvoiceEslipsById(dispatch, eslipId)
    }
    fetchEslip()
  }, [eslipId])

  return (
    <>
      {isLoading || !invoiceEslip ? (
        <CheckoutSkeleton />
      ) : (
        <Stack
          direction="column"
          sx={{
            height: '100%',
          }}
        >
          <Stack>
            <Stack
              direction="row"
              alignItems="center"
              sx={{
                width: '100%',
                px: 3,
                py: 2,
                bgcolor: '#FFFFFF',
                borderBottom: '1px solid #EAECF0',
              }}
            >
              <BackButton onClick={() => router.back()} />
              <Typography
                sx={{
                  width: '100%',
                  textAlign: 'left',
                  padding: '1rem',
                  color: '#000A12',
                  fontSize: '1.25rem',
                  fontWeight: 700,
                }}
              >
                Checkout: Eslip No. {invoiceEslip.invoiceNumber}
                <CopyButton value={invoiceEslip.invoiceNumber} />
                <StatusChip
                  label={invoiceEslip.status}
                  status={STATUS_COLOR[invoiceEslip.status]}
                  sx={{
                    width: '5rem',
                    borderRadius: '1rem',
                    fontSize: '0.75rem',
                  }}
                />
              </Typography>
              <Stack direction="row" spacing={2}>
                <AccessWrapper clientTypes={['Buyer']} backofficeAccess={false}>
                  <ExportButton
                    onClick={handleDownloadEslip}
                    ButtonText={'Download E-Slip'}
                    disabled={false}
                    isLoading={isDownloadingEslip}
                  />

                  {invoiceEslip.status !== 'PAID' && (
                    <Button
                      variant="contained"
                      disabled={isLoading}
                      onClick={handleSave}
                      sx={{
                        backgroundColor: 'rgba(245, 158, 11, 0.93)',
                        color: 'FFFFF',
                        border: '1px solid #D0D5DD',
                        borderRadius: '4px',
                        textWrap: 'noWrap',
                      }}
                    >
                      Save
                    </Button>
                  )}
                </AccessWrapper>
              </Stack>
            </Stack>
            <Stack sx={{ minWidth: '200px', backgroundColor: '#f2f4f7' }}>
              <Typography
                sx={{
                  width: '100%',
                  textAlign: 'left',
                  padding: '1rem',
                }}
              >
                {`Showing ${invoiceEslip.invoiceEntries.length} lots in cart`}
              </Typography>
            </Stack>
          </Stack>

          <Stack sx={{ flexGrow: 1, overflow: 'auto', marginBottom: 2 }}>
            <TableContainer
              component={Paper}
              sx={{
                boxShadow: 'none',
                '& .MuiTableCell-root': {
                  paddingInline: '1.5rem',
                  paddingBlock: '0.5rem',
                  textAlign: 'left',
                },
              }}
            >
              <Table
                sx={{ minWidth: 650 }}
                aria-label="designations table"
                size="small"
              >
                <CustomTableHeader
                  order={order}
                  orderBy={orderBy}
                  headLabel={
                    invoiceEslip.status === 'NEW' &&
                    invoiceEslip.invoiceEntries.length > 1
                      ? TABLE_HEADERS
                      : TABLE_HEADERS.filter(
                          (header) => header.id !== 'actions'
                        )
                  }
                  showCheckbox={false}
                  rowCount={invoiceEslip.invoiceEntries.length}
                  numSelected={0}
                />
                <TableBody>
                  {invoiceEslip.invoiceEntries.map((entry) => {
                    const {
                      lotNumber,
                      grade,
                      netWeight,
                      totalValue,
                      brokerShare,
                      netAmount,
                      withholdingTax,
                      pricePerKg,
                      gardenInvoice,
                      warehouse,
                      broker,
                      mark,
                    } = entry

                    return (
                      <TableRow hover key={`${entry.id}`} tabIndex={-1}>
                        <TableCell component="th" scope="row">
                          {lotNumber}
                        </TableCell>
                        <TableCell>
                          <Typography variant="body1">
                            {gardenInvoice}
                          </Typography>
                          <Typography variant="body2">{mark}</Typography>
                        </TableCell>
                        <TableCell>{warehouse}</TableCell>
                        <TableCell>{broker}</TableCell>
                        <TableCell>{grade}</TableCell>
                        <TableCell>{netWeight}</TableCell>
                        <TableCell>
                          ${new Intl.NumberFormat('en-US').format(pricePerKg)}
                        </TableCell>
                        <TableCell>
                          ${new Intl.NumberFormat('en-US').format(totalValue)}
                        </TableCell>
                        <TableCell>
                          ${new Intl.NumberFormat('en-US').format(brokerShare)}
                        </TableCell>
                        <TableCell>
                          ${new Intl.NumberFormat('en-US').format(withholdingTax)}
                        </TableCell>
                        <TableCell>
                          <Typography
                            variant="body1"
                            sx={{ fontWeight: 600, color: '#1A202C' }}
                          >
                            ${new Intl.NumberFormat('en-US').format(netAmount)}
                          </Typography>
                        </TableCell>
                        {invoiceEslip.status === 'NEW' &&
                          invoiceEslip.invoiceEntries.length > 1 && (
                            <TableCell>
                              <IconButton
                                onClick={() =>
                                  handleRemove(
                                    entry.id,
                                    invoiceEslip.invoiceNumber
                                  )
                                }
                              >
                                <RemoveIcon />
                              </IconButton>
                            </TableCell>
                          )}
                      </TableRow>
                    )
                  })}
                </TableBody>
                <TableFooter />
              </Table>
            </TableContainer>
          </Stack>

          <InvoiceDetails invoice={invoiceEslip} accountNumber={'**********'} />
        </Stack>
      )}
    </>
  )
}
