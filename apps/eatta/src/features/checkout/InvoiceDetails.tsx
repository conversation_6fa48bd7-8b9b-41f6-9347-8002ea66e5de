'use client'

import React from 'react'
import { Box, Divider, Stack, Typography } from '@mui/material'
import { InvoiceEslip } from '@/store/interfaces'
import { formatCurrency } from '@dtbx/store/utils'
import CopyButton from '@/components/CopyButton'

export interface InvoiceDetailsProps {
  invoice: InvoiceEslip
  accountNumber: string
}

const InvoiceDetails: React.FC<InvoiceDetailsProps> = ({
  invoice,
  accountNumber,
}) => {
  const {
    invoiceNumber,
    totalAmountToPay,
    totalWithholdingTax,
    totalBrokerCommission,
    totalTeaValue,
  } = invoice
  return (
    <Stack
      direction="column"
      spacing={2}
      sx={{
        width: '100%',
        bgcolor: '#f5f5f5',
        paddingBottom: 10,
        paddingTop: 2,
        paddingInline: 3,
        background: 'white',
        borderTop: '1px solid #EAECF0',
      }}
    >
      <Stack
        direction={{ xs: 'column', sm: 'row' }}
        spacing={2}
        mb={{ xs: 2, sm: 3 }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.5,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#000A12',
              fontWeight: 600,
              fontSize: '0.75rem',
            }}
          >
            Total Tea Value
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
          >
            {formatCurrency(totalTeaValue, 'USD', 'en-US')}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.5,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#344054',
              fontWeight: 600,
              fontSize: '0.75rem',
            }}
          >
            Total Broker Commission (0.5%)
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
          >
            {formatCurrency(totalBrokerCommission, 'USD', 'en-US')}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.5,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#344054',
              fontWeight: 600,
              fontSize: '0.75rem',
            }}
          >
            Total Withholding Tax (5% of Broker Commission)
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
          >
            {formatCurrency(totalWithholdingTax, 'USD', 'en-US')}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.5,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#344054',
              fontWeight: 600,
              fontSize: '0.75rem',
            }}
          >
            Total Amount To Pay (Including Broker %)
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
          >
            {formatCurrency(totalAmountToPay, 'USD', 'en-US')}
          </Typography>
        </Box>
      </Stack>

      <Divider />

      <Typography
        variant="body2"
        sx={{ mt: 2, color: '#475467', fontSize: '1rem', fontWeight: 500 }}
      >
        Please pay the above amount into{' '}
        <b>
          EATTA'S DTB Collections account no.{accountNumber || ''}{' '}
          <CopyButton value={accountNumber} />
        </b>
        . Remember to add your{' '}
        <b>
          E-Slip Number: {invoiceNumber || ''}
          <CopyButton value={invoiceNumber} />
        </b>
        , to your narration/description when making the payment through RTGS or
        SWIFT.
      </Typography>
    </Stack>
  )
}

export default InvoiceDetails
