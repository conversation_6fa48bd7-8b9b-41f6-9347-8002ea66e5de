/**
 * <AUTHOR> on 09/05/2025
 */
import { Box, Divider, Skeleton, Stack } from '@mui/material'
import React from 'react'
import { BackButton } from '@dtbx/ui/components/Button'
import { useCustomRouter } from '@dtbx/ui/hooks'
import TableSkeleton from '@/components/TableSkeleton'

export const CheckoutSkeleton = () => {
  const router = useCustomRouter()
  return (
    <Stack
      direction="column"
      sx={{
        height: '100%',
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="flex-start"
        spacing={2}
        sx={{
          width: '100%',
          px: 3,
          py: 2,
          bgcolor: '#FFFFFF',
          borderBottom: '1px solid #EAECF0',
        }}
      >
        <BackButton onClick={() => router.back()} />
        <Skeleton sx={{ width: '356px', height: '56px' }} />
      </Stack>

      <Stack paddingInline={3} paddingBlock={2}>
        <Skeleton sx={{ width: '200px', height: '56px' }} />
      </Stack>

      <Stack sx={{ flexGrow: 1, overflow: 'auto', marginBottom: 2 }}>
      <TableSkeleton rowCount={10} columnCount={11} />
      </Stack>

      <Stack
        direction="column"
        spacing={1}
        sx={{
          width: '100%',
          bgcolor: '#f5f5f5',
          paddingBottom: 10,
          paddingTop: 2,
          paddingInline: 3,
          background: 'white',
        }}
      >
        <Stack
          direction={{ xs: 'column', sm: 'row' }}
          spacing={2}
          mb={{ xs: 2, sm: 3 }}
        >
          {[1, 2, 4, 5].map((item) => (
            <Box
              key={item}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                flex: 0.5,
              }}
            >
              <Skeleton sx={{ height: '20px', width: '280px' }} />
              <Skeleton sx={{ height: '36px', width: '150px' }} />
            </Box>
          ))}
        </Stack>

        <Divider />

        <Skeleton />
        <Skeleton sx={{ width: '50%' }} />
      </Stack>
    </Stack>
  )
}
