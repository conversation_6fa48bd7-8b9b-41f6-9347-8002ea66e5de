'use client'
import React, { useCallback, useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import {
  getBrokerCommissions,
  getPendingRequests,
} from '@/store/actions/brokerCommission'
import { ApprovalRequestFilters } from '@/store/interfaces/makerChecker'
import TableSkeleton from '@/components/TableSkeleton'
import CommissionTable from './CommissionTable'
import { EATTA_MODULES } from '@/utils/constants'

export interface CommissionsTableProps {
  selectedTab: number
}

const CommissionsTable: React.FC<CommissionsTableProps> = ({ selectedTab }) => {
  const dispatch = useAppDispatch()
  const { commissionResponse, isLoadingCommissions, pendingRequestsResponse } =
    useAppSelector((state) => state.brokerCommissions)

  const [filters, setFilters] = useState<ApprovalRequestFilters>({
    channel: '',
    status: '',
    module: '',
    createDateFrom: '',
    createDateTo: '',
    requestType: '',
    page: 1,
    size: 10,
    makerFirstName: '',
    makerLastName: '',
  })

  const handlePageChange = useCallback((page: number, size: number) => {
    setFilters((prev) => ({
      ...prev,
      page,
      size,
    }))
  }, [])

  // Handle broker commissions
  useEffect(() => {
    if (selectedTab === 0) {
      getBrokerCommissions(dispatch, { ...filters, page: 1, size: 10 })
    }
  }, [filters, selectedTab, dispatch])

  // Handle Pending Approvals
  useEffect(() => {
    if (selectedTab === 1) {
      getPendingRequests(dispatch, {
        ...filters,
        page: 1,
        size: 10,
        status: 'PENDING',
        channel: 'EATTA',
        module: EATTA_MODULES.commissions,
      })
    }
  }, [filters, selectedTab])

  const tableData =
    selectedTab === 0 ? commissionResponse : pendingRequestsResponse

  return isLoadingCommissions ? (
    <TableSkeleton rowCount={4} columnCount={9} />
  ) : (
    <>
      {tableData.data.length === 0 ? (
        <EmptyPage
          title="No configurations found"
          message="Please create a new configuration to get started"
        />
      ) : (
        <CommissionTable
          filters={filters}
          onPageChange={handlePageChange}
          data={tableData}
          isCurrentTab={selectedTab === 0}
        />
      )}
    </>
  )
}

export default CommissionsTable
