import { useAppDispatch, useAppSelector } from '@/store'
import {
  approveBrokerCommissions,
  createBrokerCommissionsMake,
} from '@/store/actions/brokerCommission'
import { BrokerCommissionRequest } from '@/store/interfaces/brokerCommissions'
import { IApprovalRequest } from '@/store/interfaces/makerChecker'
import { StatusChip } from '@dtbx/ui/components/Chip'
import CloseIcon from '@mui/icons-material/Close'
import {
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import { JSX, useMemo, useState } from 'react'
import * as Yup from 'yup'
import MakerCheckerActivities from '../../../components/MakerCheckerActivities'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { CommentDialog } from '@/components/CommentDialog'

export interface ApprovalRequestDetails extends IApprovalRequest {
  requestType: string
  createdAt: string
  createdBy: string
}

export interface FormValues {
  buyerCommission: string
  commissionType: string
  producerCommission: string
  centralBankRate: string
  penalty: string
  withHoldingTax: string
  comments: string
}

export type BrokerCommissionFormProps = {
  open: boolean
  handleClose: () => void
  actionType: 'MAKER' | 'CHECKER'
  initialValues?: BrokerCommissionRequest
  requestDetails?: ApprovalRequestDetails
}

const defaultInitialValues: FormValues = {
  buyerCommission: '',
  commissionType: 'PERCENTAGE',
  producerCommission: '',
  centralBankRate: '',
  penalty: '',
  withHoldingTax: '',
  comments: '',
}

const validationSchema = Yup.object({
  buyerCommission: Yup.number()
    .required('Buyer Commission value should not be empty')
    .max(100, 'Commission should not be more than 100%')
    .min(0, 'Commission should not be less than 0%'),
  producerCommission: Yup.number()
    .required('Producer Commission should not be empty')
    .max(100, 'Commission should not be more than 100%')
    .min(0, 'Commission should not be less than 0%'),
  centralBankRate: Yup.number()
    .required('CBR Rate should not be empty')
    .max(100, 'CBR Rate should not be more than 100%')
    .min(0, 'CBR Rate should not be less than 0%'),
  penalty: Yup.number()
    .required('Penalty should not be empty')
    .min(0, 'Penalty cannot be negative')
    .max(100, 'Penalty should not be more than 100%'),
  withHoldingTax: Yup.number()
    .required('WithHolding Tax should not be empty')
    .min(0, 'WithHolding Tax cannot be negative')
    .max(100, 'WithHolding Tax should not be more than 100%'),
})

const BrokerCommissionForm = ({
  open,
  handleClose,
  actionType,
  initialValues,
  requestDetails,
}: BrokerCommissionFormProps): JSX.Element => {
  const dispatch = useAppDispatch()
  const selectedApprovalRequests = useAppSelector(
    (state) => state.brokerCommissions.selectedApprovalRequests
  )
  const [openCommentDialog, setOpenCommentsDialog] = useState(false)
  const isCheckerView = actionType === 'CHECKER'
  const isMakerView = actionType === 'MAKER'

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_COMMISSION),
    []
  )

  const onSuccess = () => {
    formik.resetForm()
    handleClose()
  }

  const handleOnSubmitCommentsDialog = async (comments: string) => {
    setOpenCommentsDialog(false)

    if (!requestDetails) return
    await approveBrokerCommissions(
      dispatch,
      comments,
      onSuccess,
      requestDetails.id
    )
  }

  const formInitialValues: FormValues = initialValues
    ? {
        buyerCommission: initialValues.buyerCommission.toString(),
        commissionType: initialValues.commissionType,
        producerCommission: initialValues.producerCommission.toString(),
        centralBankRate: initialValues.centralBankRate.toString(),
        penalty: initialValues.penalty.toString(),
        withHoldingTax: initialValues.withHoldingTax.toString(),
        comments: initialValues.comments || '',
      }
    : defaultInitialValues

  const formik = useFormik<FormValues>({
    initialValues: formInitialValues,
    validateOnMount: true,
    validationSchema: isCheckerView ? null : validationSchema,
    enableReinitialize: true,

    onSubmit: async (values) => {
      const data: BrokerCommissionRequest = {
        buyerCommission: parseFloat(values.buyerCommission) || 0,
        commissionType: 'PERCENTAGE',
        producerCommission: parseFloat(values.producerCommission) || 0,
        centralBankRate: parseFloat(values.centralBankRate) || 0,
        penalty: parseFloat(values.penalty) || 0,
        withHoldingTax: parseFloat(values.withHoldingTax) || 0,
        comments: values.comments || '',
      }
      if (isMakerView) {
        await createBrokerCommissionsMake(isSuper, dispatch, data, onSuccess)
      } else if (HasAccessToRights(ACCESS_CONTROLS.ACCEPT_COMMISSION)) {
        setOpenCommentsDialog(true)
      }
    },
  })

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ fontWeight: 600 }}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            spacing={1}
          >
            <Typography variant="subtitle1" fontWeight="bold">
              Configure Rates and Commissions
            </Typography>
            {isCheckerView && (
              <StatusChip
                label="Pending Checker Approval"
                sx={{
                  color: '#000000 !important',
                  backgroundColor: '#FFFFFF !important',
                  border: '1px solid #CCCCCC !important',
                }}
              />
            )}
          </Stack>
          <IconButton
            sx={{
              height: '2rem',
              width: '2rem',
              border: '1px solid #D0D5DD',
              borderRadius: '0.5rem',
            }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack>
              <Typography sx={{ fontWeight: 500, color: '#000000' }}>
                All figures should be set to match values in the EATTA Rulebook.
                Please enter values as flat figures. E.g if a broker commission
                is 0.05%, enter 0.05
              </Typography>
            </Stack>

            <Stack>
              <Typography
                sx={{
                  fontSize: '17px',
                  fontWeight: 600,
                  color: '#000000',
                  marginBottom: '1rem',
                  marginTop: '1rem',
                }}
              >
                Broker-Buyer Commission
              </Typography>
              <TextField
                hiddenLabel
                placeholder="Value 0 to 100"
                variant="outlined"
                size="small"
                disabled={isCheckerView}
                {...(isCheckerView && selectedApprovalRequests
                  ? { value: selectedApprovalRequests.buyerCommission }
                  : formik.getFieldProps('buyerCommission'))}
                slotProps={{
                  htmlInput: {
                    minLength: 1,
                    maxLength: 4,
                  },
                }}
                error={
                  formik.touched.buyerCommission &&
                  Boolean(formik.errors.buyerCommission)
                }
                helperText={
                  formik.touched.buyerCommission &&
                  formik.errors.buyerCommission
                }
              />
            </Stack>

            <Stack>
              <Typography
                sx={{
                  fontSize: '17px',
                  fontWeight: 600,
                  color: '#000000',
                  marginBottom: '1rem',
                  marginTop: '1rem',
                }}
              >
                Broker-Producer Commission
              </Typography>
              <TextField
                hiddenLabel
                placeholder="Value 0 to 100"
                variant="outlined"
                size="small"
                disabled={isCheckerView}
                {...(isCheckerView && selectedApprovalRequests
                  ? { value: selectedApprovalRequests.producerCommission }
                  : formik.getFieldProps('producerCommission'))}
                slotProps={{
                  htmlInput: {
                    minLength: 1,
                    maxLength: 4,
                  },
                }}
                error={
                  formik.touched.producerCommission &&
                  Boolean(formik.errors.producerCommission)
                }
                helperText={
                  formik.touched.producerCommission &&
                  formik.errors.producerCommission
                }
              />
            </Stack>
            <Stack>
              <Typography
                sx={{
                  fontSize: '17px',
                  fontWeight: 600,
                  color: '#000000',
                  marginBottom: '1rem',
                  marginTop: '1rem',
                }}
              >
                Central Bank Rate
              </Typography>
              <TextField
                hiddenLabel
                placeholder="Value 0 to 100"
                variant="outlined"
                size="small"
                disabled={isCheckerView}
                {...(isCheckerView && selectedApprovalRequests
                  ? { value: selectedApprovalRequests.centralBankRate }
                  : formik.getFieldProps('centralBankRate'))}
                slotProps={{
                  htmlInput: {
                    minLength: 1,
                    maxLength: 4,
                  },
                }}
                error={
                  formik.touched.centralBankRate &&
                  Boolean(formik.errors.centralBankRate)
                }
                helperText={
                  formik.touched.centralBankRate &&
                  formik.errors.centralBankRate
                }
              />
            </Stack>
            <Stack>
              <Typography
                sx={{
                  fontSize: '17px',
                  fontWeight: 600,
                  color: '#000000',
                  marginBottom: '1rem',
                  marginTop: '1rem',
                }}
              >
                Penalty
              </Typography>
              <TextField
                hiddenLabel
                placeholder="Value 0 to 100"
                variant="outlined"
                size="small"
                disabled={isCheckerView}
                {...(isCheckerView && selectedApprovalRequests
                  ? { value: selectedApprovalRequests.penalty }
                  : formik.getFieldProps('penalty'))}
                slotProps={{
                  htmlInput: {
                    minLength: 1,
                    maxLength: 4,
                  },
                }}
                error={formik.touched.penalty && Boolean(formik.errors.penalty)}
                helperText={formik.touched.penalty && formik.errors.penalty}
              />
            </Stack>
            <Stack>
              <Typography
                sx={{
                  fontSize: '17px',
                  fontWeight: 600,
                  color: '#000000',
                  marginBottom: '1rem',
                  marginTop: '1rem',
                }}
              >
                WithHolding Tax
              </Typography>
              <TextField
                hiddenLabel
                placeholder="Value 0 to 100"
                variant="outlined"
                size="small"
                disabled={isCheckerView}
                {...(isCheckerView && selectedApprovalRequests
                  ? { value: selectedApprovalRequests.withHoldingTax || '0' }
                  : formik.getFieldProps('withHoldingTax'))}
                slotProps={{
                  htmlInput: {
                    minLength: 1,
                    maxLength: 4,
                  },
                }}
                error={
                  formik.touched.withHoldingTax &&
                  Boolean(formik.errors.withHoldingTax)
                }
                helperText={
                  formik.touched.withHoldingTax && formik.errors.withHoldingTax
                }
              />
            </Stack>

            <Stack sx={{ marginTop: '1rem' }}>
              {isCheckerView && (
                <MakerCheckerActivities
                  activities={[
                    {
                      action: 'New Rates created by',
                      email: requestDetails?.createdBy || '<EMAIL>',
                      actionedBy: requestDetails?.createdBy || '',
                      actionedDate: requestDetails?.createdAt || '',
                    },
                  ]}
                />
              )}
              <AccessControlWrapper
                rights={
                  isMakerView
                    ? ACCESS_CONTROLS.CREATE_COMMISSION
                    : ACCESS_CONTROLS.ACCEPT_COMMISSION
                }
                makerId={isCheckerView ? undefined : requestDetails?.maker}
              >
                <Button
                  disabled={!isCheckerView && !formik.isValid}
                  variant="contained"
                  color="primary"
                  type="submit"
                  sx={{
                    marginTop: '38px',
                  }}
                >
                  {isCheckerView ? 'Approve New Rates & Commissions' : 'Submit'}
                </Button>
              </AccessControlWrapper>

              {openCommentDialog && (
                <CommentDialog
                  open={openCommentDialog}
                  onClose={() => setOpenCommentsDialog(false)}
                  onSubmit={handleOnSubmitCommentsDialog}
                />
              )}
            </Stack>
          </Form>
        </FormikProvider>
      </DialogContent>
    </Dialog>
  )
}

export default BrokerCommissionForm
