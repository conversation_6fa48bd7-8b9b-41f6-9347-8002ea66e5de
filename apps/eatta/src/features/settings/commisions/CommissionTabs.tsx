import React, { FC } from 'react'
import { <PERSON>, Divider, Stack } from '@mui/material'
import { AntTab, AntTabs } from '@dtbx/ui/components/Tabs'
import { BrokerCommissionStatus } from '@/store/interfaces'
import { useAppSelector } from '@/store'
import CommissionsTable from './ListCommissions'
import { CHIP_COLORS } from '@/utils/statusChips'
import { ConfigureCommissionHeader } from './ConfigureCommissionHeader'

export type CommissionTabType = {
  title: string
  status: (typeof CHIP_COLORS)[keyof typeof CHIP_COLORS]
  commissionStatus?: BrokerCommissionStatus
  itemCounts?: number
  canSelect: boolean
}

export interface CommissionTabsProps {
  tabs: CommissionTabType[]
  selectedTab: number
  onTabSelected: (index: number) => void
}

export const CommissionTabs: FC<CommissionTabsProps> = ({
  tabs,
  selectedTab,
  onTabSelected,
}) => {
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    onTabSelected(newValue)
  }

  const { isLoadingCommissions } = useAppSelector(
    (state) => state.brokerCommissions
  )

  return (
    <Stack sx={{ backgroundColor: '#F2F4F7' }}>
      <AntTabs
        value={selectedTab}
        onChange={handleTabChange}
        aria-label="invoice tabs"
        sx={{
          '& .MuiTabs-flexContainer': {
            gap: '0.5rem',
          },
        }}
      >
        {tabs.map((tab, index) => (
          <AntTab
            key={tab.title}
            value={index}
            label={
              <Stack direction="row" alignItems="center" spacing={1}>
                <span
                  style={{
                    color:
                      selectedTab === index
                        ? CHIP_COLORS.success.label
                        : 'black',
                    fontWeight: selectedTab === index ? 700 : 600,
                  }}
                >
                  {tab.title}
                </span>

                {tab.itemCounts !== undefined &&
                  tab.itemCounts > 0 &&
                  !isLoadingCommissions && (
                    <Chip
                      label={tab.itemCounts}
                      sx={{
                        '& .MuiChip-label': {
                          px: 0.7,
                          py: 0,
                        },
                        height: 'auto',
                        fontSize: '0.75rem',
                        color: tab.status.label,
                        fontWeight: 500,
                        backgroundColor: tab.status.bg,
                        border: `1px solid ${tab.status.border}`,
                      }}
                    />
                  )}
              </Stack>
            }
          />
        ))}
      </AntTabs>

      <Divider sx={{ width: '98%', margin: '0 auto' }} />

      <ConfigureCommissionHeader />

      <Stack>
        {tabs.map((tab, index) => (
          <div key={tab.title} hidden={selectedTab !== index}>
            <CommissionsTable selectedTab={index} />
          </div>
        ))}
      </Stack>
    </Stack>
  )
}
