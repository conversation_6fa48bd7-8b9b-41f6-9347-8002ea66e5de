import { Button, Typography, Stack } from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import BrokerCommissionForm from './CommissionDialog'
import { useState } from 'react'

export const ConfigureCommissionHeader = () => {
  const [openModal, setOpenModal] = useState<boolean>(false)

  return (
    <Stack
      spacing={1}
      paddingInline={3}
      paddingBlock={2}
      marginTop={1}
      sx={{
        backgroundColor: '#FFFFFF',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <Typography sx={{ fontSize: '20px', fontWeight: 600, color: '#000000' }}>
        Configure Commissions
      </Typography>
      <Button
        variant="contained"
        color="primary"
        startIcon={<AddIcon />}
        sx={{ borderRadius: '0.5rem', textWrap: 'nowrap' }}
        onClick={() => setOpenModal(true)}
      >
        Set Up New Rates
      </Button>
      <BrokerCommissionForm
        open={openModal}
        handleClose={() => setOpenModal(false)}
        actionType="MAKER"
      />
    </Stack>
  )
}
