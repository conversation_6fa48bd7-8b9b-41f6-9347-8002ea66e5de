'use client'
import { But<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ack, Typography } from '@mui/material'
import React, { useState } from 'react'
import { ConfigureIcon } from '@dtbx/ui/components/SvgIcons'
import WeeklySalesTable from './weekly-schedule/WeeklySalesTable'
import { CalendarIcon } from '@mui/x-date-pickers'
import { CommissionTabs } from './commisions/CommissionTabs'
import { CHIP_COLORS } from '@/utils/statusChips'
import { BrokerCommissionStatus } from '@/store/interfaces'

const SettingToggleButton = ({
  text,
  isActive,
  handleToggle,
  icon,
}: {
  text: string
  isActive: boolean
  handleToggle: () => void
  icon: React.ReactNode
}) => {
  return (
    <Button
      variant="contained"
      color="primary"
      startIcon={icon}
      onClick={handleToggle}
      sx={{
        px: 1.5,
        textWrap: 'nowrap',
        backgroundColor: isActive ? '#00BC2D' : 'white',
        color: isActive ? 'white' : 'black',
        border: isActive ? '1px solid #00BC2D' : '1px solid #ccc',
        '&:hover': {
          backgroundColor: isActive ? '#00A625' : '#f5f5f5',
        },
      }}
    >
      {text}
    </Button>
  )
}
export interface CommissionTabsProps {
  tabs: CommissionTabType[]
  selectedTab: number
  onTabSelected: (index: number) => void
}

export type CommissionTabType = {
  title: string
  status: (typeof CHIP_COLORS)[keyof typeof CHIP_COLORS]
  commissionStatus?: BrokerCommissionStatus
  itemCounts?: number
  canSelect: boolean
}

const COMMISSION_TABS: CommissionTabType[] = [
  {
    title: 'Current',
    status: CHIP_COLORS.success,
    commissionStatus: 'Active',
    itemCounts: 0,
    canSelect: true,
  },
  {
    title: 'Pending Approval',
    status: CHIP_COLORS.success,
    commissionStatus: 'PENDING',
    itemCounts: 0,
    canSelect: false,
  },
]

export default function SettingConfigs() {
  const [activeView, setActiveView] = useState<'commisions' | 'schedule'>(
    'commisions'
  )
  const [tabs, setTabs] = React.useState<CommissionTabType[]>(COMMISSION_TABS)
  const [selectedTab, setSelectedTab] = useState(0)
  const handleTabSelected = (index: number) => {
    setSelectedTab(index)
  }
  const handleToggle = (view: 'commisions' | 'schedule') => {
    setActiveView(view)
  }

  const isActive = (view: 'commisions' | 'schedule') => activeView === view

  return (
    <Stack>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: '600',
            }}
          >
            Settings
          </Typography>
        </Stack>
      </Stack>

      <Divider />

      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <SettingToggleButton
            text="Configure Commissions"
            isActive={isActive('commisions')}
            handleToggle={() => handleToggle('commisions')}
            icon={
              <ConfigureIcon
                stroke={isActive('commisions') ? 'white' : 'black'}
              />
            }
          />

          <SettingToggleButton
            text="Schedule of Weekly Sales"
            isActive={isActive('schedule')}
            handleToggle={() => handleToggle('schedule')}
            icon={
              <CalendarIcon stroke={isActive('schedule') ? 'white' : 'black'} />
            }
          />
        </Stack>
      </Stack>
      <Divider />

      {activeView === 'commisions' && (
        <CommissionTabs
          tabs={tabs}
          selectedTab={selectedTab}
          onTabSelected={handleTabSelected}
        />
      )}
      {activeView === 'schedule' && <WeeklySalesTable />}
    </Stack>
  )
}
