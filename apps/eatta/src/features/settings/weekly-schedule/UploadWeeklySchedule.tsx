import React, { useState } from 'react'
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Stack,
  Typography,
} from '@mui/material'
import * as Yup from 'yup'
import { Form, FormikProvider, useFormik } from 'formik'
import CloseIcon from '@mui/icons-material/Close'
import { FileUpload } from '@/components/FileUpload'
import { useAppDispatch, useAppSelector } from '@/store'
import { uploadSalesSchedule } from '@/store/actions/AuctionSchedule'
import { UploadIcon } from '@dtbx/ui/icons'
import { CustomSingleDatePicker } from '@/components/CustomSingleDatePicker'
import { Dayjs } from 'dayjs'

type UploadDialogProps = {
  years: string[]
  open: boolean
  handleClose: () => void
}

export const UploadWeeklyScheduleDialog = ({
  years,
  open,
  handleClose,
}: UploadDialogProps) => {
  const validationSchema = Yup.object({
    startDate: Yup.string().required('Start date is required'),
    endDate: Yup.string()
      .required('End date is required')
      .test(
        'end-date-validation',
        'End date must be after start date',
        function (value) {
          const { startDate } = this.parent
          if (!startDate || !value) return true
          return new Date(value) >= new Date(startDate)
        }
      ),
  })

  const dispatch = useAppDispatch()
  const { isLoadingSchedules } = useAppSelector((state) => state.salesSchedule)

  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [startDate, setStartDate] = useState<Dayjs | null>(null)
  const [endDate, setEndDate] = useState<Dayjs | null>(null)

  const formik = useFormik({
    initialValues: {
      startDate: '',
      endDate: '',
    },
    validationSchema,
    onSubmit: async () => {
      if (!selectedFile || !startDate || !endDate) {
        console.log('Missing required fields: file, start date, or end date')
        return
      }

      setUploadProgress(0)

      await uploadSalesSchedule(
        dispatch,
        selectedFile,
        startDate.format('YYYY').toString(),
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              )
              setUploadProgress(progress)
            }
          },
        }
      )

      handleClose()
    },
  })

  const resetForm = () => {
    // Reset all form fields and state
    formik.resetForm()
    setSelectedFile(null)
    setStartDate(null)
    setEndDate(null)
    setUploadProgress(0)
  }

  const handleCancelClick = () => {
    resetForm()
    handleClose()
  }

  return (
    <Dialog maxWidth="sm" open={open} onClose={handleClose}>
      <DialogTitle>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Typography variant="subtitle1" fontWeight="bold">
            Upload New Schedule of Weekly Sales.
          </Typography>
          <IconButton
            onClick={handleClose}
            sx={{
              height: '2rem',
              width: '2rem',
              border: '1px solid #D0D5DD',
              borderRadius: '0.5rem',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <DialogContentText sx={{ mb: 2 }}>
          Please ensure that you use the schedule template to ensure the dates
          are accurate.{' '}
          <a href="/eatta/templates/schedule.xlsx" download>
            Download Template
          </a>
        </DialogContentText>

        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack spacing={2} marginTop={2}>
              <Typography fontWeight="bolder">Start Date:</Typography>
              <Stack spacing={1} sx={{ width: { xs: '100%', sm: 'auto' } }}>
                <CustomSingleDatePicker
                  onDateSelect={(date: Dayjs) => {
                    setStartDate(date)
                    formik.setFieldValue('startDate', date.format('YYYY-MM-DD'))

                    if (endDate && endDate.isBefore(date, 'day')) {
                      setEndDate(null)
                      formik.setFieldValue('endDate', '')
                    }
                  }}
                  placeholder="Select Start Date..."
                  currentDate={startDate}
                />
                {formik.touched.startDate && formik.errors.startDate && (
                  <Typography color="error" variant="caption">
                    {formik.errors.startDate}
                  </Typography>
                )}
                <Typography>
                  Refers to the <strong>catalogue closing date</strong> listed
                  under the first auction in the EATTA schedule.
                </Typography>
              </Stack>
              <Stack spacing={2} sx={{ width: { xs: '100%', sm: 'auto' } }}>
                <Typography fontWeight="bolder">End Date:</Typography>
                <CustomSingleDatePicker
                  onDateSelect={(date: Dayjs) => {
                    setEndDate(date)
                    formik.setFieldValue('endDate', date.format('YYYY-MM-DD'))
                    console.log('End Date Selected:', {
                      formatted: date.format('YYYY-MM-DD'),
                      dayjs: date,
                      localDate: date.format('YYYY-MM-DD'),
                    })
                  }}
                  placeholder="Select End Date..."
                  currentDate={endDate}
                  minDate={startDate} // Disable dates before start date
                />
                {formik.touched.endDate && formik.errors.endDate && (
                  <Typography color="error" variant="caption">
                    {formik.errors.endDate}
                  </Typography>
                )}
                <Typography>
                  Refers to the <strong>prompt date </strong>listed under the
                  last auction in the EATTA schedule.
                </Typography>
              </Stack>
            </Stack>

            <Stack spacing={2} mt={2}>
              <FileUpload
                progress={uploadProgress}
                disabled={isLoadingSchedules}
                onFileChange={(file) => setSelectedFile(file)}
              />
            </Stack>

            <Stack direction="row" spacing={3} mt={4}>
              <Button
                fullWidth
                variant="contained"
                onClick={handleCancelClick}
                sx={{ background: '#D92D20' }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                fullWidth
                variant="contained"
                startIcon={
                  isLoadingSchedules ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <UploadIcon />
                  )
                }
                disabled={isLoadingSchedules || !selectedFile}
                sx={{
                  bgcolor: selectedFile ? 'primary.main' : '#FFF',
                  border: '1px solid #E4E7EC',
                  color: selectedFile ? '#FFFFFF' : '#98A2B3',
                  fontWeight: 600,
                  '&:hover': {
                    bgcolor: 'primary.main',
                    color: '#FFFFFF',
                    '& svg': {
                      stroke: '#FFFFFF',
                    },
                  },
                }}
              >
                {isLoadingSchedules ? 'Uploading...' : 'Upload Schedule'}
              </Button>
            </Stack>
          </Form>
        </FormikProvider>
      </DialogContent>
    </Dialog>
  )
}
