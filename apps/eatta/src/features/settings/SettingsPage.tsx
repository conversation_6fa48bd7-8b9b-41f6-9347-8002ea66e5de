'use client'
import { Divider, Stack, Typography } from '@mui/material'
import React from 'react'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import SettingConfigs from './SettingConfigs'

const Payments = () => {
  const isBackOffice = checkIfBackOffice()
  return (
    <>
      {!isBackOffice ? (
        <Stack>
          <Stack
            paddingInline={3}
            paddingBlock={2}
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              backgroundColor: '#FFFFFF',
            }}
          >
            <Stack
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'start',
                gap: '8px',
              }}
            >
              <Typography
                variant="h5"
                sx={{
                  textAlign: 'left',
                  fontWeight: '600',
                }}
              >
                Settings
              </Typography>
            </Stack>
          </Stack>

          <Divider />
          <Stack
            sx={{
              backgroundColor: '#FFFFFF',
            }}
            direction="row"
            spacing={2}
            paddingInline={2}
            alignItems="center"
            justifyContent="space-between"
            width="100%"
          ></Stack>
          <EmptyPage
            bgUrl={isBackOffice ? '/eatta/combo.svg' : '/combo.svg'}
            title="No configurations"
            message="Configurations and settings will appear here"
          />
        </Stack>
      ) : (
        <SettingConfigs />
      )}
    </>
  )
}

export default Payments
