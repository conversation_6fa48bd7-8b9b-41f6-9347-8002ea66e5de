import React, { useEffect, useState } from 'react'
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import {
  BrokerInvoice,
  InvoicesFilters,
  BrokerInvoiceStatus,
  Order,
} from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { getBrokerInvoices } from '@/store/actions/invoices'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { ArrowForwardIos } from '@mui/icons-material'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { BROKER_INVOICE_STATUS_COLOR } from '@/utils/statusChips'
import { setSelectedInvoice } from '@/store/reducers/invoicesReducer'
import { setSelectedInvoices } from '@/store/reducers'
import { formatCurrency } from '@dtbx/store/utils'
import TableSkeleton from '@/components/TableSkeleton'
import { useResetPageOnFilterChange } from '@/hooks/useResetPageOnFilterChange'

export interface InvoiceTableProps {
  canSelect: boolean
  filters: InvoicesFilters
  onLotCountChange?: (count: number) => void
}

export const InvoiceTable = ({
  canSelect = false,
  filters,
  onLotCountChange,
}: InvoiceTableProps) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('id')

  const { brokerInvoicesResponse, isLoading, selectedInvoices } =
    useAppSelector((state) => state.invoices)

  const { decodedToken } = useAppSelector((state) => state.auth)
  const isBackOffice = checkIfBackOffice()

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    setOrder(isAsc ? 'desc' : 'asc')
    setOrderBy(property as keyof BrokerInvoice)
  }

  // const sortKey = orderBy as keyof BrokerInvoice
  // const brokerInvoicesSorted = sortData(
  //   [...brokerInvoicesResponse.data],
  //   sortKey,
  //   order
  // )

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })

  const handleSelectOne = (invoice: BrokerInvoice) => {
    dispatch(setSelectedInvoice(invoice))
  }

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    let invoices: BrokerInvoice[] = []
    if (event.target.checked) {
      invoices = brokerInvoicesResponse.data
    }
    dispatch(setSelectedInvoices(invoices))
  }

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
  }
  /*************************end pagination handlers***************************/

  const emptyMessage =
    isBackOffice || decodedToken.clientType === 'Broker'
      ? 'Please upload a new invoice to get started.'
      : 'Your invoices from the latest _sales catalogue will appear here. Please reach out to your broker to confirm they have uploaded the latest catalogue or contact support on 0716938273 for further assistance.'

  const resetPageRef = useResetPageOnFilterChange(
    filters,
    paginationOptions,
    setPaginationOptions
  )

  useEffect(() => {
    if (resetPageRef.current) {
      resetPageRef.current = false
      return
    }
    const fetchInvoices = async () => {
      if (!filters.saleDate) return
      await getBrokerInvoices(
        dispatch,
        {
          ...filters,
          page: paginationOptions.page,
          size: paginationOptions.size,
          ascending: false,
        },
        isBackOffice
      )
    }
    fetchInvoices()
  }, [dispatch, filters, paginationOptions])

  useEffect(() => {
    onLotCountChange && onLotCountChange(brokerInvoicesResponse.totalElements)
  }, [brokerInvoicesResponse])

  const handleViewRequest = (brokerInvoiceId: string) => {
    router.push(`/invoices/${brokerInvoiceId}`)
  }

  return (
    <>
      {isLoading ? (
        <TableSkeleton rowCount={4} columnCount={10} />
      ) : brokerInvoicesResponse.totalElements === 0 ? (
        <EmptyPage
          title="There are no records to show"
          message={emptyMessage}
          bgUrl={isBackOffice ? '/eatta/combo.svg' : '/combo.svg'}
        />
      ) : (
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
            marginTop: '25px',
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="designations table"
            size="small"
          >
            <CustomTableHeader
              order={order}
              orderBy={orderBy}
              headLabel={[
                {
                  id: 'invoiceNumber',
                  label: 'Invoice Number',
                  alignRight: false,
                },
                { id: 'createdBy', label: 'Created By', alignRight: false },
                {
                  id: decodedToken.clientType === 'Broker' ? 'buyer' : 'broker',
                  label:
                    decodedToken.clientType === 'Broker' ? 'Buyer' : 'Broker',
                  alignRight: false,
                },

                { id: 'auctionDate', label: 'Auction Date', alignRight: false },
                { id: 'promptDate', label: 'Prompt Date', alignRight: false },
                {
                  id: 'lotNumber',
                  label: 'No of Lots',
                  alignRight: false,
                },
                {
                  id: 'soldWeight',
                  label: 'Total Sold Weight (Kgs)',
                  alignRight: false,
                },
                {
                  id: 'totalInvoiceAmount',
                  label: 'Amount Payable',
                  alignRight: false,
                },
                {
                  id: 'amountReceived',
                  label: 'Amount Received (USD)',
                  alignRight: false,
                },
                {
                  id: 'remainingBalance',
                  label: 'Remaining  Balance',
                  alignRight: false,
                },
                {
                  id: 'status',
                  label: 'Status',
                  alignRight: false,
                },
                {
                  label: '',
                },
              ]}
              showCheckbox={canSelect}
              rowCount={brokerInvoicesResponse.totalElements}
              onRequestSort={handleRequestSort}
              numSelected={selectedInvoices.length}
              onSelectAllClick={handleSelectAll}
            />
            <TableBody>
              {brokerInvoicesResponse.data.map((row) => {
                const {
                  invoiceNumber,
                  createdBy,
                  auctionDate,
                  buyerCode,
                  brokerCode,
                  status,
                  lotCount,
                  soldWeight,
                  totalInvoiceAmount,
                  amountReceived,
                  promptDate,
                  remainingBalance,
                  id,
                } = row
                const isItemSelected = selectedInvoices.some(
                  (row) => row.id === id
                )
                return (
                  <TableRow
                    hover
                    key={`${id}`}
                    tabIndex={-1}
                    role="checkbox"
                    selected={isItemSelected}
                    aria-checked={isItemSelected}
                  >
                    {canSelect && (
                      <TableCell
                        padding="checkbox"
                        onClick={() => handleSelectOne(row)}
                      >
                        <CustomCheckBox
                          checked={isItemSelected}
                          inputProps={{
                            'aria-labelledby': id,
                          }}
                          customColor={'#26b43b'}
                          style={{ padding: '5px' }}
                        />
                      </TableCell>
                    )}
                    <TableCell>{invoiceNumber}</TableCell>
                    <TableCell>{createdBy}</TableCell>

                    <TableCell>
                      {decodedToken.clientType === 'Broker'
                        ? buyerCode
                        : brokerCode}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">{auctionDate}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">{promptDate}</Typography>
                    </TableCell>
                    <TableCell>{lotCount}</TableCell>
                    <TableCell>{soldWeight}</TableCell>
                    <TableCell>
                      {formatCurrency(totalInvoiceAmount, 'USD', 'en-US')}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(amountReceived, 'USD', 'en-US')}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(remainingBalance, 'USD', 'en-US')}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        <StatusChip
                          label={status as BrokerInvoiceStatus}
                          status={
                            BROKER_INVOICE_STATUS_COLOR[
                              status as BrokerInvoiceStatus
                            ]
                          }
                        />
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton onClick={() => handleViewRequest(id)}>
                        <ArrowForwardIos />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell
                  align="center"
                  colSpan={12}
                  sx={{ paddingInline: 0, height: 40 }}
                >
                  {brokerInvoicesResponse.totalNumberOfPages > 0 && (
                    <CustomPagination
                      options={{
                        ...paginationOptions,
                        totalPages: brokerInvoicesResponse.totalNumberOfPages,
                      }}
                      handlePagination={handlePagination}
                    />
                  )}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      )}
    </>
  )
}
