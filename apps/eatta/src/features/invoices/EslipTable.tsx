/**
 * <AUTHOR> on 08/05/2025
 */

'use client'
import React, { useEffect, useState } from 'react'
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { BrokerInvoice, InvoicesFilters, Order } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { getInvoiceEslips } from '@/store/actions/invoices'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { ArrowForwardIos } from '@mui/icons-material'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { STATUS_COLOR } from '@/utils/statusChips'
import { formatCurrency, formatDate } from '@dtbx/store/utils'
import CopyButton from '@/components/CopyButton'
import TableSkeleton from '@/components/TableSkeleton'
import { useResetPageOnFilterChange } from '@/hooks/useResetPageOnFilterChange'

export interface EslipTableProps {
  filters: InvoicesFilters
  onLotCountChange?: (count: number) => void
}

export const EslipTable = ({ filters }: EslipTableProps) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('id')
  const { invoiceEslipsResponse, isLoading } = useAppSelector(
    (state) => state.invoices
  )

  const isBackOffice = checkIfBackOffice()

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    setOrder(isAsc ? 'desc' : 'asc')
    setOrderBy(property as keyof BrokerInvoice)
  }

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })

  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }

  const resetPageRef = useResetPageOnFilterChange(
    filters,
    paginationOptions,
    setPaginationOptions
  )

  useEffect(() => {
    if (resetPageRef.current) {
      resetPageRef.current = false
      return
    }
    const fetchInvoices = async () => {
      await getInvoiceEslips(dispatch, {
        ...filters,
        saleCode: filters.saleDate,
        page: paginationOptions.page,
        size: paginationOptions.size,
        ascending: false,
      })
    }
    fetchInvoices()
  }, [dispatch, filters, paginationOptions.page])

  const handleViewRequest = (eslipId: string) => {
    router.push(`/invoices/checkout/${eslipId}`)
  }

  return (
    <>
      {isLoading ? (
        <TableSkeleton rowCount={10} columnCount={7} />
      ) : invoiceEslipsResponse.totalElements === 0 ? (
        <EmptyPage
          title="No E-slips found."
          message="Your e-slips will appear here"
          bgUrl={isBackOffice ? '/eatta/combo.svg' : '/combo.svg'}
        />
      ) : (
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
            '& .MuiTableCell-root': {
              paddingInline: '1.5rem',
              paddingBlock: '0.5rem',
              textAlign: 'left',
            },
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="designations table"
            size="small"
          >
            <CustomTableHeader
              order={order}
              orderBy={orderBy}
              headLabel={[
                {
                  id: 'invoiceNumber',
                  label: 'E-Slip Number',
                  alignRight: false,
                },
                {
                  id: 'lotNumber',
                  label: 'No of Lots',
                  alignRight: false,
                },
                {
                  id: 'soldWeight',
                  label: 'Total Sold Weight (Kgs)',
                  alignRight: false,
                },
                {
                  id: 'promptDate',
                  label: 'Prompt Date',
                  alignRight: false,
                },
                {
                  id: 'totalInvoiceAmount',
                  label: 'Amount Payable',
                  alignRight: false,
                },
                {
                  id: 'amountReceived',
                  label: 'Amount Received (USD)',
                  alignRight: false,
                },
                {
                  id: 'remainingBalance',
                  label: 'Remaining  Balance',
                  alignRight: false,
                },
                {
                  id: 'status',
                  label: 'Status',
                  alignRight: false,
                },
                {
                  label: '',
                },
              ]}
              showCheckbox={false}
              rowCount={invoiceEslipsResponse.totalElements}
              onRequestSort={handleRequestSort}
              numSelected={0}
            />
            <TableBody>
              {invoiceEslipsResponse.data.map((row) => {
                const {
                  id,
                  invoiceNumber,
                  status,
                  totalAmountToPay,
                  totalSoldWeight,
                  balance,
                  paidAmount,
                  lotCount,
                  promptDate,
                } = row
                return (
                  <TableRow
                    hover
                    key={`${invoiceNumber}`}
                    tabIndex={-1}
                    role="checkbox"
                  >
                    <TableCell>
                      {invoiceNumber}
                      <CopyButton value={invoiceNumber} />
                    </TableCell>
                    <TableCell>{lotCount}</TableCell>
                    <TableCell>{totalSoldWeight}</TableCell>
                    <TableCell>{formatDate(promptDate)}</TableCell>
                    <TableCell>
                      {formatCurrency(totalAmountToPay, 'USD', 'en-US')}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(paidAmount, 'USD', 'en-US')}
                    </TableCell>
                    <TableCell>
                      {formatCurrency(balance, 'USD', 'en-US')}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        <StatusChip
                          label={status}
                          status={STATUS_COLOR[status]}
                        />
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <IconButton onClick={() => handleViewRequest(id)}>
                        <ArrowForwardIos />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell
                  align="center"
                  colSpan={11}
                  sx={{ paddingInline: 0, height: 40 }}
                >
                  {invoiceEslipsResponse.totalElements > 0 && (
                    <CustomPagination
                      options={{
                        ...paginationOptions,
                        totalPages: invoiceEslipsResponse.totalNumberOfPages,
                      }}
                      handlePagination={handlePagination}
                    />
                  )}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      )}
    </>
  )
}
