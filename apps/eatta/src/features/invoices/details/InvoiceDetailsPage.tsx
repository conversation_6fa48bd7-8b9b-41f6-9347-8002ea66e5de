import { useAppDispatch, useAppSelector } from '@/store'
import React, { FC, useEffect, useState } from 'react'
import { MainPageHeader } from '@/components/MainPageHeader'
import {
  Divider,
  IconButton,
  Stack,
  ToggleButton,
  Typography,
} from '@mui/material'
import { useCustomRouter } from '@dtbx/ui/hooks'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import { CustomToggleButtonGroup } from '@/components/CustomToggleButton'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { getInvoiceById } from '@/store/actions'
import { formatCurrency } from '@dtbx/store/utils'
import { CheckoutCart } from '@/components/CheckoutCart'
import { InvoiceLotsTable } from '@/features/invoices/details/InvoiceLotsTable'

const SECTIONS = ['Lots Invoiced'] as const
type SectionType = (typeof SECTIONS)[number]

export const InvoiceDetails = () => {
  const router = useCustomRouter()
  const { brokerInvoice } = useAppSelector((state) => state.invoices)

  return (
    <Stack
      sx={{ backgroundColor: '#FFFFFF' }}
      paddingInline={3}
      paddingBlock={2}
      spacing={2}
    >
      <Stack direction="row" alignItems="center" spacing={3}>
        <IconButton
          sx={{
            backgroundColor: '#FFFFFF',
            borderRadius: '8px',
            border: '1px solid #D0D5DD',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          onClick={() => router.back()}
        >
          <ArrowBackIosNewOutlinedIcon />
        </IconButton>

        <Stack direction="row" alignItems="center" spacing={1}>
          <Typography variant={'h6'} fontWeight={600} color="textPrimary">
            Invoice no. {brokerInvoice?.invoiceNumber}
          </Typography>

          <StatusChip
            label={`${brokerInvoice?.lotCount} Lots`}
            sx={{ width: '5rem', borderRadius: '1rem', fontSize: '0.75rem' }}
          />
        </Stack>
        <Typography variant={'h6'} fontWeight={600} color="textPrimary">
          Total Amount payable:{' '}
          {formatCurrency(brokerInvoice?.totalInvoiceAmount, 'USD', 'en-US')}
        </Typography>
      </Stack>

      <Stack direction="row" alignItems="center" spacing={3}>
        <Typography>Buyer: {brokerInvoice?.buyerCode}</Typography>
        <Typography>Broker: {brokerInvoice?.brokerCode}</Typography>
      </Stack>
    </Stack>
  )
}

interface InvoiceDetailsProps {
  invoiceId: string
}
const InvoiceDetailsPage: FC<InvoiceDetailsProps> = ({ invoiceId }) => {
  const dispatch = useAppDispatch()

  const [section, setSection] = useState<SectionType>('Lots Invoiced')

  const { decodedToken } = useAppSelector((state) => state.auth)
  const isBuyer = decodedToken.clientType === 'Buyer'

  const handleSection = (
    _event: React.MouseEvent<HTMLElement>,
    newSection: SectionType
  ) => {
    setSection(newSection)
  }

  useEffect(() => {
    const fetchInvoice = async () => {
      await getInvoiceById(dispatch, invoiceId)
    }
    fetchInvoice()
  }, [])

  return (
    <Stack sx={{ height: '100%' }} spacing={2}>
      <Stack sx={{ backgroundColor: '#FFFFFF' }}>
        <MainPageHeader>
          <CheckoutCart checkoutType="INVOICE_ENTRY" />
        </MainPageHeader>

        <Divider />

        <InvoiceDetails />

        <Divider />

        <Stack paddingInline={3} paddingBlock={1}>
          <Typography variant={'h6'} fontWeight={600} color="textPrimary">
            Lots Invoiced
          </Typography>
        </Stack>
      </Stack>

      <InvoiceLotsTable canSelect={isBuyer} invoiceId={invoiceId} />
    </Stack>
  )
}

export default InvoiceDetailsPage
