import React from 'react'
import {
  Stack,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  Paper,
  TableRow,
} from '@mui/material'

const AccountSalesSkeleton = () => {
  return (
    <Stack spacing={3} useFlexGap>
      {[1].map((Index) => (
        <Stack key={Index}>
          <Stack aria-controls="pre auction content" id="panel1-header">
            <Stack
              direction="column"
              justifyContent="flex-start"
              width="100%"
              paddingInline={3}
              sx={{ backgroundColor: '#FFF' }}
            >
              <Stack direction="row" alignItems="center" spacing={1}>
                <Skeleton variant="text" width={150} height={32} />
                <Skeleton
                  variant="rectangular"
                  width={80}
                  height={24}
                  sx={{ borderRadius: '1rem' }}
                />
              </Stack>
              <Skeleton variant="text" width={100} height={24} />
            </Stack>
          </Stack>
          <Stack>
            <TableContainer
              component={Paper}
              sx={{
                boxShadow: 'none',
                '& .MuiTableCell-root': {
                  paddingInline: '1.5rem',
                  paddingBlock: '0.5rem',
                  textAlign: 'left',
                },
              }}
            >
              <Table sx={{ minWidth: 650 }} size="small">
                <TableBody>
                  {/* Table Data Rows */}
                  {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((rowIndex) => (
                    <TableRow key={rowIndex}>
                      {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((cellIndex) => (
                        <TableCell key={cellIndex}>
                          <Skeleton variant="text" width={80} height={24} />
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Stack>
        </Stack>
      ))}
    </Stack>
  )
}

export default AccountSalesSkeleton
