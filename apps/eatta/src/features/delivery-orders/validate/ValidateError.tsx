/**
 * <AUTHOR> on 02/07/2025
 */
import { Stack, Typography } from '@mui/material'
import Image from 'next/image'

export const ValidateError = () => {
  return (
    <Stack justifyContent="center" alignItems="center" spacing={3} flexGrow={1}>
      <Stack alignItems="center" spacing={3}>
        <Image src="/icons/close.png" alt="error" height={90} width={90} />
        <Stack alignItems="center" spacing={2}>
          <Typography fontWeight="bold">Delivery Order Not Found</Typography>
          <Typography textAlign="center">
            We are unable to verify your request. Please check the delivery
            order number and try again.
          </Typography>
        </Stack>
      </Stack>
    </Stack>
  )
}
