/**
 * <AUTHOR> on 02/07/2025
 */
import { Skeleton, Stack, Typography } from '@mui/material'

export const ValidateSkeleton = () => {
  return (
    <Stack justifyContent="center" alignItems="center" spacing={3} flexGrow={1}>
      <Stack alignItems="center">
        <Skeleton sx={{ width: '12rem', height: '3rem' }} />
        <Skeleton sx={{ width: '13rem', height: '2rem' }} />
        <Skeleton sx={{ width: '10rem', height: '2rem' }} />
      </Stack>

      <Stack
        padding={2}
        sx={{
          width: '100%',
          border: '1px solid #D0D5DD',
          borderRadius: '6px',
        }}
      >
        <Skeleton sx={{ width: '10rem', height: '2rem' }} />
        <Skeleton sx={{ width: '15rem', height: '2rem' }} />
        <Skeleton sx={{ width: '12rem', height: '2rem' }} />
      </Stack>
      <Stack
        padding={2}
        sx={{
          width: '100%',
          border: '1px solid #D0D5DD',
          borderRadius: '6px',
        }}
      >
        {[1, 2, 3].map((item) => (
          <Stack key={item} spacing={0.5}>
            <Skeleton sx={{ width: '8rem', height: '2rem' }} />
            <Skeleton sx={{ width: '12rem', height: '2rem' }} />
          </Stack>
        ))}
      </Stack>
    </Stack>
  )
}
