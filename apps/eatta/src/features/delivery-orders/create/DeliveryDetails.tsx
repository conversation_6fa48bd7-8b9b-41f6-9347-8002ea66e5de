import { useAppSelector } from '@/store'
import { Stack, Typography } from '@mui/material'
import { FC } from 'react'

const DeliveryDetails: FC = () => {
  const { selectedDeliveryOrderEntries } = useAppSelector(
    (state) => state.edo
  )
  return (
    <Stack
      spacing={4}
      padding={3}
      sx={{
        bgcolor: '#FFFFFF',
        border: '1px solid #D0D5DD',
        borderRadius: 2,
        width: '100%',
        mb: 2,
      }}
    >
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="flex-start"
        width="100%"
        gap={2}
      >
        <Stack direction="column" spacing={1}>
          <Typography
            sx={{
              fontSize: '1.125rem',
              fontWeight: 500,
              color: '#101828',
              textAlign: 'start',
            }}
          >
            Warehouse
          </Typography>
          <Typography
            sx={{
              fontSize: '1.125rem',
              fontWeight: 700,
              color: '#344054',
              textAlign: 'start',
            }}
          >
            {selectedDeliveryOrderEntries?.warehouse || ''}
          </Typography>
        </Stack>
        <Stack direction="column" spacing={1}>
          <Typography
            sx={{
              fontSize: '1.125rem',
              fontWeight: 500,
              color: '#101828',
              textAlign: 'start',
            }}
          >
            Buyer
          </Typography>
          <Typography
            sx={{
              fontSize: '1.125rem',
              fontWeight: 700,
              color: '#344054',
              textAlign: 'start',
            }}
          >
            {selectedDeliveryOrderEntries?.buyer || ''}
          </Typography>
        </Stack>
      </Stack>
    </Stack>
  )
}

export default DeliveryDetails
