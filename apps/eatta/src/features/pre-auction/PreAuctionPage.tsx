'use client'

import React, { useCallback, useEffect, useState } from 'react'
import { CircularProgress, Divider, Stack } from '@mui/material'
import {
  AuctionGroup,
  CATALOGUE_STATUS_OPTIONS,
  CatalogueFilters,
} from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { downloadCatalogues, getPreAuctionCatalogues } from '@/store/actions'
import { setPreAuctionFilters } from '@/store/reducers'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { PreAuctionTable } from './PreAuctionTable'
import useOnScreen from '@/hooks/useOnScreen'
import { MainPageHeader } from '@/components/MainPageHeader'
import { UploadCatalogue } from '@/components/UploadCatalogue'
import { DEFAULT_FILTER_CONFIG, PageFilters } from '@/components/PageFilters'
import { SaleTitle } from '@/components/SaleTitle'
import { CustomButton } from '@/components/CustomButton'
import { UploadIcon } from '@dtbx/ui/icons'
import { AccessWrapper } from '@/components/AccessHelper'
import AuctionSkeleton from '../../components/AuctionSkeleton'

export const PreAuctionPage = () => {
  const dispatch = useAppDispatch()
  const isBackOffice = checkIfBackOffice()
  const {
    preAuctionCataloguesResponse,
    isLoadingCatalogues,
    preAuctionFilters,
  } = useAppSelector((state) => state.catalogues)

  const { decodedToken } = useAppSelector((state) => state.auth)

  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [hasMore, setHasMore] = useState(true)

  const handleSearch = useCallback(
    (newFilters: CatalogueFilters) => {
      const updatedFilters = {
        ...newFilters,
        invoiceStatus: newFilters.status,
        page: 1,
      }
      dispatch(setPreAuctionFilters(updatedFilters))
    },
    [dispatch]
  )

  const handleExport = useCallback(() => {
    downloadCatalogues(dispatch, isBackOffice, preAuctionFilters)
  }, [dispatch, isBackOffice, preAuctionFilters])

  const loadMore = async () => {
    if (isLoadingMore) return
    setIsLoadingMore(true)
    const updatedFilters = {
      ...preAuctionFilters,
      page: preAuctionFilters.page + 1,
    }
    dispatch(setPreAuctionFilters(updatedFilters))
    // Check if we've reached the last page
    if (
      preAuctionFilters.page >= preAuctionCataloguesResponse.totalNumberOfPages
    ) {
      setHasMore(false)
    }
  }

  const { measureRef, isIntersecting, observer } = useOnScreen()

  useEffect(() => {
    if (isIntersecting && hasMore) {
      loadMore()
      observer?.disconnect()
    }
  }, [isIntersecting, hasMore, loadMore])

  //Todo: Show message based on logged in user
  const emptyMessage =
    isBackOffice || decodedToken.clientType === 'Broker'
      ? 'Please upload a new tea sale catalogue to get started.'
      : 'Your invoices from the latest sales catalogue will appear here. Please reach out to your broker to confirm they have uploaded the latest catalogue or contact customer-support on 0719 031 888 / 0732 121 888 for further assistance.'

  const [openUploadCatalogue, setOpenUploadCatalogue] = useState(false)

  const handleOpenUploadDialog = () => {
    setOpenUploadCatalogue(true)
  }

  const handleOnCloseUpload = (success?: boolean | undefined) => {
    setOpenUploadCatalogue(false)
    //Refresh data on success
    if (success) {
      setTimeout(() => {
        dispatch(
          setPreAuctionFilters({
            ...preAuctionFilters,
            page: 1,
          })
        )
      }, 2000)
    }
  }

  const fetchCatalogues = async (filters: CatalogueFilters) => {
    await getPreAuctionCatalogues(dispatch, isBackOffice, filters)
    setIsLoadingMore(false)
  }

  useEffect(() => {
    if (!preAuctionFilters.saleDate) return
    fetchCatalogues({ ...preAuctionFilters, ascending: false })
  }, [preAuctionFilters])

  return (
    <Stack sx={{ height: '100%' }}>
      {!isBackOffice && (
        <>
          <MainPageHeader>
            <AccessWrapper clientTypes={['Broker']} backofficeAccess={false}>
              <CustomButton
                variant="contained"
                onClick={handleOpenUploadDialog}
                label="Upload New Catalogue"
                startIcon={<UploadIcon stroke="#FFFFFF" />}
                sx={{
                  backgroundColor: 'primary.main',
                  border: 'none',
                  color: '#FFFFFF',
                  '&:hover': {
                    border: 'none',
                    color: '#FFFFFF',
                  },
                }}
              />
            </AccessWrapper>
          </MainPageHeader>
          <Divider />
        </>
      )}

      <PageFilters
        title="Pre-Sale"
        onSearch={handleSearch}
        statuses={CATALOGUE_STATUS_OPTIONS}
        filters={preAuctionFilters}
        onExport={handleExport}
        searchByValues={[
          { filterLabel: 'Lot Number', filterKey: 'lotNo', type: 'numeric' },
          { filterLabel: 'Producer', filterKey: 'producer', type: 'string' },
          {
            filterLabel: 'Garden Invoice',
            filterKey: 'invoiceNo',
            type: 'string',
          },
          { filterLabel: 'Mark', filterKey: 'factory', type: 'string' },
        ]}
        filterConfig={{
          ...DEFAULT_FILTER_CONFIG,
          showLateUnpaidStatus: false,
          showStatus: false,
        }}
      />

      <SaleTitle
        saleDate={`${preAuctionFilters.year}/${preAuctionFilters.saleDate}`}
        lotsCount={preAuctionCataloguesResponse.totalElements}
      />

      <Stack
        sx={{
          height: 'calc(100vh - 180px)',
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        {isLoadingCatalogues && !isLoadingMore ? (
          <AuctionSkeleton />
        ) : preAuctionCataloguesResponse.data.length === 0 ? (
          <EmptyPage
            title="There are no records to show"
            message={emptyMessage}
            bgUrl={isBackOffice ? '/eatta/combo.svg' : '/combo.svg'}
          />
        ) : (
          <Stack
            spacing={3}
            sx={{
              height: '100%',
              overflowY: 'auto',
              paddingRight: 2,
              '&::-webkit-scrollbar': {
                width: '8px',
              },
              '&::-webkit-scrollbar-track': {
                background: '#f1f1f1',
              },
              '&::-webkit-scrollbar-thumb': {
                background: '#888',
                borderRadius: '4px',
              },
            }}
          >
            {preAuctionCataloguesResponse.data.map(
              (producerGroup: AuctionGroup, groupIndex: number) => (
                <Stack
                  key={`${producerGroup.producer}-${groupIndex}`}
                  spacing={3}
                >
                  {producerGroup.factories.map((factory, factoryIndex) => (
                    <PreAuctionTable
                      key={`${factory.name}-${producerGroup.producer}-${factoryIndex}`}
                      producer={producerGroup.producer}
                      factory={factory.name}
                      catalogues={factory.catalogues}
                    />
                  ))}
                </Stack>
              )
            )}
            <div
              ref={(node) => {
                if (node) measureRef(node)
              }}
              style={{
                height: '60px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                minHeight: '60px',
              }}
            >
              {isLoadingMore && <CircularProgress size={40} />}
            </div>
          </Stack>
        )}
      </Stack>
      <UploadCatalogue
        open={openUploadCatalogue}
        onClose={handleOnCloseUpload}
      />
    </Stack>
  )
}
