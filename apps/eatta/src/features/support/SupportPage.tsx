/**
 * <AUTHOR> on 04/07/2025
 */
'use client'
import * as Yup from 'yup'
import { ALPHA_NUMERIC_REGEX } from '@/utils/validators'
import { useAppDispatch, useAppSelector } from '@/store'
import React, { FC, useState } from 'react'
import { Form, FormikProvider, useFormik } from 'formik'
import { handleCustomerSupport } from '@/store/actions'
import Grid from '@mui/material/Grid2'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Divider,
  FormControl,
  FormHelperText,
  Link,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { ChevronLeftIcon, EmailIcon, PhoneIcon } from '@dtbx/ui/icons'
import { COMPANY_TYPES_COMBINED } from '@/store/interfaces'
import { FileUpload } from '@/components/FileUpload'
import { MainPageHeader } from '@/components/MainPageHeader'
import { useRouter } from 'next/navigation'
import { matchIsValid<PERSON>, MuiTelInput } from 'mui-tel-input'

const validationSchema = Yup.object({
  membershipType: Yup.string().required('Membership type is required'),
  organizationCode: Yup.string()
    .required('Organization Code is required')
    .matches(ALPHA_NUMERIC_REGEX, 'Only alphanumeric characters are allowed'),
  email: Yup.string()
    .trim()
    .required('Email is required')
    .email('Enter valid email'),
  phoneNumber: Yup.string()
    .required('Phone number is required')
    .test('isValidPhone', 'Phone number is invalid', (value) => {
      return matchIsValidTel(value)
    }),
  issueDescription: Yup.string().required('Please describe your issue'),
  file: Yup.mixed<File>().optional(),
}).test(
  'email-or-phone-required',
  'Please provide at least an email or phone number',
  (values) => {
    return Boolean(values.email || values.phoneNumber)
  }
)

interface SupportPageProps {
  showHeader?: boolean
}

export const SupportPage: FC<SupportPageProps> = ({ showHeader = false }) => {
  const dispatch = useAppDispatch()
  const router = useRouter()

  const { isLoadingLogin } = useAppSelector((store) => store.auth)
  const [fileUploadKey, setFileUploadKey] = React.useState(Date.now())

  const formik = useFormik({
    initialValues: {
      membershipType: '',
      organizationCode: '',
      email: '',
      phoneNumber: '',
      issueDescription: '',
      file: undefined,
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      await handleCustomerSupport(dispatch, values, handleSubmitSuccess)
    },
  })

  const [phoneNumber, setPhoneNumber] = useState<string>(
    formik.values.phoneNumber
  )

  const handlePhoneChange = (value: string) => {
    console.log(value)
    setPhoneNumber(value)
    formik.setFieldTouched('phoneNumber', true, false)
    formik.setFieldValue('phoneNumber', value.replace(/\s/g, ''))
  }

  const handleSubmitSuccess = () => {
    formik.resetForm()
    setFileUploadKey(Date.now())
  }

  return (
    <Stack
      width="100%"
      height="100%"
      sx={{
        backgroundColor: '#FFF',
        flexDirection: 'column',
      }}
    >
      {showHeader && (
        <>
          <MainPageHeader />
          <Divider />
        </>
      )}

      <Stack
        display="flex"
        alignItems="center"
        overflow="auto"
        sx={{
          padding: 4,
          flexGrow: 1,
        }}
      >
        <Stack width="100%" maxWidth="62.5rem" height="max-content">
          <FormikProvider value={formik}>
            <Form onSubmit={formik.handleSubmit}>
              <Grid container spacing={2}>
                <Grid
                  size={{ xs: 12, sm: 12, md: 12, lg: 8 }}
                  sx={{
                    border: '1px solid #ccc',
                    borderRadius: '8px',
                    padding: 2,
                  }}
                >
                  <Link
                    rel="noopener noreferrer"
                    sx={{ textDecoration: 'none', display: 'inline-flex' }}
                    onClick={() => {
                      router.back()
                    }}
                  >
                    <Typography
                      variant="body2"
                      fontWeight={600}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                        gap: 1,
                        marginBottom: 1,
                        color: 'primary.main',
                      }}
                    >
                      <ChevronLeftIcon stroke="#00BC2D" fontSize="small" />
                      Back
                    </Typography>
                  </Link>

                  <Stack spacing={3}>
                    <Typography variant="h5" fontWeight="600">
                      Send us a message:
                    </Typography>

                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        Type of Membership
                        <Box component="span" sx={{ color: 'primary.main' }}>
                          *
                        </Box>
                      </Typography>
                      <Autocomplete
                        disablePortal
                        size="small"
                        id="membershipType"
                        options={COMPANY_TYPES_COMBINED}
                        {...formik.getFieldProps('membershipType')}
                        onChange={(_, value) => {
                          formik.setFieldValue('membershipType', value)
                        }}
                        renderInput={(params) => (
                          <TextField
                            hiddenLabel
                            placeholder="Select type"
                            {...params}
                            error={Boolean(
                              formik.touched.membershipType &&
                                formik.errors.membershipType
                            )}
                            helperText={
                              formik.touched.membershipType &&
                              formik.errors.membershipType
                                ? formik.errors.membershipType
                                : ''
                            }
                          />
                        )}
                      />
                    </Box>

                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        EATTA Organization Code:
                        <Box component="span" sx={{ color: 'primary.main' }}>
                          *
                        </Box>
                      </Typography>
                      <TextField
                        fullWidth
                        size="small"
                        {...formik.getFieldProps('organizationCode')}
                        error={Boolean(
                          formik.touched.organizationCode &&
                            formik.errors.organizationCode
                        )}
                        helperText={
                          formik.touched.organizationCode &&
                          formik.errors.organizationCode
                        }
                      />
                    </Box>

                    <Box>
                      <Typography variant="body2" fontWeight={500} mb={1}>
                        Enter your phone number and email{' '}
                        <Box component="span" sx={{ color: 'primary.main' }}>
                          *
                        </Box>
                      </Typography>

                      <Grid container spacing={2}>
                        <Grid size={{ xs: 12, sm: 6, md: 6, lg: 6 }}>
                          <TextField
                            fullWidth
                            size="small"
                            label="Email"
                            placeholder="e.g. <EMAIL>"
                            {...formik.getFieldProps('email')}
                            error={Boolean(
                              formik.touched.email && formik.errors.email
                            )}
                            helperText={
                              formik.touched.email && formik.errors.email
                            }
                          />
                        </Grid>

                        <Grid size={{ xs: 12, sm: 6, md: 6, lg: 6 }}>
                          <FormControl
                            fullWidth
                            error={Boolean(
                              formik.touched.phoneNumber &&
                                formik.errors.phoneNumber
                            )}
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 1,
                            }}
                          >
                            <MuiTelInput
                              hiddenLabel
                              size="small"
                              value={phoneNumber}
                              name="phoneNumber"
                              defaultCountry="KE"
                              onlyCountries={['KE', 'UG', 'TZ', 'BI']}
                              onChange={handlePhoneChange}
                              slotProps={{
                                htmlInput: {
                                  maxLength: 15,
                                },
                              }}
                            />
                            {formik.touched.phoneNumber &&
                              formik.errors.phoneNumber && (
                                <FormHelperText error>
                                  {formik.errors.phoneNumber}
                                </FormHelperText>
                              )}
                          </FormControl>
                        </Grid>
                      </Grid>

                      <Typography
                        variant="caption"
                        color="text.secondary"
                        mt={1}
                        display="block"
                      >
                        Provide at least one so we can contact you.
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="body2" fontWeight={500}>
                        Describe your issue{' '}
                        <Box component="span" sx={{ color: 'primary.main' }}>
                          *
                        </Box>
                      </Typography>
                      <TextField
                        fullWidth
                        multiline
                        rows={4}
                        size="small"
                        placeholder="Enter description..."
                        {...formik.getFieldProps('issueDescription')}
                        error={Boolean(
                          formik.touched.issueDescription &&
                            formik.errors.issueDescription
                        )}
                        helperText={
                          formik.touched.issueDescription &&
                          formik.errors.issueDescription
                        }
                      />
                    </Box>

                    <Box>
                      <FileUpload
                        key={fileUploadKey}
                        progress={0}
                        description="PDF or PNG, JPG image allowed."
                        mimeTypes={[
                          'application/pdf',
                          'image/png',
                          'image/jpeg',
                        ]}
                        required={false}
                        onFileChange={(file) =>
                          formik.setFieldValue('file', file)
                        }
                      />
                    </Box>

                    <Button
                      variant="contained"
                      type="submit"
                      disabled={!formik.isValid || isLoadingLogin}
                      endIcon={
                        isLoadingLogin ? (
                          <CircularProgress size={20} thickness={3} />
                        ) : undefined
                      }
                    >
                      Submit
                    </Button>
                  </Stack>
                </Grid>

                <Grid size={{ xs: 12, sm: 12, md: 12, lg: 4 }}>
                  <Stack spacing={2}>
                    <Typography variant="h6" color="primary" fontWeight={600}>
                      Or Talk to Us Directly
                    </Typography>

                    <EmailIcon />
                    <Stack direction="row" alignItems="flex-start" spacing={2}>
                      <Box>
                        <Typography
                          variant="body1"
                          color="text.secondary"
                          fontWeight={500}
                        >
                          EMAIL
                        </Typography>
                        <Link
                          variant="body1"
                          color="primary"
                          sx={{ cursor: 'pointer', textDecoration: 'none' }}
                          href="mailto:<EMAIL>?subject=EATTA Support Request"
                        >
                          <EMAIL>
                        </Link>
                      </Box>
                    </Stack>

                    <PhoneIcon />
                    <Stack direction="row" alignItems="flex-start" spacing={2}>
                      <Box>
                        <Typography
                          variant="body1"
                          color="text.secondary"
                          fontWeight={500}
                        >
                          PHONE NUMBER
                        </Typography>
                        <Link
                          variant="body1"
                          color="primary"
                          sx={{ cursor: 'pointer', textDecoration: 'none' }}
                          href="tel:**********"
                        >
                          0719 031 888 / 0732 121 888
                        </Link>
                      </Box>
                    </Stack>
                  </Stack>
                </Grid>
              </Grid>
            </Form>
          </FormikProvider>
        </Stack>
      </Stack>
    </Stack>
  )
}
