/**
 * <AUTHOR> on 01/07/2025
 */
import { Divider, Skeleton, Stack } from '@mui/material'

export const EslipDetailsSkeleton = () => {
  return (
    <Stack
      spacing={1}
      padding={3}
      sx={{
        border: '1px solid #D0D5DD',
        borderRadius: '6px',
      }}
    >
      <Stack direction="row" justifyContent="flex-start" alignItems="center">
        <Skeleton height={'3rem'} width={'8rem'} />
      </Stack>

      <Stack direction="row" spacing={2}>
        <Stack spacing={1} flex={1}>
          <Skeleton width={'4rem'} height={'2rem'} />
          <Skeleton width={'10rem'} height={'2rem'} />
        </Stack>
        <Stack spacing={1} flex={1}>
          <Skeleton width={'4rem'} height={'2rem'} />
          <Skeleton width={'10rem'} height={'2rem'} />
        </Stack>
      </Stack>
      <Divider />

      <Stack spacing={1}>
        {[1, 2, 3, 4, 5, 6].map((item) => (
          <Stack
            key={item}
            direction="row"
            justifyContent="space-between"
            spacing={2}
          >
            <Skeleton width={'10rem'} height={'2rem'} />
            <Skeleton width={'10rem'} height={'2rem'} />
          </Stack>
        ))}
        <Skeleton height={'5rem'} />
      </Stack>
    </Stack>
  )
}
