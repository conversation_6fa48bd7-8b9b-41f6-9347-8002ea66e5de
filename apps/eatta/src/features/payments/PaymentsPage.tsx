'use client'
import { Divide<PERSON>, Stack } from '@mui/material'
import { PaymentFilters, PaymentStatus } from '@/store/interfaces/transactions'
import { PaymentsTable } from './PaymentsTable'
import React, { useEffect, useMemo, useState } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { TabPanel } from '@dtbx/ui/components/Tabs'
import { PaymentsApprovalsTable } from '@/features/payments/PaymentsApprovalsTable'
import { CustomTabs, TabType } from '@/components/CustomTabs'
import { useAppDispatch, useAppSelector } from '@/store'
import { resetTransactions } from '@/store/reducers'
import { PaymentHeader } from '@/features/payments/PaymentHeader'
import { SearchByValueConfig } from '@/components/PageFilters'
import { InvoicesFilters } from '@/store/interfaces'
import { ApprovalRequestFilters } from '@/store/interfaces/makerChecker'

const TABS: TabType<PaymentStatus>[] = [
  {
    title: 'Allocated',
    status: 'ALLOCATED',
    canSelect: true,
  },
  {
    title: 'Unallocated',
    status: 'UNALLOCATED',
    canSelect: false,
  },
  {
    title: 'Pending Approval',
    status: 'PENDING',
    canSelect: false,
  },
]

const initialFilters = {
  page: 1,
  size: 10,
}

const paymentSearchByValue: SearchByValueConfig<PaymentFilters>[] = [
  {
    filterLabel: 'Transaction Ref',
    filterKey: 'transactionRef',
    type: 'string',
  },
  {
    filterLabel: 'Eslip Number',
    filterKey: 'eslipNumber',
    type: 'string',
  },
  {
    filterLabel: 'Buyer Name',
    filterKey: 'buyer',
    type: 'string',
  },
  {
    filterLabel: 'Amount',
    filterKey: 'amount',
    type: 'numeric',
  },
]

const approvalsSearchByValues: SearchByValueConfig<ApprovalRequestFilters>[] = [
  {
    filterLabel: 'Maker Firstname',
    filterKey: 'makerFirstName',
    type: 'string',
  },
  {
    filterLabel: 'Maker Lastname',
    filterKey: 'makerLastName',
    type: 'string',
  },
]

export const PaymentsPage = () => {
  const dispatch = useAppDispatch()
  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState<TabType<PaymentStatus>[]>(TABS)
  const [selectedTab, setSelectedTab] = useState(initialTab)

  const { isLoading } = useAppSelector((state) => state.transactions)

  const [filters, setFilters] = useState<
    PaymentFilters | ApprovalRequestFilters
  >({
    page: 1,
    size: 10,
  })

  const isApprovalTab = useMemo(() => {
    return tabs[selectedTab].title === 'Pending Approval'
  }, [selectedTab, tabs])

  const searchByValues = useMemo(() => {
    return isApprovalTab ? approvalsSearchByValues : paymentSearchByValue
  }, [isApprovalTab])

  const handleSearch = (
    field: keyof PaymentFilters | keyof ApprovalRequestFilters,
    value: string
  ) => {
    setFilters({
      ...initialFilters,
      [field]: value,
    })
  }

  const handleTabChange = (index: number) => {
    dispatch(resetTransactions())
    setSelectedTab(index)
  }

  const handleCountChange = (count: number, tab: TabType<PaymentStatus>) => {
    setTabs((prevTabs) =>
      prevTabs.map((t) =>
        t.title === tab.title ? { ...t, itemCounts: count } : t
      )
    )
  }

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    replace(`${pathname}?${params.toString()}`)
  }

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab, filters])

  return (
    <Stack sx={{ height: '100%' }}>
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
        }}
      >
        <PaymentHeader
          filters={filters}
          onSearchChange={handleSearch}
          searchByValues={searchByValues}
          initialSearchBy={isApprovalTab ? 'makerFirstName' : 'transactionRef'}
        />
        <Divider />
        <CustomTabs
          tabs={tabs}
          selectedTab={selectedTab}
          onTabSelected={handleTabChange}
          isLoading={isLoading}
        />
      </Stack>

      <Stack sx={{ overflow: 'auto' }}>
        {tabs.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            {tab.title === 'Pending Approval' ? (
              <PaymentsApprovalsTable
                status={tab.status}
                filters={filters as ApprovalRequestFilters}
                onApprovalCountChange={(count) => handleCountChange(count, tab)}
                canSelect={false}
                onSetCurrentTab={setSelectedTab}
              />
            ) : (
              <PaymentsTable
                status={tab.status}
                filters={filters}
                canSelect={tabs[selectedTab].canSelect}
                onTransactionCountChange={(count) =>
                  handleCountChange(count, tabs[selectedTab])
                }
                onSetCurrentTab={setSelectedTab}
              />
            )}
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}
