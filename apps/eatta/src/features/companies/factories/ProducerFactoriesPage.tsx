/**
 * <AUTHOR> on 23/10/2024
 */

'use client'
import { Divider, Stack } from '@mui/material'
import React, { useCallback, useEffect, useState } from 'react'
import { useAppDispatch } from '@/store'
import { getProducerFactories } from '@/store/actions'
import { ProducerFactoryFilters } from '@/store/interfaces'
import { getAuctionWeek } from '@dtbx/store/utils'
import { MainPageHeader } from '@/components/MainPageHeader'
import { PageFilters } from '@/components/PageFilters'
import { DEFAULT_FILTER_CONFIG } from '@/components/PageFilters'
import ProducerFactoriesTable from './ProducerFactories'

const ProducerFactoriesPage = () => {
  const dispatch = useAppDispatch()
  const week = getAuctionWeek().toString()
  const [filters, setFilters] = useState<ProducerFactoryFilters>({
    page: 1,
    size: 10,
    invoiceEntryStatus: undefined,
    year: new Date().getFullYear(),
    saleDate: week,
  })

  const handleSearch = useCallback((newFilters: ProducerFactoryFilters) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
    }))
  }, [])

  const handlePageChange = useCallback((page: number, size: number) => {
    setFilters((prev) => ({
      ...prev,
      page,
      size,
    }))
  }, [])

  useEffect(() => {
    getProducerFactories(dispatch, {
      ...filters,
    })
  }, [dispatch, filters])

  return (
    <Stack sx={{ height: '100%' }}>
      <MainPageHeader />

      <Divider />
      <PageFilters
        title="Factories"
        onSearch={handleSearch}
        searchByValues={[{ filterKey: 'factory', type: 'string' }]}
        filters={filters}
        filterConfig={{
          ...DEFAULT_FILTER_CONFIG,
          showBrokerFilter: false,
          showStatus: false,
          showExport: false,
          showFactory: false,
          showSearchBox: true,
        }}
        onExport={() => {}}
      />

      <Stack
        spacing={1}
        paddingInline={3}
        paddingBlock={2}
        sx={{
          backgroundColor: '#FFFFFF',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      ></Stack>

      <ProducerFactoriesTable
        filters={filters}
        onPageChange={handlePageChange}
      />
    </Stack>
  )
}

export default ProducerFactoriesPage
