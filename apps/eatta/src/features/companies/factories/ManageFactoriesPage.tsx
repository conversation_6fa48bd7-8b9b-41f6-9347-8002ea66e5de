'use client'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Stack, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { getFactories, getOrganizationById } from '@/store/actions'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import FactoriesTable from './ListFactories'
import { CreateFactoryDialog } from './CreateFactoryDialog'
import { FactoryFilters } from '@/store/interfaces/factory'
import { AddFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { SearchRounded } from '@mui/icons-material'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'

const initialFilters: FactoryFilters = {
  page: 1,
  size: 10,
  name: '',
  parentOrganizationId: '',
}

export default function FactoriesPage({
  organizationId,
}: {
  organizationId: string
}) {
  const [openFactoryDialog, setOpenFactoryDialog] = useState(false)
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const isBackOffice = checkIfBackOffice()

  const { selectedCompany, factoryResponse, selectedFactories } =
    useAppSelector((state) => state.companies)

  const paginationOptions = {
    page: 1,
    size: 10,
    totalPages: factoryResponse.totalNumberOfPages,
  }

  const [filters, setFilters] = useState<FactoryFilters>(initialFilters)

  const [searchValue, setSearchValue] = useState('')
  const debouncedSearchValue = useDebounce(searchValue, 500)

  useEffect(() => {
    handleChange('name', debouncedSearchValue)
  }, [debouncedSearchValue])

  const handleChange = (field: keyof FactoryFilters, value: string) => {
    setFilters({
      ...initialFilters,
      [field]: value,
    })
  }

  const handleCreateFactory = () => {
    if (!selectedCompany) return
    router.push(`/backoffice/companies/${selectedCompany.id}/factories/create`)
  }

  const handleCloseFactoryDialog = () => {
    setOpenFactoryDialog(false)
  }

  useEffect(() => {
    if (organizationId) {
      getOrganizationById(dispatch, organizationId)
    }
  }, [organizationId])

  useEffect(() => {
    const fetchFactories = async () => {
      await getFactories(dispatch, isBackOffice, {
        ...filters,
        page: paginationOptions.page,
        size: paginationOptions.size,
        parentOrganizationId: organizationId || '',
      })
    }
    fetchFactories()
  }, [filters])
  return (
    <Stack>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            gap: '8px',
          }}
        >
          <IconButton
            sx={{
              borderRadius: '8px',
              border: '1px solid #D0D5DD',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            onClick={() => router.back()}
          >
            <ArrowBackIosNewOutlinedIcon />
          </IconButton>
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: 600,
            }}
          >
            {selectedCompany?.name || ''}
          </Typography>
        </Stack>
      </Stack>
      <Divider />
      <Stack
        spacing={1}
        paddingInline={3}
        paddingBlock={2}
        flexWrap="wrap"
        sx={{
          backgroundColor: '#FFFFFF',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Stack direction="row" spacing={3}>
          <CustomSearchInput
            placeholder="Search factory, mark"
            onChange={(e) => setSearchValue(e.target.value)}
            startAdornment={
              <SearchRounded
                sx={{
                  mr: 1,
                  color: 'gray',
                }}
              />
            }
            sx={{
              width: '25rem',
              borderRadius: '8px',
              background: '#FFFFFF',
              '& fieldset': {
                border: `1px solid #D0D5DD !important`,
              },
            }}
          />
        </Stack>

        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_ORGANIZATION}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddFactoryIcon stroke="#FFFFFF" />}
            sx={{ borderRadius: '0.5rem', textWrap: 'nowrap' }}
            onClick={handleCreateFactory}
          >
            Add New Factory
          </Button>
        </AccessControlWrapper>
      </Stack>
      <Divider />

      <Stack>
        <FactoriesTable factories={selectedFactories} />
      </Stack>
      <CreateFactoryDialog
        open={openFactoryDialog}
        handleClose={handleCloseFactoryDialog}
        organizationName={selectedCompany?.name || ''}
        organizationId={selectedCompany?.id || ''}
      />
    </Stack>
  )
}
