/**
 * <AUTHOR> on 17/10/2024
 */
import {
  Autocomplete,
  Button,
  CircularProgress,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import { useAppDispatch } from '@dtbx/store'
import { useAppSelector } from '@/store'
import React, { useEffect, useMemo } from 'react'
import {
  Bank,
  BankBranch,
  CHANNELS,
  Company,
  CompanyPaymentRequest,
  CURRENCIES,
  Currency,
  PaymentChannel,
} from '@/store/interfaces'
import { createCompany, fetchBanks } from '@/store/actions'
import { NUMERIC_REGEX } from '@/utils/validators'
import { CreateCompanySteps } from '@/features/companies/create/CreateCompanyPage'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'

const initialSchema = Yup.object({
  bankName: Yup.string().required('Bank should not be empty'),
  bankAccountNumber: Yup.string()
    .required('Account number should not be empty')
    .matches(NUMERIC_REGEX, 'Account number should be a number')
    .min(5, 'Account number should be at least 5 digits')
    .max(16, 'Account number should have max of 16 digits'),
  currency: Yup.string().required('Currency should not be empty'),
  swiftCode: Yup.string().required('Swift code should not be empty'),
})

type PaymentDetailsProps = {
  setStep: (step: CreateCompanySteps) => void
}

const PaymentDetails = ({ setStep }: PaymentDetailsProps) => {
  const dispatch = useAppDispatch()
  const { onboardingCompany, banks, bankBranches, isLoading } = useAppSelector(
    (state) => state.companies
  )
  const company: Company | null = useMemo(
    () =>
      onboardingCompany?.entity ? JSON.parse(onboardingCompany.entity) : null,
    [onboardingCompany]
  )

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_ORGANIZATION),
    []
  )

  const [validationSchema, updateValidationSchema] =
    React.useState(initialSchema)

  const [bank, setBank] = React.useState<Bank | null>(null)
  const [branch, setBranch] = React.useState<BankBranch | null>(null)
  const [currency, setCurrency] = React.useState<Currency | null>(null)

  useEffect(() => {
    setBank(banks.find((b) => b.bankName === company?.bankName) ?? null)
    setBranch(
      bankBranches.find((b) => b.code === company?.bankBranchCode) ?? null
    )
    setCurrency(CURRENCIES.find((c) => c.code === company?.currency) ?? null)
  }, [company, banks, bankBranches])

  useEffect(() => {
    fetchBanks(dispatch)
  }, [])

  useEffect(() => {
    if (company?.type === 'Buyer') return

    const schema = initialSchema.shape({
      channel: Yup.string<PaymentChannel>().required(
        'Channel should not be empty'
      ),
    })

    if (bank?.bicCode !== 'DTKEKENA') {
      schema.shape({
        bankBranchName: Yup.string().required(
          'Branch name should not be empty'
        ),
        bankBranchCode: Yup.string().required(
          'Branch code should not be empty'
        ),
      })
    }

    updateValidationSchema(schema)
  }, [company?.type, bank?.bicCode])

  const formik = useFormik({
    initialValues: {
      bankName: company?.bankName ?? '',
      bankBranchName: company?.bankBranchName ?? '',
      bankBranchCode: company?.bankBranchCode ?? '',
      bankAccountNumber: company?.bankAccountNumber ?? '',
      currency: company?.currency ?? '',
      channel: company?.channel ?? '',
      swiftCode: company?.swiftCode ?? '',
      stepName: 'PAYMENT',
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      if (!onboardingCompany) return

      const payload = {
        ...values,
        approvalId: onboardingCompany.id,
      } as CompanyPaymentRequest

      await createCompany(isSuper, dispatch, payload, () =>
        setStep(CreateCompanySteps.SUBMISSION)
      )
    },
  })

  const handleBankChange = (value: Bank | null) => {
    setBank(value)
    formik.setFieldValue('bankName', value?.bankName ?? '')
    formik.setFieldValue('swiftCode', value?.bicCode ?? '')
    formik.setFieldValue('channel', value?.bicCode === 'DTKEKENA' ? 'IFT' : '')
  }

  return (
    <Stack spacing={2}>
      <Typography variant="h6" fontWeight={600}>
        Payment Details
      </Typography>
      <Typography
        variant="body1"
        fontWeight={400}
        sx={{ color: '#344054', fontSize: '1rem' }}
      >
        {company?.type === 'Buyer'
          ? 'Bank accounts belonging to buyers that will be the origin of the payments they make to the collections account.'
          : 'Bank accounts belonging to producers or brokers through which they receive funds after a sale. They can be paid either through SWIFT or RTGS.'}
      </Typography>

      <Stack
        spacing={3}
        sx={{
          padding: 3,
          backgroundColor: '#FFFFFF',
          borderRadius: '0.5rem',
        }}
      >
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack spacing={2}>
              <Stack spacing={1}>
                <Typography variant="body2" sx={{ fontWeight: 600 }}>
                  Select Bank
                </Typography>
                <Autocomplete
                  disablePortal
                  size="small"
                  id="bankCode"
                  options={banks}
                  value={bank}
                  onChange={(_, value) => handleBankChange(value)}
                  getOptionLabel={(option) => option.bankName}
                  isOptionEqualToValue={(option, value) =>
                    option.bankCode === value.bankCode
                  }
                  renderInput={(params) => (
                    <TextField
                      hiddenLabel
                      {...params}
                      placeholder="Select bank"
                      error={Boolean(
                        formik.touched.bankName && formik.errors.bankName
                      )}
                      helperText={
                        formik.touched.bankName && formik.errors.bankName
                          ? formik.errors.bankName
                          : ''
                      }
                    />
                  )}
                />
              </Stack>

              <Stack direction="row" spacing={2}>
                <Stack sx={{ width: '50%' }}>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    Account Number
                  </Typography>
                  <TextField
                    hiddenLabel
                    size="small"
                    type="text"
                    placeholder="********"
                    sx={{ marginBlock: '0' }}
                    margin={'normal'}
                    {...formik.getFieldProps('bankAccountNumber')}
                    fullWidth
                    slotProps={{
                      input: {
                        inputProps: {
                          maxLength: 16,
                          minLength: 5,
                        },
                      },
                    }}
                    error={Boolean(
                      formik.touched.bankAccountNumber &&
                        formik.errors.bankAccountNumber
                    )}
                    helperText={
                      formik.touched.bankAccountNumber &&
                      formik.errors.bankAccountNumber
                    }
                  />
                </Stack>

                <Stack sx={{ width: '50%' }}>
                  <Typography variant="body2" sx={{ fontWeight: 600 }}>
                    Currency
                  </Typography>
                  <Autocomplete
                    fullWidth
                    disablePortal
                    size="small"
                    id="currency"
                    options={CURRENCIES}
                    value={currency}
                    onChange={(_, value) => {
                      setCurrency(value)
                      formik.setFieldValue('currency', value?.code ?? '')
                    }}
                    getOptionLabel={(option) => option.name}
                    isOptionEqualToValue={(option, value) =>
                      option.code === value.code
                    }
                    renderInput={(params) => (
                      <TextField
                        hiddenLabel
                        {...params}
                        placeholder="Select currency"
                        error={Boolean(
                          formik.touched.currency && formik.errors.currency
                        )}
                        helperText={
                          formik.touched.currency && formik.errors.currency
                            ? formik.errors.currency
                            : ''
                        }
                      />
                    )}
                  />
                </Stack>
              </Stack>

              {company?.type !== 'Buyer' && (
                <>
                  <Stack sx={{ width: '100%' }}>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      Channel
                    </Typography>
                    <Autocomplete
                      fullWidth
                      disablePortal
                      size="small"
                      id="channel"
                      options={CHANNELS.filter((c) =>
                        bank?.bicCode === 'DTKEKENA' ? c === 'IFT' : c !== 'IFT'
                      )}
                      {...formik.getFieldProps('channel')}
                      onChange={(_, value: PaymentChannel) => {
                        formik.setFieldValue('channel', value)
                      }}
                      renderInput={(params) => (
                        <TextField
                          hiddenLabel
                          {...params}
                          placeholder="Select channel"
                          error={Boolean(
                            formik.touched.channel && formik.errors.channel
                          )}
                          helperText={
                            formik.touched.channel && formik.errors.channel
                              ? formik.errors.channel
                              : ''
                          }
                        />
                      )}
                    />
                  </Stack>

                  {formik.values.channel === 'IFT' && (
                    <Stack direction="row" spacing={2}>
                      <Stack sx={{ width: '100%' }}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Branch Name
                        </Typography>
                        <Autocomplete
                          fullWidth
                          disablePortal
                          size="small"
                          id="bankBranchName"
                          options={bankBranches}
                          value={branch}
                          onChange={(_, value) => {
                            setBranch(value)
                            formik.setFieldValue(
                              'bankBranchName',
                              value?.name ?? ''
                            )
                            formik.setFieldValue(
                              'bankBranchCode',
                              value?.code ?? ''
                            )
                          }}
                          getOptionLabel={(option) => option.name}
                          isOptionEqualToValue={(option, value) =>
                            option.code === value.code
                          }
                          renderInput={(params) => (
                            <TextField
                              hiddenLabel
                              {...params}
                              placeholder="Select branch"
                              error={Boolean(
                                formik.touched.bankBranchName &&
                                  formik.errors.bankBranchName
                              )}
                              helperText={
                                formik.touched.bankBranchName &&
                                formik.errors.bankBranchName
                                  ? formik.errors.bankBranchName
                                  : ''
                              }
                            />
                          )}
                        />
                      </Stack>

                      <Stack sx={{ width: '50%' }}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Branch Code
                        </Typography>
                        <TextField
                          disabled
                          hiddenLabel
                          size="small"
                          type="text"
                          placeholder="001"
                          sx={{ marginBlock: '0' }}
                          margin={'normal'}
                          {...formik.getFieldProps('bankBranchCode')}
                          fullWidth
                          error={Boolean(
                            formik.touched.bankBranchCode &&
                              formik.errors.bankBranchCode
                          )}
                          helperText={
                            formik.touched.bankBranchCode &&
                            formik.errors.bankBranchCode
                          }
                        />
                      </Stack>
                    </Stack>
                  )}

                  <Stack>
                    <Typography variant="body2" sx={{ fontWeight: 600 }}>
                      Swift Code
                    </Typography>
                    <TextField
                      disabled
                      hiddenLabel
                      size="small"
                      type="text"
                      placeholder="KENXBNK"
                      sx={{ marginBlock: '0' }}
                      margin={'normal'}
                      {...formik.getFieldProps('swiftCode')}
                      fullWidth
                      error={Boolean(
                        formik.touched.swiftCode && formik.errors.swiftCode
                      )}
                      helperText={
                        formik.touched.swiftCode && formik.errors.swiftCode
                      }
                    />
                  </Stack>
                </>
              )}

              <Stack direction="row" spacing={2} sx={{ width: '100%' }}>
                <Button
                  sx={{ width: '40%' }}
                  variant="outlined"
                  onClick={() => setStep(1)}
                >
                  Back
                </Button>

                <AccessControlWrapper
                  rights={ACCESS_CONTROLS.CREATE_ORGANIZATION}
                  makerId={onboardingCompany?.maker}
                  isMake={true}
                >
                  <Button
                    fullWidth
                    variant="contained"
                    type="submit"
                    disabled={!formik.isValid || isLoading}
                    endIcon={
                      isLoading ? (
                        <CircularProgress size={20} thickness={3.0} />
                      ) : undefined
                    }
                  >
                    Next
                  </Button>
                </AccessControlWrapper>
              </Stack>
            </Stack>
          </Form>
        </FormikProvider>
      </Stack>
    </Stack>
  )
}

export default PaymentDetails
