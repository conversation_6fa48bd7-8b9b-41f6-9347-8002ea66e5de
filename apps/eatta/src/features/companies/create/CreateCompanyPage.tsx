/**
 * <AUTHOR> on 23/10/2024
 */

'use client'
import { Box, Divider, Stack, Typography } from '@mui/material'
import { BackButton } from '@dtbx/ui/components/Button'
import { useRouter } from 'next/navigation'
import React, { FC, useEffect, useMemo, useState } from 'react'
import Grid from '@mui/material/Grid2'
import Profile from '@/features/companies/create/Profile'
import PaymentDetails from '@/features/companies/create/PaymentDetails'
import Complete from '@/features/companies/create/Complete'
import DetailsVerification from '@/features/companies/create/DetailsVerification'
import { CustomStepper } from '@/components/CustomStepper'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  Company,
  CompanyTypeExtra,
  OnboardingStep,
  OrganizationSetUpStage,
} from '@/store/interfaces'
import { ApprovalRequestStatus } from '@/store/interfaces/makerChecker'
import { safeJsonParse } from '@/utils/objectUtil'
import { checkIsMaker, HasA<PERSON>essToRights } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { getOrganizationById } from '@/store/actions'
import { CreateUserSteps } from '@/features/companies/users/create/CreateUserPage'

export enum CreateCompanySteps {
  PROFILE = 1,
  PAYMENT,
  SUBMISSION,
  COMPLETE,
}

const STEPS: OnboardingStep[] = [
  {
    title: 'Create Company profile',
    description:
      'Provide Company registration details and supporting documents.',
  },
  {
    title: 'Payment Details',
    description: 'Accounts and payment methods, schedule.',
  },
  {
    title: 'Confirm and Submit',
    description: 'Review company profile before submission.',
  },
  {
    title: 'Complete',
    description: 'Company is onboarded successfully.',
  },
]

interface CreateCompanyPage {
  type?: CompanyTypeExtra
  organizationId?: string
}

const CreateCompanyPage: FC<CreateCompanyPage> = ({ type, organizationId }) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const [currentStep, setCurrentStep] = useState<CreateCompanySteps>(1)

  const isFactory = type === 'Factory'

  const { onboardingCompany, onboardedCompany } = useAppSelector(
    (state) => state.companies
  )
  const company: Company | null = useMemo(
    () => safeJsonParse(onboardingCompany?.entity),
    [onboardingCompany]
  )

  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_ORGANIZATION),
    []
  )

  const steps: OnboardingStep[] = useMemo(() => {
    if (isSuper) {
      return STEPS
    }
    return STEPS.filter((_step, index) => index !== 3)
  }, [isSuper])

  const handleSetNextStep = (
    step: OrganizationSetUpStage,
    status: ApprovalRequestStatus
  ) => {
    //Maker
    if (status === 'STAGING' && !onboardedCompany) {
      switch (step) {
        case 'PROFILE':
          setCurrentStep(CreateCompanySteps.PAYMENT)
          break
        case 'PAYMENT':
          setCurrentStep(CreateCompanySteps.SUBMISSION)
          break
      }
      return
    }
    //Checker
    if (onboardedCompany && !isSuper) {
      setCurrentStep(CreateCompanySteps.COMPLETE)
    }
  }

  useEffect(() => {
    if (organizationId) {
      getOrganizationById(dispatch, organizationId)
    }
  }, [organizationId])

  useEffect(() => {
    const step = company?.stepName
    const status = onboardingCompany?.status
    if (step && status) {
      handleSetNextStep(step, status)
    }
  }, [onboardingCompany, company])

  return (
    <Stack
      sx={{
        backgroundColor: '#F2F4F7',
        height: '100%',
      }}
    >
      <Stack
        sx={{ backgroundColor: '#FFFFFF' }}
        direction="row"
        paddingInline={3}
        paddingBlock={2}
        spacing={2}
        alignItems="flex-start"
      >
        <BackButton onClick={() => router.back()} />
        <Typography
          variant="h5"
          sx={{
            fontWeight: 600,
            color: '#000A12',
          }}
        >
          Add New {type ? type : 'Company'}
        </Typography>
      </Stack>

      <Divider />

      <Box sx={{ flexGrow: 1, height: '100%', overflow: 'hidden' }}>
        <Grid
          container
          spacing={2}
          sx={{ height: '100%', overflowY: { sm: 'auto', md: 'hidden' } }}
        >
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 4, lg: 3 }}
            sx={{ height: { md: '100%' }, overflowY: 'auto' }}
          >
            <CustomStepper
              steps={steps}
              currentStep={currentStep}
              setStep={(step: CreateCompanySteps) => setCurrentStep(step)}
              isComplete={currentStep === CreateCompanySteps.COMPLETE}
            />
          </Grid>
          <Grid
            padding={3}
            size={{ xs: 12, sm: 12, md: 8, lg: 9 }}
            sx={{ height: { md: '100%' }, overflowY: { md: 'auto' } }}
          >
            <Box width={{ sm: '100%', md: '100%', lg: '70%' }}>
              {(() => {
                switch (currentStep) {
                  case CreateCompanySteps.PROFILE:
                    return (
                      <Profile isFactory={isFactory} setStep={setCurrentStep} />
                    )
                  case CreateCompanySteps.PAYMENT:
                    return <PaymentDetails setStep={setCurrentStep} />
                  case CreateCompanySteps.SUBMISSION:
                    return (
                      <DetailsVerification
                        actionType="SUBMISSION"
                        setStep={setCurrentStep}
                      />
                    )
                  case CreateCompanySteps.COMPLETE:
                    return <Complete isFactory={isFactory} />
                }
              })()}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Stack>
  )
}

export default CreateCompanyPage
