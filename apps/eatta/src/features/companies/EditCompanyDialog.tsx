import React, { useEffect, useMemo, useState } from 'react'
import {
  Autocomplete,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import * as Yup from 'yup'
import { Form, FormikProvider, useFormik } from 'formik'
import {
  Bank,
  BankBranch,
  CHANNELS,
  COMPANY_TYPES,
  CompanyType,
  CURRENCIES,
  Currency,
  PartialCompanyData,
  PaymentChannel,
} from '@/store/interfaces'
import { useAppSelector } from '@/store'
import CloseIcon from '@mui/icons-material/Close'
import { AddFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { FileUpload } from '@/components/FileUpload'
import {
  fetchBanks,
  updateCompany,
  uploadOnboardingDocuments,
} from '@/store/actions'
import { useAppDispatch } from '@dtbx/store'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { matchIsValidTel, MuiTelInput } from 'mui-tel-input'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { ALPHA_NUMERIC_REGEX, ORG_NAME_REGEX } from '@/utils/validators'
import { NUMERIC_REGEX } from '@/utils/validators'

const validationSchema = Yup.object({
  type: Yup.string<CompanyType>().required('Company type should not be empty'),
  code: Yup.string()
    .required('Membership code not be empty')
    .matches(ALPHA_NUMERIC_REGEX, 'Only alphanumeric characters are allowed')
    .min(3, 'Membership code must be at least 3 characters'),
  name: Yup.string()
    .required('Company name should not be empty')
    .matches(
      ORG_NAME_REGEX,
      'Only letters, numbers, spaces, and brackets are allowed. No leading or trailing spaces.'
    )
    .test(
      'alpha',
      'Name must contain at least three letter.',
      (value) => (value.match(/[a-zA-Z]/g) || []).length >= 3
    ),
  phoneNumber: Yup.string()
    .required('Phone number should not be empty')
    .test('isValidPhone', 'Phone number is invalid', (value) =>
      matchIsValidTel(value)
    ),
  emailAddress: Yup.string()
    .required('Email should not be empty')
    .email('Enter valid email'),
  logo: Yup.mixed().nullable(),
  file: Yup.mixed().required('File is required'),
  bankName: Yup.string().required('Bank should not be empty'),
  bankAccountNumber: Yup.string()
    .required('Account number should not be empty')
    .matches(NUMERIC_REGEX, 'Account number should be a number')
    .min(5, 'Account number should be at least 5 digits')
    .max(16, 'Account number should have max of 16 digits'),
  currency: Yup.string().required('Currency should not be empty'),
  swiftCode: Yup.string().required('Swift code should not be empty'),
})

interface EditCompanyDialogProps {
  open: boolean
  handleClose: () => void
  CompanyData: PartialCompanyData
}

export const EditCompanyForm = ({
  onEditSuccess,
  onCancel,
  CompanyData,
}: {
  onEditSuccess: () => void
  onCancel: () => void
  CompanyData: PartialCompanyData | null
}) => {
  const dispatch = useAppDispatch()
  const isFactory = false
  const {
    isLoading,
    onboardingCompany,
    banks,
    bankBranches,
    isUpdatingCompany,
  } = useAppSelector((state) => state.companies)

  const [logoUrl, setLogoUrl] = useState<string | undefined>(
    CompanyData?.logoUrl
  )
  const [documentUrl, setDocumentUrl] = useState<string | undefined>(
    CompanyData?.certificateUrl
  )
  const [bank, setBank] = React.useState<Bank | null>(null)
  const [branch, setBranch] = React.useState<BankBranch | null>(null)
  const [currency, setCurrency] = React.useState<Currency | null>(null)
  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_UPDATE_ORGANIZATION),
    []
  )

  const handlePhoneChange = (phone: string) => {
    formik.setFieldTouched('phoneNumber', true, true)
    formik.setFieldValue('phoneNumber', phone)
  }
  useEffect(() => {
    if (!CompanyData) return

    // Find bank based on name
    const matchedBank =
      banks.find((b) => b.bankName === CompanyData.bankName) ?? null

    setBank(matchedBank)

    // Find branch using code
    const matchedBranch =
      bankBranches.find((b) => b.code === CompanyData.bankBranchCode) ?? null
    setBranch(matchedBranch)

    // Find currency using code
    const matchedCurrency =
      CURRENCIES.find((c) => c.code === CompanyData.accountCurrency) ?? null
    setCurrency(matchedCurrency)

    // Set other formik fields if needed
    formik.setFieldValue('channel', CompanyData.channel ?? '')
    formik.setFieldValue('swiftCode', CompanyData.swiftCode ?? '')
    formik.setFieldValue('bankBranchCode', CompanyData.bankBranchCode ?? '')
  }, [CompanyData, banks, bankBranches])

  useEffect(() => {
    fetchBanks(dispatch)
  }, [])
  const handleSubmitCompany = async (payload: PartialCompanyData) => {
    if (CompanyData?.id) {
      await updateCompany(isSuper, dispatch, CompanyData?.id, payload, () =>
        onEditSuccess()
      )
    }
  }
  console.log('IS SUPER >', isSuper)
  const handleBankChange = (value: Bank | null) => {
    setBank(value)
    formik.setFieldValue('bankName', value?.bankName ?? '')
    formik.setFieldValue('swiftCode', value?.bicCode ?? '')
    formik.setFieldValue('channel', value?.bicCode === 'DTKEKENA' ? 'IFT' : '')
  }

  const formik = useFormik({
    initialValues: {
      type: CompanyData?.type ?? '',
      code: CompanyData?.code ?? '',
      name: CompanyData?.name ?? '',
      phoneNumber: CompanyData?.phoneNumber ?? '',
      emailAddress: CompanyData?.emailAddress ?? '',
      file: undefined,
      logo: undefined,
      bankName: CompanyData?.bankName ?? '',
      bankBranchName: CompanyData?.bankBranchName ?? '',
      bankBranchCode: CompanyData?.bankBranchCode ?? '',
      bankAccountNumber: CompanyData?.accountNumber ?? '',
      currency: CompanyData?.accountCurrency ?? '',
      channel: CompanyData?.channel ?? '',
      swiftCode: CompanyData?.swiftCode ?? '',
      makerStep: 'SUBMISSION',
    },
    validateOnMount: true,
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      if (!isFactory) {
        try {
          const { file, logo, ...otherValues } = values
          const payload = {
            ...otherValues,
            certificateUrl: documentUrl,
            logoUrl: logoUrl,
            approvalId: onboardingCompany?.id,
          } as PartialCompanyData

          //upload files
          const promises: Promise<string | undefined>[] = []
          const uploadedUrls: string[] = []

          if (!documentUrl && file) {
            promises.push(uploadOnboardingDocuments(dispatch, file as File, {}))
            uploadedUrls.push('document')
          }
          if (!logoUrl && logo) {
            promises.push(uploadOnboardingDocuments(dispatch, logo as File, {}))
            uploadedUrls.push('logo')
          }

          if (promises.length > 0) {
            const results = await Promise.all(promises)
            results.forEach((url, index) => {
              if (uploadedUrls[index] === 'document') {
                setDocumentUrl(url)
                payload.certificateUrl = url
              } else if (uploadedUrls[index] === 'logo') {
                setLogoUrl(url)
                payload.logoUrl = url
              }
            })
          }
          await handleSubmitCompany(payload)
        } catch (error) {
          console.error('Error during submission:', error)
        }

        return
      }
    },
  })

  return (
    <Box sx={{ maxWidth: '1000px', width: '100%', margin: '0 auto' }}>
      <Stack spacing={2} sx={{ height: '100%' }}>
        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack spacing={1.5}>
              <Stack
                spacing={3}
                sx={{
                  padding: 3,
                  backgroundColor: '#FFFFFF',
                  borderRadius: '0.5rem',
                }}
              >
                <Stack spacing={1.5}>
                  {!isFactory && (
                    <Stack direction="row" spacing={2}>
                      <Stack sx={{ width: '100%' }} spacing={1}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Select Company Type{' '}
                          <Box component="span" sx={{ color: 'primary.main' }}>
                            *
                          </Box>
                        </Typography>
                        <Autocomplete
                          disablePortal
                          size="small"
                          id="type"
                          options={COMPANY_TYPES}
                          {...formik.getFieldProps('type')}
                          onChange={(_, value) => {
                            formik.setFieldValue('type', value)
                          }}
                          renderInput={(params) => (
                            <TextField
                              hiddenLabel
                              placeholder="Select type"
                              {...params}
                              error={Boolean(
                                formik.touched.type || formik.errors.type
                              )}
                              helperText={
                                formik.touched.type || formik.errors.type
                                  ? formik.errors.type
                                  : ''
                              }
                            />
                          )}
                        />
                      </Stack>

                      <Stack sx={{ width: '100%' }} spacing={1}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Enter EATTA Membership Code{' '}
                          <Box component="span" sx={{ color: 'primary.main' }}>
                            *
                          </Box>
                        </Typography>
                        <TextField
                          hiddenLabel
                          size="small"
                          type="text"
                          placeholder="Enter membership code"
                          sx={{ marginBlock: '0' }}
                          margin={'normal'}
                          {...formik.getFieldProps('code')}
                          fullWidth
                          error={Boolean(
                            formik.touched.code || formik.errors.code
                          )}
                          helperText={formik.touched.code || formik.errors.code}
                        />
                      </Stack>
                    </Stack>
                  )}
                  <Stack direction="row" spacing={1.5}>
                    <Stack sx={{ width: '100%' }}>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        Enter the company name{' '}
                        <Box component="span" sx={{ color: 'primary.main' }}>
                          *
                        </Box>
                      </Typography>
                      <TextField
                        hiddenLabel
                        size="small"
                        type="text"
                        placeholder="Enter name"
                        sx={{ marginBlock: '0' }}
                        margin={'normal'}
                        {...formik.getFieldProps('name')}
                        fullWidth
                        error={Boolean(
                          formik.touched.name || formik.errors.name
                        )}
                        helperText={formik.touched.name || formik.errors.name}
                      />
                    </Stack>
                    <Stack sx={{ width: '100%' }}>
                      <FormControl
                        fullWidth
                        error={Boolean(
                          formik.touched.phoneNumber &&
                            formik.errors.phoneNumber
                        )}
                        sx={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: 1,
                        }}
                      >
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Company Phone number
                          <Box component="span" sx={{ color: 'primary.main' }}>
                            {isFactory ? '(optional)' : '*'}
                          </Box>
                        </Typography>
                        <MuiTelInput
                          hiddenLabel
                          size="small"
                          forceCallingCode={true}
                          value={formik.values.phoneNumber}
                          name="phoneNumber"
                          defaultCountry="KE"
                          onlyCountries={['KE', 'UG', 'TZ', 'BI']}
                          onChange={handlePhoneChange}
                          slotProps={{
                            htmlInput: {
                              maxLength: 15,
                            },
                          }}
                        />
                        {formik.touched.phoneNumber ||
                          (formik.errors.phoneNumber && (
                            <FormHelperText error>
                              {formik.errors.phoneNumber}
                            </FormHelperText>
                          ))}
                      </FormControl>
                    </Stack>
                  </Stack>

                  <Stack direction="row" spacing={1.5}>
                    <Stack sx={{ width: '100%' }}>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        Enter company email{' '}
                        <Box component="span" sx={{ color: 'primary.main' }}>
                          {isFactory ? '(optional)' : '*'}
                        </Box>
                      </Typography>
                      <TextField
                        hiddenLabel
                        size="small"
                        type="text"
                        placeholder="<EMAIL>"
                        sx={{ marginBlock: '0' }}
                        margin={'normal'}
                        {...formik.getFieldProps('emailAddress')}
                        fullWidth
                        error={Boolean(
                          formik.touched.emailAddress ||
                            formik.errors.emailAddress
                        )}
                        helperText={
                          formik.touched.emailAddress ||
                          formik.errors.emailAddress
                        }
                      />
                    </Stack>
                    <Stack sx={{ width: '100%' }}>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        Select Bank
                      </Typography>
                      <Autocomplete
                        disablePortal
                        size="small"
                        id="bankCode"
                        options={banks}
                        value={bank}
                        onChange={(_, value) => handleBankChange(value)}
                        getOptionLabel={(option) => option.bankName}
                        isOptionEqualToValue={(option, value) =>
                          option.bankCode === value.bankCode
                        }
                        renderInput={(params) => (
                          <TextField
                            hiddenLabel
                            {...params}
                            placeholder="Select bank"
                            error={Boolean(
                              formik.touched.bankName || formik.errors.bankName
                            )}
                            helperText={
                              formik.touched.bankName || formik.errors.bankName
                                ? formik.errors.bankName
                                : ''
                            }
                          />
                        )}
                      />
                    </Stack>
                  </Stack>
                  <Stack spacing={1.5}>
                    <Stack direction="row" spacing={1.5}>
                      <Stack sx={{ width: '50%' }}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Account Number
                        </Typography>
                        <TextField
                          hiddenLabel
                          size="small"
                          type="text"
                          placeholder="********"
                          sx={{ marginBlock: '0' }}
                          margin={'normal'}
                          {...formik.getFieldProps('bankAccountNumber')}
                          fullWidth
                          slotProps={{
                            input: {
                              inputProps: {
                                maxLength: 16,
                                minLength: 5,
                              },
                            },
                          }}
                          error={Boolean(
                            formik.touched.bankAccountNumber ||
                              formik.errors.bankAccountNumber
                          )}
                          helperText={
                            formik.touched.bankAccountNumber ||
                            formik.errors.bankAccountNumber
                          }
                        />
                      </Stack>

                      <Stack sx={{ width: '50%' }}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Currency
                        </Typography>
                        <Autocomplete
                          fullWidth
                          disablePortal
                          size="small"
                          id="currency"
                          options={CURRENCIES}
                          value={currency}
                          onChange={(_, value) => {
                            setCurrency(value)
                            formik.setFieldValue('currency', value?.code ?? '')
                          }}
                          getOptionLabel={(option) => option.name}
                          isOptionEqualToValue={(option, value) =>
                            option.code === value.code
                          }
                          renderInput={(params) => (
                            <TextField
                              hiddenLabel
                              {...params}
                              placeholder="Select currency"
                              error={Boolean(
                                formik.touched.currency &&
                                  formik.errors.currency
                              )}
                              helperText={
                                formik.touched.currency &&
                                formik.errors.currency
                                  ? formik.errors.currency
                                  : ''
                              }
                            />
                          )}
                        />
                      </Stack>
                    </Stack>

                    {CompanyData?.type !== 'Buyer' && (
                      <>
                        <Stack sx={{ width: '100%' }}>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            Channel
                          </Typography>
                          <Autocomplete
                            fullWidth
                            disablePortal
                            size="small"
                            id="channel"
                            options={CHANNELS.filter((c) =>
                              bank?.bicCode === 'DTKEKENA'
                                ? c === 'IFT'
                                : c !== 'IFT'
                            )}
                            {...formik.getFieldProps('channel')}
                            onChange={(_, value: PaymentChannel) => {
                              formik.setFieldValue('channel', value)
                            }}
                            renderInput={(params) => (
                              <TextField
                                hiddenLabel
                                {...params}
                                placeholder="Select channel"
                                error={Boolean(
                                  formik.touched.channel &&
                                    formik.errors.channel
                                )}
                                helperText={
                                  formik.touched.channel &&
                                  formik.errors.channel
                                    ? formik.errors.channel
                                    : ''
                                }
                              />
                            )}
                          />
                        </Stack>

                        {formik.values.channel === 'IFT' && (
                          <Stack direction="row" spacing={1.5}>
                            <Stack sx={{ width: '100%' }}>
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 600 }}
                              >
                                Branch Name
                              </Typography>
                              <Autocomplete
                                fullWidth
                                disablePortal
                                size="small"
                                id="bankBranchName"
                                options={bankBranches}
                                value={branch}
                                onChange={(_, value) => {
                                  setBranch(value)
                                  formik.setFieldValue(
                                    'bankBranchName',
                                    value?.name ?? ''
                                  )
                                  formik.setFieldValue(
                                    'bankBranchCode',
                                    value?.code ?? ''
                                  )
                                }}
                                getOptionLabel={(option) => option.name}
                                isOptionEqualToValue={(option, value) =>
                                  option.code === value.code
                                }
                                renderInput={(params) => (
                                  <TextField
                                    hiddenLabel
                                    {...params}
                                    placeholder="Select branch"
                                    error={Boolean(
                                      formik.touched.bankBranchName &&
                                        formik.errors.bankBranchName
                                    )}
                                    helperText={
                                      formik.touched.bankBranchName &&
                                      formik.errors.bankBranchName
                                        ? formik.errors.bankBranchName
                                        : ''
                                    }
                                  />
                                )}
                              />
                            </Stack>

                            <Stack sx={{ width: '50%' }}>
                              <Typography
                                variant="body2"
                                sx={{ fontWeight: 600 }}
                              >
                                Branch Code
                              </Typography>
                              <TextField
                                disabled
                                hiddenLabel
                                size="small"
                                type="text"
                                placeholder="001"
                                sx={{ marginBlock: '0' }}
                                margin={'normal'}
                                {...formik.getFieldProps('bankBranchCode')}
                                fullWidth
                                error={Boolean(
                                  formik.touched.bankBranchCode &&
                                    formik.errors.bankBranchCode
                                )}
                                helperText={
                                  formik.touched.bankBranchCode &&
                                  formik.errors.bankBranchCode
                                }
                              />
                            </Stack>
                          </Stack>
                        )}

                        <Stack>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            Swift Code
                          </Typography>
                          <TextField
                            disabled
                            hiddenLabel
                            size="small"
                            type="text"
                            placeholder="KENXBNK"
                            sx={{ marginBlock: '0' }}
                            margin={'normal'}
                            {...formik.getFieldProps('swiftCode')}
                            fullWidth
                            error={Boolean(
                              formik.touched.swiftCode &&
                                formik.errors.swiftCode
                            )}
                            helperText={
                              formik.touched.swiftCode &&
                              formik.errors.swiftCode
                            }
                          />
                        </Stack>
                      </>
                    )}
                  </Stack>
                </Stack>
              </Stack>
              {!isFactory && (
                <Stack spacing={1}>
                  <Typography variant="subtitle2" fontWeight={600}>
                    Attach Documents
                  </Typography>

                  <Stack
                    direction={{ xs: 'column', sm: 'row' }}
                    spacing={2}
                    sx={{ backgroundColor: '#fff', p: 1.5, borderRadius: 2 }}
                  >
                    <Stack flex={1} spacing={1}>
                      <Typography variant="body2" fontWeight={500}>
                        Company Logo (Optional)
                      </Typography>
                      <FileUpload
                        fileUrl={CompanyData?.logoUrl}
                        description="Only Images file allowed."
                        mimeTypes={['image/png', 'image/jpeg', 'image/jpg']}
                        disabled={isLoading}
                        required={true}
                        onFileChange={(file) =>
                          formik.setFieldValue('logo', file)
                        }
                        onFileDeleted={() => {
                          setDocumentUrl(undefined)
                          formik.setFieldValue('logoUrl', undefined)
                        }}
                      />
                    </Stack>

                    <Stack flex={1} spacing={1}>
                      <Typography variant="body2" fontWeight={500}>
                        EATTA Registration Document
                      </Typography>

                      <FileUpload
                        fileUrl={CompanyData?.certificateUrl}
                        description="Only PDF file allowed."
                        mimeTypes={['application/pdf']}
                        disabled={isLoading}
                        required={true}
                        onFileChange={(file) =>
                          formik.setFieldValue('file', file)
                        }
                        onFileDeleted={() => {
                          setDocumentUrl(undefined)
                          formik.setFieldValue('certificateUrl', undefined)
                        }}
                      />
                    </Stack>
                  </Stack>
                </Stack>
              )}

              <Stack
                direction="row"
                spacing={2}
                sx={{
                  mt: 3,
                  width: '100%',
                }}
              >
                <Button
                  sx={{ width: '40%' }}
                  disabled={isUpdatingCompany}
                  variant="outlined"
                  onClick={onCancel}
                >
                  Cancel
                </Button>
                <AccessControlWrapper
                  rights={ACCESS_CONTROLS.UPDATE_ORGANIZATION}
                >
                  <Button
                    fullWidth
                    variant="contained"
                    type="submit"
                    disabled={!formik.isValid || isUpdatingCompany}
                    endIcon={
                      isUpdatingCompany ? (
                        <CircularProgress size={20} thickness={3.0} />
                      ) : undefined
                    }
                  >
                    Update
                  </Button>
                </AccessControlWrapper>
              </Stack>
            </Stack>
          </Form>
        </FormikProvider>
      </Stack>
    </Box>
  )
}

export const EditCompanyDialog = ({
  open,
  handleClose,
  CompanyData,
}: EditCompanyDialogProps) => {
  return (
    <Dialog maxWidth="md" open={open} onClose={handleClose} fullWidth>
      <DialogTitle fontWeight={600}>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            spacing={1}
          >
            <AddFactoryIcon height={32} width={32} />
            <Typography variant="subtitle1" fontWeight="bold">
              Edit Company - {CompanyData.name}
            </Typography>
          </Stack>
          <IconButton
            sx={{
              height: '2rem',
              width: '2rem',
              border: '1px solid #D0D5DD',
              borderRadius: '0.5rem',
            }}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>
      <DialogContent>
        <EditCompanyForm
          onEditSuccess={handleClose}
          onCancel={handleClose}
          CompanyData={CompanyData}
        />
      </DialogContent>
    </Dialog>
  )
}
