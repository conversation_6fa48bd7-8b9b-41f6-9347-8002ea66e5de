'use client'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>on, Stack, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { getOrganizationById, getGodowns } from '@/store/actions'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import ListGodowns from './ListGodowns'
import { CreateGodownsDialog } from './CreateGodownsDialog'
import { AddFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { GodownsFilters } from '@/store/interfaces/warehouse'
import { PageFilters } from '@/store/interfaces'
import { CustomFilterUserBox } from '@/components/SearchFilters'

const initialFilters: GodownsFilters = {
  page: 1,
  size: 10,
  name: '',
  godownCode: '',
  warehouseId: '',
}

type WarehouseSearchByType = keyof Omit<GodownsFilters, keyof PageFilters>

export default function WarehousesPage({
  organizationId,
}: {
  organizationId: string
}) {
  const router = useCustomRouter()
  const [openWarehouseDialog, setOpenWarehouseDialog] = useState(false)
  const dispatch = useAppDispatch()

  const { selectedCompany, warehousesResponse } = useAppSelector(
    (state) => state.companies
  )

  const paginationOptions = {
    page: 1,
    size: 10,
    totalPages: warehousesResponse.totalNumberOfPages,
  }

  const [filters, setFilters] = useState<GodownsFilters>(initialFilters)

  const [searchBy, setSearchBy] = useState<WarehouseSearchByType>('name')
  const searchByValue = Object.keys(filters).filter(
    (key) =>
      typeof filters[key as keyof GodownsFilters] === 'string' &&
      key !== 'warehouseId'
  )
  const [searchValue, setSearchValue] = useState('')
  const debouncedSearchValue = useDebounce(searchValue, 500)

  useEffect(() => {
    handleChange(searchBy, debouncedSearchValue)
  }, [debouncedSearchValue, searchBy])

  const handleChange = (field: keyof GodownsFilters, value: string) => {
    setFilters({
      ...initialFilters,
      [field]: value,
    })
  }

  const fetGodowns = async () => {
    await getGodowns(dispatch, {
      ...filters,
      page: paginationOptions.page,
      size: paginationOptions.size,
      warehouseId: organizationId || '',
    })
  }

  const handleAddWarehouse = () => {
    setOpenWarehouseDialog(true)
  }

  const handleCloseWarehouseDialog = (refresh?: boolean) => {
    setOpenWarehouseDialog(false)
    if (refresh) {
      fetGodowns()
    }
  }

  useEffect(() => {
    if (organizationId) {
      getOrganizationById(dispatch, organizationId)
    }
  }, [organizationId])

  useEffect(() => {
    fetGodowns()
  }, [filters])

  return (
    <Stack>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'start',
            gap: '8px',
          }}
        >
          <IconButton
            sx={{
              borderRadius: '8px',
              border: '1px solid #D0D5DD',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            onClick={() => router.back()}
          >
            <ArrowBackIosNewOutlinedIcon />
          </IconButton>
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: 600,
            }}
          >
            {selectedCompany?.name || ''}
          </Typography>
        </Stack>
      </Stack>
      <Divider />
      <Stack
        spacing={1}
        paddingInline={3}
        paddingBlock={2}
        flexWrap="wrap"
        sx={{
          backgroundColor: '#FFFFFF',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Stack direction="row" spacing={3}>
          <CustomFilterUserBox
            searchValue={searchValue}
            searchByValues={searchByValue}
            selectedSearchBy={searchBy}
            onHandleSearch={(e) => setSearchValue(e.target.value)}
            setSearchByValue={(value: string) =>
              setSearchBy(value as WarehouseSearchByType)
            }
            searchPlaceHolder={'Search warehouse'}
            prependSearchBy={true}
          />
        </Stack>
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddFactoryIcon stroke="#FFFFFF" />}
          sx={{ borderRadius: '0.5rem', textWrap: 'nowrap' }}
          onClick={handleAddWarehouse}
        >
          Add New Godown
        </Button>
      </Stack>
      <Divider />

      <Stack>
        <ListGodowns />
      </Stack>
      <CreateGodownsDialog
        open={openWarehouseDialog}
        handleClose={handleCloseWarehouseDialog}
        organizationName={selectedCompany?.name || ''}
        organizationId={selectedCompany?.id || ''}
      />
    </Stack>
  )
}
