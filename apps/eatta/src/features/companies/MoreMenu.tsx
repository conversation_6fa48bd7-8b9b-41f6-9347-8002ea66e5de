/**
 * <AUTHOR> on 20/12/2024
 */
'use client'
import { IconButton } from '@mui/material'
import React, { useState } from 'react'
import { DotsVerticalIcon } from '@dtbx/ui/icons'
import { CustomMenu, StyledMenuItem } from '@/components/CustomMenu'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch } from '@/store'
import { Company, PartialCompanyData } from '@/store/interfaces'
import { resetOnboardingState, setSelectedCompany } from '@/store/reducers'
import { CreateFactoryDialog } from './factories/CreateFactoryDialog'
import { AddUserIcon, ManageUserIcon } from '@/components/SvgIcons/UserIcon'
import {
  AddFactoryIcon,
  ManageFactoryIcon,
  EditFactoryIcon,
} from '@/components/SvgIcons/FactoryIcon'
import { CreateGodownsDialog } from '@/features/companies/godowns/CreateGodownsDial<PERSON>'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { EditCompanyDialog } from './EditCompanyDialog'

export interface MenuItem {
  id: string
  label: string
  onClick: () => void
  icon: React.ReactNode
  show?: boolean
  requiresAccess?: boolean
  rights?: string[]
}

export const CompanyMoreMenu = ({ company }: { company: Company }) => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const [showCreateFactory, setShowCreateFactory] = useState(false)
  const [showCreateWarehouse, setShowCreateWarehouse] = useState(false)
  const [showEditCompany, setShowEditCompany] = useState(false)
  const [CompanyData, setCompanyData] = useState<PartialCompanyData>()
  const isProducer = company.type === 'Producer'
  const isWarehouse = company.type === 'Warehouse'

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
    setShowCreateFactory(false)
    setShowCreateWarehouse(false)
    setShowEditCompany(false)
  }

  const handleManageUser = (organizationId: string) => {
    dispatch(setSelectedCompany(company))
    router.push(`/backoffice/companies/${organizationId}/users`)
  }

  const handleManageFactory = (organizationId: string) => {
    dispatch(setSelectedCompany(company))
    router.push(`/backoffice/companies/${organizationId}/factories`)
  }

  const handleCreateFactory = (organizationId: string) => {
    dispatch(resetOnboardingState())
    dispatch(setSelectedCompany(company))
    router.push(`/backoffice/companies/${organizationId}/factories/create`)
  }

  const handleManageGodowns = (organizationId: string) => {
    dispatch(setSelectedCompany(company))
    router.push(`/backoffice/companies/${organizationId}/warehouses`)
  }

  const handleCreateUser = (organizationId: string) => {
    dispatch(resetOnboardingState())
    dispatch(setSelectedCompany(company))
    router.push(`/backoffice/companies/${organizationId}/users/create`)
  }
  const handleEditCompany = (organization: PartialCompanyData) => {
    setShowEditCompany(true)
    if (organization) {
      setCompanyData(organization)
    }
  }

  const menuItems: MenuItem[] = [
    {
      id: 'edit-company',
      label: 'Edit Company',
      onClick: () => handleEditCompany(company),
      icon: <EditFactoryIcon />,
      requiresAccess: true,
      rights: ACCESS_CONTROLS.UPDATE_ORGANIZATION,
    },
    {
      id: 'manage-users',
      label: 'Manage Users',
      onClick: () => handleManageUser(company.id),
      icon: <ManageUserIcon />,
    },
    {
      id: 'add-users',
      label: 'Add Users',
      onClick: () => handleCreateUser(company.id),
      icon: <AddUserIcon />,
      requiresAccess: true,
      rights: ACCESS_CONTROLS.CREATE_USER,
    },
    {
      id: 'manage-godowns',
      label: 'Manage Godowns',
      onClick: () => handleManageGodowns(company.id),
      icon: <ManageFactoryIcon />,
      show: isWarehouse,
    },
    {
      id: 'add-godown',
      label: 'Add New Godown',
      onClick: () => setShowCreateWarehouse(true),
      icon: <AddFactoryIcon />,
      show: isWarehouse,
    },
    {
      id: 'manage-factories',
      label: 'Manage Factories',
      onClick: () => handleManageFactory(company.id),
      icon: <ManageFactoryIcon />,
      show: isProducer,
    },
    {
      id: 'add-factory',
      label: 'Add New Factory',
      onClick: () => handleCreateFactory(company.id),
      icon: <AddFactoryIcon />,
      show: isProducer,
      requiresAccess: true,
      rights: ACCESS_CONTROLS.CREATE_ORGANIZATION,
    },
  ]

  return (
    <>
      <IconButton onClick={handleClick}>
        <DotsVerticalIcon />
      </IconButton>

      <CustomMenu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={{
          list: {
            'aria-labelledby': 'basic-button',
          },
        }}
      >
        {menuItems.map((item) => {
          if (item.show === false) return null

          const menuItem = (
            <StyledMenuItem key={item.id} onClick={item.onClick}>
              {item.label}
              {item.icon}
            </StyledMenuItem>
          )

          return item.requiresAccess && item.rights ? (
            <AccessControlWrapper key={item.id} rights={item.rights}>
              {menuItem}
            </AccessControlWrapper>
          ) : (
            menuItem
          )
        })}
      </CustomMenu>

      <CreateFactoryDialog
        organizationName={company.name}
        organizationId={company.id}
        open={showCreateFactory}
        handleClose={handleClose}
      />
      <CreateGodownsDialog
        organizationName={company.name}
        organizationId={company.id}
        open={showCreateWarehouse}
        handleClose={handleClose}
      />
      <EditCompanyDialog
        open={showEditCompany}
        handleClose={handleClose}
        CompanyData={CompanyData || {}}
      />
    </>
  )
}
