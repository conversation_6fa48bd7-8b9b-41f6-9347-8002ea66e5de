import { But<PERSON>, Di<PERSON><PERSON>, I<PERSON><PERSON><PERSON>on, Stack, Typography } from '@mui/material'
import React, { FC, useEffect, useMemo, useState } from 'react'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import { useAppDispatch, useAppSelector } from '@/store'
import { CustomFilterUserBox } from '@/components/SearchFilters'
import { PageFilters, UserFilters } from '@/store/interfaces'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { resetOnboardingState } from '@/store/reducers'
import { SearchByValueConfig } from '@/components/PageFilters'
import { ApprovalRequestFilters } from '@/store/interfaces/makerChecker'

interface UserHeaderProps {
  onSearchChange: (
    key: keyof UserFilters | keyof ApprovalRequestFilters,
    value: string
  ) => void
  searchByValues:
    | SearchByValueConfig<UserFilters>[]
    | SearchByValueConfig<ApprovalRequestFilters>[]
  initialSearchBy: UserSearchByType
}

type UserSearchByType =
  | keyof Omit<UserFilters, keyof PageFilters>
  | keyof Omit<ApprovalRequestFilters, keyof PageFilters>

export const UserHeader: FC<UserHeaderProps> = ({
  onSearchChange,
  searchByValues,
  initialSearchBy,
}: UserHeaderProps) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { selectedCompany } = useAppSelector((state) => state.companies)

  const [searchBy, setSearchBy] = useState<UserSearchByType>(initialSearchBy)

  const searchByLabel = useMemo(() => {
    const map: Record<string, string> = {}
    searchByValues.forEach((value) => {
      map[String(value.filterKey)] =
        value.filterLabel || String(value.filterKey)
    })
    return map
  }, [searchByValues])

  const [searchValue, setSearchValue] = useState('')
  const debouncedSearchValue = useDebounce(searchValue, 500)

  const handleCreateUser = (organizationId: string) => {
    dispatch(resetOnboardingState())
    router.push(`/backoffice/companies/${organizationId}/users/create`)
  }

  useEffect(() => {
    onSearchChange(searchBy, debouncedSearchValue)
  }, [debouncedSearchValue, searchBy])

  useEffect(() => {
    setSearchBy(initialSearchBy)
  }, [initialSearchBy])

  return (
    <Stack>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <IconButton
            sx={{
              borderRadius: '8px',
              border: '1px solid #D0D5DD',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            onClick={() => router.back()}
          >
            <ArrowBackIosNewOutlinedIcon />
          </IconButton>
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            {selectedCompany?.name}
          </Typography>
        </Stack>
      </Stack>
      <Divider />
      <Stack
        sx={{
          backgroundColor: '#FFFFFF',
          justifyContent: 'space-between',
        }}
        direction="row"
        spacing={2}
        paddingInline={3}
        paddingBlock={2}
        alignItems="center"
        justifyContent="flex-start"
      >
        <CustomFilterUserBox
          searchValue={searchValue}
          searchByValues={searchByValues.map(
            (item) => item.filterKey as string
          )}
          searchByLabel={searchByLabel}
          selectedSearchBy={searchBy}
          onHandleSearch={(e) => setSearchValue(e.target.value)}
          setSearchByValue={(value: string) =>
            setSearchBy(value as UserSearchByType)
          }
          searchPlaceHolder={`Search ${searchByLabel?.[searchBy] || searchBy || 'member'}`}
          prependSearchBy={true}
        />
        <AccessControlWrapper rights={ACCESS_CONTROLS.CREATE_USER}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddOutlinedIcon />}
            sx={{ borderRadius: '8px' }}
            onClick={() => handleCreateUser(selectedCompany?.id ?? '')}
          >
            Add User
          </Button>
        </AccessControlWrapper>
      </Stack>
    </Stack>
  )
}
