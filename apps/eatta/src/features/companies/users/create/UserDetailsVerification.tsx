import {
  <PERSON>,
  Button,
  Cir<PERSON>Progress,
  Divider,
  Stack,
  Typography,
} from '@mui/material'
import Grid from '@mui/material/Grid2'
import React, {useMemo, useState } from 'react'
import MakerCheckerActivities from '../../../../components/MakerCheckerActivities'
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline'
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight'
import { DownloadPng } from '@/components/SvgIcons/DownloadPng'
import { CompanyUser, MakeRequest } from '@/store/interfaces/Company'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  approveUser,
  createUser,
  downloadOnboardingDocuments,
} from '@/store/actions'
import { useRouter } from 'next/navigation'
import { CreateUserSteps } from '@/features/companies/users/create/CreateUserPage'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { ACCESS_CONTROLS } from '@/utils/constants'
import { CommentDialog } from '@/components/CommentDialog'
import { safeJsonParse } from '@/utils/objectUtil'
import { ApproveMemberSteps } from '@/features/members/approve/ApproveMemberCreation'

interface DetailsVerificationProps {
  actionType: 'SUBMISSION' | 'CHECKER'
  setStep: (step: number) => void
}

const UserDetailsVerification: React.FC<DetailsVerificationProps> = ({
  setStep,
  actionType = 'SUBMISSION',
}) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const { onboardingUser, isCreatingUser, selectedCompany } = useAppSelector(
    (state) => state.companies
  )
  const user: CompanyUser | null = useMemo(
    () => safeJsonParse(onboardingUser?.entity),
    [onboardingUser]
  )
  const isSuper = useMemo(
    () => HasAccessToRights(ACCESS_CONTROLS.SUPER_CREATE_USER),
    []
  )

  const isConfirmAction = actionType === 'SUBMISSION'
  const [openCommentDialog, setOpenCommentsDialog] = useState(false)

  const handleNextStep = async () => {
    if (!onboardingUser) return
    //confirmation
    if (isConfirmAction) {
      const payload: MakeRequest = {
        stepName: actionType,
        approvalId: onboardingUser?.id,
      }
      await createUser(isSuper, dispatch, payload, handleConfirmSuccess)
      return
    }
    //Approval
    setOpenCommentsDialog(true)
  }

  const handleConfirmSuccess = () => {
    if (isSuper) {
      setStep(CreateUserSteps.COMPLETE)
      return
    }

    router.replace(`/backoffice/companies/${selectedCompany?.id}/users?tab=2`)
  }

  const handleOnSubmitCommentsDialog = async (comment: string) => {
    setOpenCommentsDialog(false)
    if (!onboardingUser) return
    const parsedEntity = JSON.parse(onboardingUser.entity);
  
    const requestType = onboardingUser.makerCheckerType.type;

   const userId = parsedEntity.userId;

    await approveUser(dispatch, onboardingUser.id, comment, () =>
      setStep(
        isConfirmAction ? CreateUserSteps.COMPLETE : ApproveMemberSteps.COMPLETE
      ),requestType,userId
    )
  }

  const handleDownload = async (url: string) => {
    await downloadOnboardingDocuments(dispatch, url, true)
  }

  return (
    <Stack spacing={3}>
      <Stack>
        <Stack
          width="100%"
          spacing={3}
          sx={{
            backgroundColor: 'primary.main',
            padding: 3,
            borderRadius: '0.5rem 0.5rem 0  0',
          }}
        >
          <Stack
            direction="row"
            alignItems="center"
            spacing={2}
            flexWrap="wrap"
            justifyContent="space-between"
          >
            <Stack spacing={2} direction="row" alignItems="center">
              <Typography
                variant="subtitle1"
                fontWeight={600}
                sx={{ color: '#FFFFFF' }}
              >
                {user?.firstName} {user?.middleName} {user?.lastName}
              </Typography>
              <Box
                sx={{
                  backgroundColor: '#F0F0F0',
                  color: '#000000',
                  px: 1.5,
                  borderRadius: '999px',
                  fontSize: '0.75rem',
                  fontWeight: 500,
                }}
              >
                {user?.isCompanyAdmin ? 'Contact Person' : 'Team Member'}
              </Box>
            </Stack>
          </Stack>

          <Stack
            spacing={2}
            direction="row"
            useFlexGap
            sx={{ justifyContent: 'space-between', flexWrap: 'wrap' }}
          >
            <Stack sx={{ minWidth: '200px' }}>
              <Typography
                variant="subtitle2"
                sx={{ color: '#FFFFFF' }}
                fontWeight="600"
              >
                Email
              </Typography>
              <Typography sx={{ color: '#FFFFFF' }} fontWeight="500">
                {user?.email || ''}
              </Typography>
            </Stack>

            <Box sx={{ minWidth: '200px' }}>
              <Typography
                variant="subtitle2"
                sx={{ color: '#FFFFFF' }}
                fontWeight="600"
              >
                Phone Number
              </Typography>
              <Typography sx={{ color: '#FFFFFF' }} fontWeight="500">
                {user?.phoneNumber || ''}
              </Typography>
            </Box>
          </Stack>
        </Stack>

        <Stack
          spacing={3}
          sx={{
            padding: 3,
            backgroundColor: '#FFFFFF',
            borderRadius: '0 0 0.5rem 0.5rem',
          }}
        >
          <Box>
            <Grid
              container
              spacing={4}
              sx={{
                fontWeight: 'bold',
                paddingBlock: '0.8rem',
                backgroundColor: '#FFFFFF',
                display: 'flex',
                flexDirection: 'row',
                flexWrap: 'wrap',
              }}
            >
              <Grid>
                <Typography
                  variant="subtitle3"
                  sx={{
                    fontWeight: 500,
                    fontSize: '0.75rem',
                    color: '#344054',
                  }}
                >
                  NATIONAL ID
                </Typography>
                <Stack direction="row" alignItems="center" gap={1}>
                  <Typography
                    sx={{ fontWeight: 500, fontSize: '1rem', color: '#000A12' }}
                  >
                    {user?.nationalId}
                  </Typography>
                  <CheckCircleOutlineIcon
                    fontSize="small"
                    sx={{ color: '#00BC2D' }}
                  />
                </Stack>
              </Grid>
              <Grid>
                <Typography
                  variant="subtitle3"
                  sx={{
                    fontWeight: 500,
                    fontSize: '0.75rem',
                    color: '#344054',
                  }}
                >
                  FIRST NAME
                </Typography>
                <Stack direction="row" alignItems="center" gap={1}>
                  <Typography
                    sx={{ fontWeight: 500, fontSize: '1rem', color: '#000A12' }}
                  >
                    {user?.firstName}
                  </Typography>
                  <CheckCircleOutlineIcon
                    fontSize="small"
                    sx={{ color: '#00BC2D' }}
                  />
                </Stack>
              </Grid>
              {user?.middleName && (
                <Grid>
                  <Typography
                    variant="subtitle3"
                    sx={{
                      fontWeight: 500,
                      fontSize: '0.75rem',
                      color: '#344054',
                    }}
                  >
                    MIDDLE NAME
                  </Typography>
                  <Stack direction="row" alignItems="center" gap={1}>
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '1rem',
                        color: '#000A12',
                      }}
                    >
                      {user?.middleName}
                    </Typography>
                    <CheckCircleOutlineIcon
                      fontSize="small"
                      sx={{ color: '#00BC2D' }}
                    />
                  </Stack>
                </Grid>
              )}

              <Grid>
                <Typography
                  sx={{
                    fontWeight: 500,
                    fontSize: '0.75rem',
                    color: '#344054',
                  }}
                >
                  LAST NAME
                </Typography>
                <Stack direction="row" alignItems="center" gap={1}>
                  <Typography
                    sx={{ fontWeight: 500, fontSize: '1rem', color: '#000A12' }}
                  >
                    {user?.lastName}
                  </Typography>
                  <CheckCircleOutlineIcon
                    fontSize="small"
                    sx={{ color: '#00BC2D' }}
                  />
                </Stack>
              </Grid>

              {user?.gender && (
                <Grid>
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '0.75rem',
                      color: '#344054',
                    }}
                  >
                    GENDER{' '}
                  </Typography>
                  <Stack direction="row" alignItems="center" gap={1}>
                    <Typography
                      sx={{
                        fontWeight: 500,
                        fontSize: '1rem',
                        color: '#000A12',
                      }}
                    >
                      {user?.gender}
                    </Typography>
                    <CheckCircleOutlineIcon
                      fontSize="small"
                      sx={{ color: '#00BC2D' }}
                    />
                  </Stack>
                </Grid>
              )}

              <Grid>
                <Typography
                  sx={{
                    fontWeight: 500,
                    fontSize: '0.75rem',
                    color: '#344054',
                  }}
                >
                  CITIZENSHIP
                </Typography>
                <Stack direction="row" alignItems="center" gap={1}>
                  <Typography
                    sx={{
                      fontWeight: 500,
                      fontSize: '1rem',
                      color: '#000A12',
                    }}
                  >
                    {user?.citizenship}
                  </Typography>
                  <CheckCircleOutlineIcon
                    fontSize="small"
                    sx={{ color: '#00BC2D' }}
                  />
                </Stack>
              </Grid>
            </Grid>
          </Box>

          {user?.accountCreationDocumentUrl && (
            <Stack spacing={3}>
              <Divider />

              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="flex-start"
                flexWrap="wrap"
                width="100%"
                mb={3}
              >
                <Stack direction="row" alignItems="center" gap={1}>
                  <DownloadPng />
                  <Stack alignItems="start" justifyContent="start">
                    <Typography variant={'body2'} fontWeight={600} mb={0.5}>
                      Filled & Signed EATTA Account Creation Request Form
                    </Typography>
                    <Button
                      variant="text"
                      onClick={() =>
                        handleDownload(user?.accountCreationDocumentUrl!)
                      }
                      sx={{
                        padding: '0px',
                        color: '#1976d2 !important',
                        textDecoration: 'underline',
                        textDecorationColor: '#1976d2',
                      }}
                    >
                      Download
                    </Button>
                  </Stack>
                </Stack>
              </Stack>
            </Stack>
          )}
        </Stack>
      </Stack>
      {!isConfirmAction && (
        <MakerCheckerActivities
          activities={[
            {
              action: 'User Created by',
              actionedBy: onboardingUser?.maker ?? '',
              actionedDate: onboardingUser?.dateCreated ?? '',
            },
          ]}
        />
      )}

      <AccessControlWrapper
        rights={
          isConfirmAction
            ? ACCESS_CONTROLS.CREATE_USER
            : ACCESS_CONTROLS.ACCEPT_CREATE_USER
        }
        makerId={onboardingUser?.maker}
        isMake={isConfirmAction}
      >
        <Button
          variant="contained"
          color="primary"
          onClick={handleNextStep}
          disabled={isCreatingUser}
          endIcon={
            isCreatingUser ? (
              <CircularProgress size={20} thickness={3.0} />
            ) : (
              <KeyboardArrowRightIcon />
            )
          }
        >
          {isConfirmAction ? 'Submit' : 'Approve'}
        </Button>
      </AccessControlWrapper>

      {openCommentDialog && (
        <CommentDialog
          open={openCommentDialog}
          onClose={() => setOpenCommentsDialog(false)}
          onSubmit={handleOnSubmitCommentsDialog}
        />
      )}
    </Stack>
  )
}

export default UserDetailsVerification
