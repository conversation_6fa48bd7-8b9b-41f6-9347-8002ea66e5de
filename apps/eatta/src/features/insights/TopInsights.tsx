/**
 * <AUTHOR> on 17/02/2025
 */
import {
  IconButton,
  LinearProgress,
  Stack,
  ToggleButton,
  Typography,
} from '@mui/material'
import Grid from '@mui/material/Grid2'
import { ToMetricClient, TopMetric, TopStat } from '@/store/interfaces/insight'
import { InfoOutlined } from '@mui/icons-material'
import React, { useState } from 'react'
import { useAppSelector } from '@/store'
import { TopInsightSkeleton } from '@/features/insights/InsightSkeleton'
import { EmptyInsights } from '@/features/insights/EmptyInsights'
import { AccessWrapper } from '@/components/AccessHelper'
import { CustomToggleButtonGroup } from '@/components/CustomToggleButton'

const TopInsight = ({ topInsight }: { topInsight: TopMetric }) => {
  const { organizationCode, value, percentage } = topInsight
  return (
    <Stack direction="row" alignItems="center" spacing={1}>
      <Typography variant="body1">{organizationCode}</Typography>
      <IconButton>
        <InfoOutlined />
      </IconButton>

      <LinearProgress
        sx={{
          width: '100%',
          height: '0.5rem',
          borderRadius: '0.5rem',
          backgroundColor: '#E4E7EC',
        }}
        variant="determinate"
        value={percentage}
      ></LinearProgress>

      <Typography sx={{ width: '15ch' }}>{`${value} Kgs`}</Typography>
    </Stack>
  )
}

const transformTopData = (total: number, data: TopStat[]): TopMetric[] => {
  return data.map((data) => {
    const { organizationCode, value } = data
    return {
      organizationCode,
      value,
      percentage: (value / total) * 100,
    } as TopMetric
  })
}

export const TopInsights = () => {
  const { topMetrics, loadingTopMetrics } = useAppSelector(
    (state) => state.insights
  )
  const [client, setClient] = useState<ToMetricClient>('Producers')

  const handleClient = (
    event: React.MouseEvent<HTMLElement>,
    newClient: ToMetricClient
  ) => {
    setClient(newClient)
  }

  const renderSection = (data: TopMetric[]) => (
    <>
      {data.map((metric) => (
        <TopInsight key={metric.organizationCode} topInsight={metric} />
      ))}
    </>
  )

  return (
    <Stack spacing={2}>
      <CustomToggleButtonGroup
        value={client}
        exclusive
        sx={{}}
        onChange={handleClient}
        aria-label="text alignment"
      >
        <AccessWrapper clientTypes={['Partner']} backofficeAccess={false}>
          <ToggleButton
            color="primary"
            value="Producers"
            aria-label="Producers"
          >
            Top 10 Producers
          </ToggleButton>
        </AccessWrapper>
        <ToggleButton color="primary" value="Buyers" aria-label="Buyers">
          Top 10 Buyers
        </ToggleButton>
      </CustomToggleButtonGroup>

      {loadingTopMetrics ? (
        <TopInsightSkeleton />
      ) : (
        <Grid container>
          <Grid size={{ xs: 12, sm: 12, md: 12, lg: 9 }}>
            <Stack
              paddingBlock={3}
              paddingInline={2}
              spacing={2}
              sx={{
                backgroundColor: '#FFFFFF',
                borderRadius: '0.375rem',
              }}
            >
              {!topMetrics ? (
                <EmptyInsights />
              ) : (
                renderSection(
                  transformTopData(
                    client === 'Producers'
                      ? topMetrics.totalWeight
                      : topMetrics.totalValue,
                    client === 'Producers'
                      ? topMetrics.topProducers
                      : topMetrics.topBuyers
                  ) ?? []
                )
              )}
            </Stack>
          </Grid>
        </Grid>
      )}
    </Stack>
  )
}
