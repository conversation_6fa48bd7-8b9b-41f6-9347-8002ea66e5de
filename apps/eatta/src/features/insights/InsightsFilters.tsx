import { But<PERSON>, Stack, useTheme } from '@mui/material'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import React from 'react'
import { SearchRounded } from '@mui/icons-material'
import { DateIcon } from '@dtbx/ui/icons'
import { InsightFilters } from '@/store/interfaces/insight'
import { debounce } from 'lodash'

/**
 * <AUTHOR> on 17/02/2025
 */

type InsightsFiltersProps = {
  onFilterChange: (filters: InsightFilters) => void
}

export const InsightsFilters = ({ onFilterChange }: InsightsFiltersProps) => {
  const theme = useTheme()

  const handleFilterChange = debounce((filter: string) => {
    onFilterChange({ saleCode: filter })
  }, 500)

  return (
    <Stack
      direction="row"
      flexWrap="wrap"
      justifyContent="space-between"
      spacing={2}
      useFlexGap
      paddingInline={3}
      paddingBlock={2}
    >
      <Stack direction="row" flexWrap="wrap" spacing={2} useFlexGap>
        <CustomSearchInput
          placeholder="Select sale number"
          endAdornment={
            <SearchRounded
              sx={{
                color: 'gray',
              }}
            />
          }
          sx={{
            [theme.breakpoints.down('md')]: {
              width: '100%',
            },
            width: '14rem',
            background: '#FFFFFF',
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
            },
          }}
          onChange={(event) => handleFilterChange(event.target.value)}
        />

        <CustomSearchInput
          placeholder="Search producer"
          endAdornment={
            <SearchRounded
              sx={{
                color: 'gray',
              }}
            />
          }
          sx={{
            [theme.breakpoints.down('md')]: {
              width: '100%',
            },
            width: '14rem',
            background: '#FFFFFF',
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
            },
          }}
        />

        <CustomSearchInput
          placeholder="Select buyer"
          endAdornment={
            <SearchRounded
              sx={{
                color: 'gray',
              }}
            />
          }
          sx={{
            [theme.breakpoints.down('md')]: {
              width: '100%',
            },
            width: '14rem',
            background: '#FFFFFF',
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
            },
          }}
        />
      </Stack>

      <Button
        variant="outlined"
        sx={{
          border: '1px solid #D0D5DD',
          paddingInline: 2,
          color: '#667085',
        }}
        onClick={() => {}}
        startIcon={<DateIcon />}
      >
        Filter by Date
      </Button>
    </Stack>
  )
}
