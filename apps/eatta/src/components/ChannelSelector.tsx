import { Autocomplete, Stack, TextField, Typography } from '@mui/material'
import React, { FC } from 'react'
import { CHANNELS, PaymentChannel } from '@/store/interfaces'
import KeyboardArrowDownRounded from '@mui/icons-material/KeyboardArrowDownRounded'

export interface ChannelSelectorProps {
  label?: string
  placeholder?: string
  options?: PaymentChannel[]
  value?: PaymentChannel | null
  onChange: (value: PaymentChannel | null) => void
}

export const ChannelSelector: FC<ChannelSelectorProps> = ({
  label = 'Select Channel',
  placeholder = 'Channel',
  options = CHANNELS,
  value,
  onChange,
}) => {
  return (
    <Stack
      spacing={1}
      sx={{
        width: '100%',
        maxWidth: '10rem',
      }}
    >
      <Typography
        variant="body2"
        sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
      >
        {label}
      </Typography>
      <Autocomplete
        disablePortal
        size="small"
        id="channel-selector"
        options={options}
        value={value}
        popupIcon={<KeyboardArrowDownRounded />}
        onChange={(_, newValue) => {
          onChange(newValue)
        }}
        sx={{
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '3px !important',
          },
          background: '#FFFFFF',
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
            },
          },
          borderRadius: '0.5rem',
          color: '#667085',
        }}
        renderInput={(params) => (
          <TextField
            hiddenLabel
            placeholder={placeholder}
            {...params}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '0.5rem',
              },
            }}
          />
        )}
      />
    </Stack>
  )
}
