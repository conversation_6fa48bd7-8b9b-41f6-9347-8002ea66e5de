import { getCompanies } from '@/store/actions'
import { <PERSON><PERSON>ple<PERSON>, <PERSON>ack, TextField, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useDispatch } from 'react-redux'
import { useAppSelector } from '@/store'
import { Company, CompanyFilters } from '@/store/interfaces'
import KeyboardArrowDownRounded from '@mui/icons-material/KeyboardArrowDownRounded'

export interface BrokerSelectorProps {
  label?: string
  placeholder?: string
  value?: string | null
  onChange: (value: string | null) => void
}

export function BrokerSelector({
  label = 'Select Broker',
  placeholder = 'Select Broker',
  value,
  onChange,
}: BrokerSelectorProps) {
  const dispatch = useDispatch()
  const { companiesResponse, isLoading } = useAppSelector(
    (state) => state.companies
  )
  const [brokers, setBrokers] = useState<Company[]>([])
  const [selectedBroker, setSelectedBroker] = useState<Company | null>(null)

  useEffect(() => {
    const filters: CompanyFilters = {
      page: 1,
      size: 100,
      type: 'Broker',
    }
    getCompanies(dispatch, filters)
  }, [dispatch])

  useEffect(() => {
    const brokersOnly = companiesResponse.data.filter((b) => b.code && b.name)
    setBrokers(brokersOnly)

    if (brokersOnly.length > 0 && !value) {
      const first = brokersOnly[0]
      setSelectedBroker(first)
      onChange(first.code)
    }
  }, [companiesResponse.data])

  useEffect(() => {
    if (value) {
      const match = brokers.find((b) => b.code === value) || null
      setSelectedBroker(match)
    }
  }, [value, brokers])

  return (
    <Stack
      spacing={1}
      sx={{
        width: '100%',
      }}
    >
      <Typography
        variant="body2"
        sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
      >
        {label}
      </Typography>
      <Autocomplete
        disablePortal
        size="small"
        id="broker-selector"
        loading={isLoading}
        options={brokers}
        popupIcon={<KeyboardArrowDownRounded />}
        getOptionLabel={(option) => `${option.name}`}
        value={selectedBroker}
        onChange={(_, newValue) => {
          setSelectedBroker(newValue)
          onChange(newValue?.code ?? null)
        }}
        sx={{
          width: '100%',
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '3px !important',
          },
          background: '#FFFFFF',
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
            },
          },
          borderRadius: '0.5rem',
          color: '#667085',
        }}
        renderInput={(params) => (
          <TextField
            hiddenLabel
            placeholder={placeholder}
            {...params}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '0.5rem',
              },
            }}
          />
        )}
      />
    </Stack>
  )
}
