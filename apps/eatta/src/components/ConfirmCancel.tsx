'use client'

import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  IconButton,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'

export interface ConfirmCancelProps {
  open: boolean
  title: string
  description: string
  confirmLabel?: string
  cancelLabel?: string
  onConfirm: () => void
  onCancel: () => void
}

export function ConfirmCancel({
  open,
  title,
  description,
  confirmLabel,
  cancelLabel,
  onConfirm,
  onCancel,
}: ConfirmCancelProps) {
  return (
    <Dialog open={open} onClose={onCancel} maxWidth="xs" fullWidth>
      <DialogTitle
        sx={{
          borderBottom: '1px solid #CBD5E1',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#F9FAFB',
        }}
      >
        <Typography sx={{ color: '#000A12', fontWeight: 600 }}>
          {title}
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onCancel}
          size="small"
          sx={{
            width: '36px',
            height: '36px',
            backgroundColor: ' #F1F5F9',
            border: '1px solid #CBD5E1)',
            borderRadius: '50%',
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent
        sx={{
          padding: '10px',
          textAlign: 'center',
          marginTop: '20px',
        }}
      >
        <Typography sx={{ color: '#000A12', fontWeight: 400 }}>
          {description}
        </Typography>
      </DialogContent>
      <DialogActions
        sx={{
          padding: '20px 24px',
          display: 'flex',
          justifyContent: 'center',
          gap: '20px',
        }}
      >
        <Button
          variant="contained"
          sx={{
            backgroundColor: '#FF4D38',
            borderRadius: '4px',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            fontSize: '16px',
            fontStyle: 'normal',
            fontWeight: '500',
            lineHeight: '24px',
            textAlign: 'center',
            width: '153px',
            height: '40px',
            textWrap: 'nowrap',
          }}
          onClick={onConfirm}
        >
          {confirmLabel}
        </Button>
        <Button
          variant="outlined"
          sx={{
            backgroundColor: '#FFFFFF',
            border: '1.5px solid  #AAADB0',
            borderRadius: '4px',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            color: '#555C61',
            fontSize: '16px',
            fontStyle: 'normal',
            fontWeight: '500',
            lineHeight: '24px',
            textAlign: 'center',
            width: '153px',
            height: '40px',
            textWrap: 'nowrap',
          }}
          onClick={onCancel}
        >
          {cancelLabel}
        </Button>
      </DialogActions>
    </Dialog>
  )
}
