import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { KeyboardArrowDownRounded } from '@mui/icons-material'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import {
  Stack,
  MenuItem,
  Select,
  SelectChangeEvent,
  InputAdornment,
} from '@mui/material'
import React from 'react'

interface CustomFilterUserBoxProps {
  searchValue: string
  onHandleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  searchByValues?: string[]
  searchByLabel?: Record<string, string>
  selectedSearchBy: string
  setSearchByValue?: (value: string) => void
  searchPlaceHolder?: string
  prependSearchBy?: boolean
}

export const CustomFilterUserBox = ({
  searchValue,
  onHandleSearch,
  searchByValues,
  searchByLabel,
  selectedSearchBy,
  setSearchByValue,
  searchPlaceHolder,
  prependSearchBy,
}: CustomFilterUserBoxProps) => {
  return (
    <Stack sx={{ flexDirection: 'column' }}>
      <Stack
        sx={{
          justifyContent: prependSearchBy ? 'flex-start' : 'flex-end',
          gap: '10px',
        }}
        direction={prependSearchBy ? 'row' : 'row-reverse'}
      >
        {searchByValues && setSearchByValue && (
          <SearchByValuesBox
            searchByValues={searchByValues}
            searchByLabel={searchByLabel}
            selectedSearchBy={selectedSearchBy}
            setSearchByValue={setSearchByValue}
            placeholder=""
          />
        )}
        <CustomSearchInput
          value={searchValue}
          onChange={onHandleSearch}
          placeholder={searchPlaceHolder || 'Search'}
          startAdornment={
            <InputAdornment position="start">
              <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
            </InputAdornment>
          }
          sx={{
            background: '#FFFFFF',
            borderRadius: '0.5rem',
            '& fieldset': {
              border: `1px solid #D0D5DD !important`,
            },
          }}
        />
      </Stack>
    </Stack>
  )
}

interface SearchByValuesBoxProps {
  searchByValues: string[]
  searchByLabel?: Record<string, string>
  selectedSearchBy: string
  setSearchByValue: (value: string) => void
  placeholder?: string
}

export const SearchByValuesBox = ({
  searchByValues,
  searchByLabel,
  selectedSearchBy,
  setSearchByValue,
}: SearchByValuesBoxProps) => {
  const handleSelect = (e: SelectChangeEvent) => {
    setSearchByValue(e.target.value)
  }
  return (
    <Stack direction="row">
      <Select
        size="medium"
        onChange={handleSelect}
        IconComponent={(iconProps) => (
          <KeyboardArrowDownRounded
            {...iconProps}
            sx={{
              color: '#667085',
              marginLeft: '0.5rem',
              ...(iconProps?.sx || {}),
            }}
          />
        )}
        value={selectedSearchBy}
        sx={{
          width: '100%',
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '2px !important',
          },
          background: '#FFFFFF',
          '& fieldset': {
            border: `1px solid #D0D5DD !important`,
          },
          borderRadius: '8px',
          color: '#667085',
        }}
      >
        {searchByValues.map((value) => (
          <MenuItem key={value} value={value}>
            {`By ${searchByLabel?.[value] || value}`}
          </MenuItem>
        ))}
      </Select>
    </Stack>
  )
}
