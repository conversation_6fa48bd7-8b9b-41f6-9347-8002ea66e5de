'use client'

import type React from 'react'
import { useEffect, useRef, useState } from 'react'
import { Box, Typography } from '@mui/material'

export interface CountdownTimerProps {
  initialSeconds?: number
  elapsedSeconds?: number
  onExpire?: () => void
  isGeneratingOtp?: boolean | React.Dispatch<React.SetStateAction<boolean>>
  resetKey?: number
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({
  initialSeconds = 100,
  onExpire,
  isGeneratingOtp = false,
  resetKey = 0,
}) => {
  const [seconds, setSeconds] = useState(initialSeconds)
  const [isActive, setIsActive] = useState(false)
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (isGeneratingOtp) {
      setSeconds(initialSeconds)
      setIsActive(true)
    }
  }, [isGeneratingOtp, initialSeconds])

  useEffect(() => {
    if (isActive && seconds > 0) {
      timerRef.current = setInterval(() => {
        setSeconds((prevSeconds) => {
          if (prevSeconds <= 1) {
            if (timerRef.current) clearInterval(timerRef.current)
            setIsActive(false)
            if (onExpire) onExpire()
            return 0
          }
          return prevSeconds - 1
        })
      }, 1000)
    } else if (seconds === 0) {
      setIsActive(false)
      if (onExpire) onExpire()
    }

    return () => {
      if (timerRef.current) clearInterval(timerRef.current)
    }
  }, [isActive, seconds, onExpire])

  const percentage = Math.floor((seconds / initialSeconds) * 100)

  return (
    <Box
      sx={{
        backgroundColor: '#F2F4F7',
        borderRadius: '100px',
        padding: '8px 16px',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        minWidth: '200px',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Progress indicator */}
      <Box
        sx={{
          position: 'absolute',
          left: 0,
          top: 0,
          bottom: 0,
          width: `${percentage}%`,
          backgroundColor: 'rgba(38, 180, 59, 0.1)',
          transition: 'width 1s linear',
        }}
      />
      <Typography
        sx={{
          color: 'primary.main',
          fontSize: '16px',
          fontWeight: 500,
          textAlign: 'center',
          transition: 'all 0.3s ease',
          zIndex: 1,
          '&:hover': {
            transform: 'scale(1.05)',
          },
        }}
      >
        {seconds} Seconds left
      </Typography>
    </Box>
  )
}

export default CountdownTimer
