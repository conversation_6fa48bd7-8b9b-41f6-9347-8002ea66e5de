import { CustomSkeleton } from '@dtbx/ui/components'
import { Stack } from '@mui/material'
import React from 'react'

const UserCardListSkeleton = () => {
  return (
    <Stack
      direction="row"
      flexWrap="wrap"
      sx={{
        marginLeft: '1rem',
        marginTop: '1rem',
        gap: '1rem',
        justifyContent: 'flex-start',
      }}
    >
      {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((user, _index) => (
        <CustomSkeleton
          variant="rectangular"
          sx={{
            width: '18rem',
            height: '16.8rem',
          }}
        />
      ))}
    </Stack>
  )
}

export default UserCardListSkeleton
