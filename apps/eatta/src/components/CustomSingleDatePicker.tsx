import {
  ChevronLeftRounded,
  ChevronRightRounded,
  KeyboardArrowDownRounded,
} from '@mui/icons-material'
import {
  Box,
  Button,
  ClickAwayListener,
  Fade,
  IconButton,
  Paper,
  Popper,
  Stack,
  SxProps,
  TextField,
  Typography,
} from '@mui/material'
import { CalendarIcon } from '@mui/x-date-pickers'
import dayjs, { Dayjs } from 'dayjs'
import React, { ReactNode, useState } from 'react'

interface ICustomSingleDatePicker {
  buttonText?: string
  buttonIcon?: ReactNode
  onDateSelect: (date: Dayjs) => void
  size?: 'small' | 'medium' | 'large'
  currentDate?: Dayjs | null
  placeholder?: string
  minDate?: Dayjs | null
}

export const CustomSingleDatePicker: React.FC<ICustomSingleDatePicker> = ({
  buttonText,
  buttonIcon,
  onDateSelect,
  size,
  currentDate: initialDate,
  placeholder = 'Select Date...',
  minDate = null,
}) => {
  const anchorRef = React.useRef<HTMLButtonElement | null>(null)
  const [open, setOpen] = useState<boolean>(false)
  const [currentDate, setCurrentDate] = useState<Dayjs>(dayjs())
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(
    initialDate || null
  )

  const [calenderView, setCalenderView] = useState<boolean>(true)
  const [monthView, setMonthView] = useState<boolean>(false)

  const isSelectedDate = (day: Dayjs) => {
    return day.isSame(selectedDate, 'day')
  }

  const isDisabledDate = (day: Dayjs) => {
    if (!minDate) return false
    return day.isBefore(minDate, 'day')
  }

  const handleDateClicked = (clickedDate: Dayjs) => {
    if (isDisabledDate(clickedDate)) {
      return // Don't allow selection of disabled dates
    }
    setSelectedDate(clickedDate.startOf('day'))
  }

  // month state setter
  const setNextMonth = () => {
    setCurrentDate(currentDate.clone().add(1, 'month'))
  }

  const setPrevMonth = () => {
    setCurrentDate(currentDate.clone().subtract(1, 'month'))
  }

  const handleClick = () => {
    setOpen((prev) => !prev)
  }

  const CalenderView = () => {
    return (
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(7, 0fr)',
          gap: '0px',
          padding: '0px',
          width: '100%',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((day) => (
          <Box
            sx={{
              textAlign: 'center',
              fontWeight: 'bold',
            }}
            key={day}
          >
            {day}
          </Box>
        ))}
        {generateCalendar()}
      </Box>
    )
  }

  const MonthView = () => {
    return (
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(3, 0fr)',
          gap: '0px',
          padding: '3px',
          width: '100%',
          height: '200px',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        {[
          'January',
          'February',
          'March',
          'April',
          'May',
          'June',
          'July',
          'August',
          'September',
          'October',
          'November',
          'December',
        ].map((month, index) => (
          <Button
            sx={{
              textAlign: 'center',
              fontWeight: 'bold',
              color: 'black',
            }}
            key={month}
            variant="text"
            onClick={() => {
              setCurrentDate(currentDate.month(index))
              setMonthView(false)
              setCalenderView(true)
            }}
          >
            {month}
          </Button>
        ))}
      </Box>
    )
  }

  const YearView = () => {
    return (
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(4, 0fr)',
          gap: '0px',
          padding: '3px',
          width: '100%',
          height: '200px',
          justifyContent: 'space-between',
          alignItems: 'center',
          overflowY: 'auto',
          overflowX: 'hidden',
          marginLeft: '2px',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'lightgray transparent',
            padding: '0px 4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'darkgray',
            borderRadius: '10px',
          },
        }}
      >
        {Array.from({ length: 70 }, (_, k) => dayjs().year() - k).map(
          (year) => (
            <Button
              sx={{
                textAlign: 'center',
                fontWeight: 'bold',
                color: 'black',
              }}
              key={year}
              variant="text"
              onClick={() => {
                setCurrentDate(currentDate.year(year))
                setMonthView(true)
                setCalenderView(false)
              }}
            >
              {year}
            </Button>
          )
        )}
      </Box>
    )
  }

  const DateBox = ({
    key,
    sx,
    date,
    onClick,
  }: {
    key: string
    sx: SxProps
    date: Dayjs
    onClick?: () => void
  }) => {
    return (
      <Box sx={sx} key={key} onClick={onClick}>
        <Box
          style={{
            padding: 0,
            height: '100%',
            width: '100%',
            backgroundColor: isSelectedDate(date) ? 'black' : 'inherit',
            borderRadius: isSelectedDate(date) ? '50%' : 'none',
            color: isSelectedDate(date) ? 'white' : 'inherit',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {date.format('D')}
        </Box>
      </Box>
    )
  }

  const generateCalendar = () => {
    const startOfMonth = currentDate.startOf('month').startOf('week')
    const endOfMonth = currentDate.endOf('month').endOf('week')
    const days = []
    let day = startOfMonth

    while (day.isBefore(endOfMonth.add(1, 'day'))) {
      days.push(day)
      day = day.add(1, 'day')
    }

    return days.map((day) => (
      <DateBox
        key={day.format('YYYY-MM-DD')}
        sx={{
          textAlign: 'center',
          padding: isSelectedDate(day) ? 0 : '10px',
          width: '50px',
          height: '50px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          cursor: isDisabledDate(day) ? 'not-allowed' : 'pointer',
          '&:hover': {
            backgroundColor: isDisabledDate(day) ? 'inherit' : '#f5f5f5',
          },
          color: isSelectedDate(day)
            ? 'white'
            : isDisabledDate(day)
              ? '#ccc'
              : 'inherit',
          backgroundColor: isSelectedDate(day) ? '#f5f5f5' : 'inherit',
          opacity: isDisabledDate(day) ? 0.5 : 1,
        }}
        onClick={() => handleDateClicked(day)}
        date={day}
      />
    ))
  }

  return (
    <>
      <Button
        variant="outlined"
        endIcon={
          buttonText ? (
            buttonIcon ? (
              buttonIcon
            ) : (
              <KeyboardArrowDownRounded />
            )
          ) : (
            <></>
          )
        }
        ref={anchorRef}
        aria-controls={open ? 'date-picker-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        id="date-picker-button"
        onClick={handleClick}
        sx={{
          height: '42px',
          minWidth: '130px',
          backgroundColor: '#FFF',
          display: 'flex',
          justifyContent: 'space-between',
          borderRadius: '6px',
          border: '1.5px solid #D0D5DD',
        }}
      >
        {buttonText ? (
          <Typography
            sx={{
              color: 'black',
              fontWeight: '400',
            }}
          >
            {buttonText}
          </Typography>
        ) : (
          <Box
            sx={{
              display: 'flex',
              gap: '4px',
              alignItems: 'center',
              width: '100%',
              justifyContent: 'space-between',
            }}
          >
            <Typography
              sx={{
                textWrap: 'nowrap',
                color: selectedDate ? 'black' : '#98A2B3',
              }}
            >
              {selectedDate ? selectedDate.format('MMM D, YYYY') : placeholder}
            </Typography>
            <CalendarIcon />
          </Box>
        )}
      </Button>
      <Popper
        id="date-picker-menu"
        placement="bottom-start"
        anchorEl={anchorRef.current}
        open={open}
        transition
        disablePortal
        sx={{
          zIndex: '2000',
        }}
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={350}>
            <Paper
              sx={{
                maxWidth: size === 'small' ? '300px' : '400px',
                marginTop: '-150px',
              }}
            >
              <ClickAwayListener
                onClickAway={() => {
                  setOpen(false)
                }}
              >
                <Stack>
                  <Box
                    component={'div'}
                    sx={{
                      padding: '20px 28px',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '16px',
                      borderBottom: '1px solid #ccc',
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        gap: '4px',
                      }}
                    >
                      <TextField
                        size="small"
                        sx={{
                          width: '100%',
                        }}
                        value={
                          selectedDate && selectedDate.format('MMMM D, YYYY')
                        }
                        inputProps={{ readonly: true }}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Box>

                    {/* Month selector */}
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        gap: '4px',
                      }}
                    >
                      <IconButton
                        onClick={setPrevMonth}
                        sx={{
                          padding: '8px',
                          width: '28px',
                          height: '28px',
                        }}
                      >
                        <ChevronLeftRounded
                          sx={{
                            fontSize: '20px',
                          }}
                        />
                      </IconButton>

                      <Box
                        sx={{
                          display: 'flex',
                          gap: '4px',
                          color: '#000000',
                        }}
                      >
                        <Button
                          sx={{
                            paddingRight: '4px',
                            color: 'black',
                          }}
                          onClick={() => {
                            setMonthView(true)
                            setCalenderView(false)
                          }}
                        >
                          {currentDate.format('MMMM D,')}
                        </Button>
                        <Button
                          sx={{
                            paddingLeft: '0',
                            color: 'black',
                          }}
                          onClick={() => {
                            setMonthView(false)
                            setCalenderView(false)
                          }}
                        >
                          {currentDate.format('YYYY')}
                        </Button>
                      </Box>

                      <IconButton
                        onClick={setNextMonth}
                        sx={{
                          padding: '8px',
                          width: '28px',
                          height: '28px',
                        }}
                      >
                        <ChevronRightRounded
                          sx={{
                            fontSize: '20px',
                          }}
                        />
                      </IconButton>
                    </Box>
                    {/* calender view grid */}
                    {calenderView ? (
                      <CalenderView />
                    ) : monthView ? (
                      <MonthView />
                    ) : (
                      <YearView />
                    )}
                  </Box>
                </Stack>
              </ClickAwayListener>

              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  padding: '16px',
                  gap: '16px',
                }}
              >
                <Button
                  variant="outlined"
                  sx={{
                    padding: '8px 16px',
                    textTransform: 'none',
                    width: '100%',
                    border: '1px solid ',
                  }}
                  onClick={() => {
                    setSelectedDate(null)
                    setOpen(false)
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="contained"
                  sx={{
                    padding: '8px 16px',
                    textTransform: 'none',
                    width: '100%',
                  }}
                  onClick={() => {
                    if (selectedDate !== null) {
                      onDateSelect(selectedDate)
                      setOpen(false)
                    }
                  }}
                >
                  Apply
                </Button>
              </Box>
            </Paper>
          </Fade>
        )}
      </Popper>
    </>
  )
}
