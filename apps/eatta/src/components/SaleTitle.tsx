/**
 * <AUTHOR> on 29/04/2025
 */
import { Stack, Typography } from '@mui/material'
import { FC } from 'react'

interface SaleTitleProps {
  saleDate: string
  lotsCount: number
}

export const SaleTitle: FC<SaleTitleProps> = ({ saleDate, lotsCount }) => {
  return (
    <Stack
      direction="column"
      sx={{
        paddingInline: 3,
        paddingBlock: 1,
        background: '#F2F4F',
        borderRadius: '8px',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        width: '100%',
      }}
    >
      <Typography
        variant="subtitle2"
        fontWeight="600"
        color={'textPrimary'}
        textAlign="left"
      >
        Main sale of {saleDate}
      </Typography>
      <Typography
        variant="subtitle3"
        sx={{
          color: '#000A12',
          fontWeight: 400,
          width: '100%',
          textAlign: 'left',
        }}
      >
        Showing {lotsCount} Lots
      </Typography>
    </Stack>
  )
}
