/**
 * <AUTHOR> on 29/04/2025
 */

import React, { JSX, ReactNode } from 'react'
import { Stack, Typography } from '@mui/material'
import { useAppSelector } from '@/store'
import Image from 'next/image'
import { checkIfBackOffice } from '@/utils/appTypeChecker'

export const LoginInfo = () => {
  const { decodedToken } = useAppSelector((state) => state.auth)
  const isBackOffice = checkIfBackOffice()

  return (
    <Stack spacing={2} direction="row" alignItems="center">
      <Image
        src={isBackOffice ? '/eatta/green-tea.png' : '/green-tea.png'}
        alt="Tea leaf icon"
        height={32}
        width={32}
      />
      <Typography
        variant="h5"
        sx={{
          whiteSpace: 'nowrap',
          color: 'primary.main',
          fontWeight: 500,
          fontSize: '1.25rem',
        }}
      >
        Logged in as {decodedToken.clientName || ''}
      </Typography>
    </Stack>
  )
}

interface MainPageHeaderProps {
  children?: ReactNode
}

export const MainPageHeader: React.FC<MainPageHeaderProps> = ({
  children,
}): JSX.Element => {
  return (
    <Stack
      direction="row"
      useFlexGap
      flexWrap="wrap"
      spacing={2}
      sx={{
        backgroundColor: '#FFFFFF',
        paddingInline: 3,
        paddingBlock: 2,
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
      }}
    >
      <LoginInfo />

      {children}
    </Stack>
  )
}
