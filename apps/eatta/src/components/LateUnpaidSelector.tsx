import { <PERSON><PERSON>plete, TextField, Stack, Typography } from '@mui/material'
import { KeyboardArrowDownRounded } from '@mui/icons-material'
import { useEffect, useState } from 'react'
import { LATE_UNPAID_OPTIONS } from '@/store/interfaces/invoices'

export interface LateUnpaidSelectorProps {
  label?: string
  value?: number[] | null
  onChange: (value: number[] | null) => void
  showAllOption?: boolean
}

type LateUnpaidOption = {
  value: readonly number[]
  label: string
}

export function LateUnpaidSelector({
  label = 'Filter unpaid late teas:',
  value,
  onChange,
  showAllOption = true,
}: LateUnpaidSelectorProps) {
  const [selectedValue, setSelectedValue] = useState<LateUnpaidOption | null>(
    null
  )

  useEffect(() => {
    if (value && value.length > 0) {
      const option = LATE_UNPAID_OPTIONS.find((opt) => 
        JSON.stringify(opt.value) === JSON.stringify(value)
      )
      setSelectedValue(option || null)
    } else {
      setSelectedValue(null)
    }
  }, [value])

  const handleChange = (_event: any, newValue: LateUnpaidOption | null) => {
    setSelectedValue(newValue)
    onChange(newValue?.value ? [...newValue.value] : null)
  }

  return (
    <Stack spacing={1} sx={{ width: '20rem' }}>
      <Typography
        variant="body2"
        sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
      >
        {label}
      </Typography>

      <Autocomplete
        value={selectedValue}
        onChange={handleChange}
        options={LATE_UNPAID_OPTIONS}
        getOptionLabel={(option) => option.label}
        isOptionEqualToValue={(option, value) => 
          JSON.stringify(option.value) === JSON.stringify(value.value)
        }
        clearOnEscape
        disableClearable={false}
        popupIcon={<KeyboardArrowDownRounded sx={{ color: '#667085' }} />}
        renderInput={(params) => (
          <TextField
            {...params}
            placeholder="Select"
            sx={{
                width: '100%',
                '.MuiInputBase-input.MuiOutlinedInput-input': {
                  py: '0.1px !important',
                },
                background: '#FFFFFF',
                '& fieldset': {
                  border: `1px solid #D0D5DD !important`,
                  borderRadius: '0.5rem !important',
                },
                color: '#667085',
              }}
          />
        )}
        renderOption={(props, option) => (
          <li
            {...props}
            style={{
              color: '#344054',
              padding: '8px 16px',
              borderLeft:
                selectedValue?.value === option.value
                  ? '3px solid #F04438'
                  : 'none',
              backgroundColor:
                selectedValue?.value === option.value
                  ? '#FEF3F2'
                  : 'transparent',
            }}
          >
            {option.label}
          </li>
        )}
        sx={{
          '& .MuiAutocomplete-popupIndicator': {
            color: '#667085',
            marginRight: '0.5rem',
          },
          '& .MuiAutocomplete-clearIndicator': {
            color: '#667085',
            visibility: 'visible',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            },
          },
        }}
      />
    </Stack>
  )
}
