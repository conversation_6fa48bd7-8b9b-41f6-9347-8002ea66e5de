import {
  <PERSON>,
  Button,
  Divider,
  Grid2 as Grid,
  Skeleton,
  Stack,
  Typography,
} from '@mui/material'

const DetailsVerification = () => {
  return (
    <Stack spacing={3}>
      <Stack>
        <Stack
          spacing={3}
          sx={{
            backgroundColor: 'primary.main',
            padding: 3,
            borderRadius: '0.5rem 0.5rem 0  0',
          }}
        >
          <Stack
            direction="row"
            alignItems="center"
            spacing={2}
            flexWrap="wrap"
          >
            <Skeleton width="6rem" height="2rem" />
            <Box
              sx={{
                backgroundColor: '#F0F0F0',
                color: '#000000',
                px: 1.5,
                borderRadius: '999px',
                fontSize: '0.75rem',
                fontWeight: 500,
              }}
            >
              <Skeleton width="4rem" />
            </Box>
          </Stack>

          <Stack
            spacing={2}
            direction="row"
            useFlexGap
            sx={{ justifyContent: 'space-between', flexWrap: 'wrap' }}
          >
            {[1, 2, 3].map((item) => (
              <Stack key={item} sx={{ minWidth: '200px' }}>
                <Skeleton width="6rem" />
                <Skeleton />
              </Stack>
            ))}
          </Stack>
        </Stack>

        <Stack
          spacing={3}
          sx={{
            padding: 3,
            backgroundColor: '#FFFFFF',
            borderRadius: '0 0 0.5rem 0.5rem',
          }}
        >
          <Box>
            <Skeleton width="10rem" height="2rem" />

            <Grid
              container
              spacing={2}
              sx={{
                fontWeight: 'bold',
                paddingBlock: '0.8rem',
                backgroundColor: '#FFFFFF',
              }}
            >
              {[1, 2, 3, 4].map((item) => (
                <Grid key={item} size={{ xs: 12, sm: 3, md: 6 }}>
                  <Skeleton width="6rem" />
                  <Skeleton width="8rem" />
                </Grid>
              ))}
            </Grid>
          </Box>

          <Divider />

          <Stack>
            <Skeleton width="10rem" height="2rem" />

            <Stack
              direction="row"
              justifyContent="flex-start"
              alignItems="center"
              flexWrap="wrap"
              width="100%"
              spacing={2}
              useFlexGap
            >
              <Skeleton width="3rem" height="6rem" />
              <Stack>
                <Skeleton width="8rem" />
                <Skeleton width="3rem" />
              </Stack>
            </Stack>
          </Stack>
        </Stack>
      </Stack>

      <Stack
        justifyContent="space-between"
        alignItems="flex-start"
        flexWrap="wrap"
        width="100%"
      >
        <Skeleton width="10rem" height="2rem" />
        <Skeleton width="20rem" />

        <Skeleton height="4rem" width="100%" />
      </Stack>
    </Stack>
  )
}

export default DetailsVerification
