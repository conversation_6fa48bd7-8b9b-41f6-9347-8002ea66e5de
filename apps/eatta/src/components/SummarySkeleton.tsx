import { CustomSkeleton } from '@dtbx/ui/components'
import { Box, Stack, Typography } from '@mui/material'
import React from 'react'

function SummarySkeleton() {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        marginTop: '1rem',
      }}
    >
      <Stack
        spacing={2}
        sx={{
          width: '100%',
          maxWidth: 880,
          margin: '0 auto',
          alignItems: 'flex-start',
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontWeight: 500,
            fontSize: '1.125rem',
            color: '#000A12',
            marginBottom: '1rem',
            width: '100%',
            textAlign: 'left',
          }}
        >
          <CustomSkeleton
            variant="rectangular"
            sx={{
              width: '5rem',
              height: '2rem',
            }}
          />
        </Typography>
        {[1, 2, 3, 4, 5].map((_auction, index) => (
          <CustomSkeleton
            key={index}
            variant="rectangular"
            sx={{
              width: '100%',
              height: '7.75rem',
            }}
          />
        ))}
      </Stack>
    </Box>
  )
}

export default SummarySkeleton
