/**
 * <AUTHOR> on 23/12/2024
 */
import { Menu, MenuItem, MenuItemProps, MenuProps, styled } from '@mui/material'
import React from 'react'

export const StyledMenuItem = styled((props: MenuItemProps) => (
  <MenuItem {...props} />
))(({ theme }) => ({
  display: 'flex',
  gap: '2rem',
  justifyContent: 'space-between',
}))

export const CustomMenu = styled((props: MenuProps) => <Menu {...props} />)(
  ({ theme }) => ({
    '& .MuiPaper-root': {
      borderRadius: 8,
      paddingInline: theme.spacing(1),
      paddingBlock: theme.spacing(2),
      minWidth: 180,
      boxShadow:
        'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
      '& .MuiMenu-list': {
        padding: 0,
        gap: theme.spacing(1),
      },
      '& .MuiMenuItem-root': {
        marginTop: theme.spacing(1),
        borderRadius: 8,
        '&:active': {
          color: '101828',
          borderRadius: 8,
        },
      },
    },
  })
)
