/**
 * <AUTHOR> on 23/05/2025
 */
import { Box, Stack, Typography } from '@mui/material'
import { StepActiveIcon, StepDoneIcon, StepInactiveIcon } from '@dtbx/ui/icons'
import React from 'react'

type OnboardingStep = {
  title: string
  description?: string
}

type StepperProps = {
  steps: OnboardingStep[]
  currentStep: number
  setStep: (step: number) => void
  isComplete?: boolean
  disablePreviousSteps?: boolean
}

export const CustomStepper = ({
  steps,
  currentStep,
  setStep,
  isComplete,
  disablePreviousSteps = false,
}: StepperProps) => {
  const stepsCount = steps.length
  const isLastStep = isComplete ?? currentStep === stepsCount

  return (
    <Stack direction="column" spacing={2}>
      {steps.map((step, index) => {
        const last = index === stepsCount - 1
        const isDone = index < currentStep - 1
        const isActive = index === currentStep - 1
        const isInactive = !isDone && !isActive

        const Icon = isDone ? (
          <StepDoneIcon />
        ) : isActive ? (
          <StepActiveIcon />
        ) : (
          <StepInactiveIcon />
        )

        return (
          <Stack
            key={step.title}
            direction="row"
            spacing={3}
            sx={{
              cursor:
                isInactive || isLastStep || disablePreviousSteps
                  ? 'default'
                  : 'pointer',
            }}
            onClick={() =>
              !isInactive &&
              !isLastStep &&
              !disablePreviousSteps &&
              setStep(index + 1)
            }
          >
            <Stack direction="column" alignItems="center">
              {Icon}
              {!last && (
                <Box
                  flexGrow={1}
                  width={2}
                  height={34}
                  borderRadius={2}
                  sx={{ backgroundColor: '#EAECF0' }}
                />
              )}
            </Stack>

            <Stack direction="column">
              <Typography
                variant="body1"
                fontWeight="600"
                sx={{ color: isActive ? 'primary.main' : 'text.main' }}
              >
                {step.title}
              </Typography>
              {step.description && (
                <Typography
                  fontWeight="400"
                  sx={{ color: isActive ? 'primary.main' : 'text.main' }}
                >
                  {step.description}
                </Typography>
              )}
            </Stack>
          </Stack>
        )
      })}
    </Stack>
  )
}
