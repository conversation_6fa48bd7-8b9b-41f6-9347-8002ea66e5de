import { Radio, RadioProps } from '@mui/material'

interface CustomRadioProps extends RadioProps {
  customColor?: string
}

export const CustomRadioButton = ({
  customColor,
  ...props
}: CustomRadioProps) => {
  return (
    <Radio
      sx={{
        '&:hover': { backgroundColor: 'transparent' },
        color: customColor || 'default',
        '&.Mui-checked': {
          color: customColor || 'primary.main', // Color for checked state
        },
      }}
      disableRipple
      checkedIcon={
        <CustomCheckedRadioIcon fillColor={customColor || 'primary.main'} />
      }
      icon={<CustomRadioIcon />}
      inputProps={{ 'aria-label': 'Radio' }}
      {...props}
    />
  )
}

const CustomRadioIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="10" cy="10" r="9.5" fill="white" stroke="#D0D5DD" />
    </svg>
  )
}

const CustomCheckedRadioIcon = ({ fillColor }: { fillColor: string }) => {
  return (
    <svg
      width={20}
      height={20}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10Z"
        fill={fillColor}
      />
      <path
        d="M6.25 10L8.75 12.5L13.75 7.5"
        stroke="white"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
