/**
 * <AUTHOR> on 16/06/2025
 */
import React from 'react'
import { <PERSON>, Divider, Stack, Typography } from '@mui/material'
import { AntTab, AntTabs } from '@dtbx/ui/components/Tabs'

export type TabType<T> = {
  title: string
  status: T
  itemCounts?: number
  canSelect: boolean
}

export interface CustomTabsProps<T> {
  isLoading: boolean
  tabs: TabType<T>[]
  selectedTab: number
  onTabSelected: (index: number) => void
}

export function CustomTabs<T>({
  isLoading,
  tabs,
  selectedTab,
  onTabSelected,
}: CustomTabsProps<T>) {
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    onTabSelected(newValue)
  }
  return (
    <Stack
      sx={{
        backgroundColor: '#F2F4F7',
      }}
    >
      <AntTabs
        value={selectedTab}
        onChange={handleTabChange}
        aria-label="tabs"
        sx={{
          '& .MuiTabs-flexContainer': {
            gap: '0.5rem',
          },
        }}
      >
        {tabs.map((tab, index) => {
          const isSelectedTab = selectedTab === index
          return (
            <AntTab
              key={tab.title}
              value={index}
              label={
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Typography
                    color={isSelectedTab ? 'primary' : '#667085'}
                    style={{ fontWeight: 600 }}
                  >
                    {tab.title}
                  </Typography>
                  {tab.itemCounts !== undefined &&
                    tab.itemCounts > 0 &&
                    !isLoading && (
                      <Chip
                        label={tab.itemCounts}
                        sx={{
                          '& .MuiChip-label': {
                            px: 0.7,
                            py: 0,
                          },
                          height: 'auto',
                          fontSize: '0.75rem',
                          fontWeight: 500,
                          color: isSelectedTab
                            ? 'primary.main'
                            : 'text.secondary',
                          backgroundColor: isSelectedTab
                            ? '#EEFFF1'
                            : '#F9FAFB',
                          border: `1px solid ${isSelectedTab ? '#B0FFC4' : '#E4E7EC'}`,
                        }}
                      />
                    )}
                </Stack>
              }
            />
          )
        })}
      </AntTabs>
      <Divider sx={{ width: '98%', margin: '0 auto' }} />
    </Stack>
  )
}
