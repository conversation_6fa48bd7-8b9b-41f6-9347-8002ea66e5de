import {
  MenuItem,
  Select,
  SelectChangeEvent,
  Stack,
  Typography,
} from '@mui/material'
import { KeyboardArrowDownRounded } from '@mui/icons-material'
import { useEffect, useState } from 'react'

export interface StatusSelectorProps {
  label?: string
  value?: string | null
  statuses: readonly string[]
  onChange: (value: string | null) => void
  showAllOption?: boolean
}

export function StatusSelector({
  label = 'Select Status',
  value,
  statuses = [],
  onChange,
  showAllOption = true,
}: StatusSelectorProps) {
  const [selectedStatus, setSelectedStatus] = useState<string>(value || '')

  // Set initial status to first value if no status is set and statuses are available
  useEffect(() => {
    if (!value && statuses.length > 0 && !showAllOption) {
      const firstStatus = statuses[0]
      setSelectedStatus(firstStatus)
      onChange(firstStatus)
    } else if (value) {
      setSelectedStatus(value)
    } else if (showAllOption) {
      setSelectedStatus('')
    }
  }, [value, statuses, showAllOption, onChange])

  const handleStatusChange = (event: SelectChangeEvent) => {
    const newValue = event.target.value
    setSelectedStatus(newValue)
    onChange(newValue || null)
  }

  const formatStatusLabel = (status: string): string => {
    return status.toLowerCase()
  }

  return (
    <Stack spacing={1} sx={{ width: '18rem' }}>
      <Typography
        variant="body2"
        sx={{ color: '#344054', fontWeight: 500, fontSize: '0.875rem' }}
      >
        {label}
      </Typography>

      <Select
        IconComponent={(iconProps) => (
          <KeyboardArrowDownRounded
            {...iconProps}
            sx={{
              color: '#667085',
              marginRight: '0.5rem',
              ...(iconProps?.sx || {}),
            }}
          />
        )}
        value={selectedStatus}
        onChange={handleStatusChange}
        displayEmpty
        sx={{
          width: '100%',
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '8px !important',
          },
          background: '#FFFFFF',
          '& fieldset': {
            border: `1px solid #D0D5DD !important`,
          },
          borderRadius: '0.5rem',
          color: '#667085',
        }}
      >
        {showAllOption && <MenuItem value="">All</MenuItem>}
        {statuses.map((status) => (
          <MenuItem
            key={status}
            value={status}
            sx={{ textTransform: 'capitalize' }}
          >
            {formatStatusLabel(status)}
          </MenuItem>
        ))}
      </Select>
    </Stack>
  )
}
