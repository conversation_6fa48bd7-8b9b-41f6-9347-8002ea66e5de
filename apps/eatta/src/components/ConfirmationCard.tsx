import { Stack, Typography } from '@mui/material'
import { SuccessIcon } from '@dtbx/ui/icons'

export interface ConfirmationCardProps {
  title: string
  description: string
  children: React.ReactNode
}

const ConfirmationCard: React.FC<ConfirmationCardProps> = ({
  title,
  description,
  children,
}) => {
  return (
    <Stack spacing={2}>
      <Typography variant="h6" fontWeight={600}>
        Registration Complete
      </Typography>

      <Stack
        spacing={4}
        direction="column"
        alignItems="center"
        sx={{
          padding: 5,
          backgroundColor: '#FFFFFF',
          borderRadius: '0.5rem',
        }}
      >
        <SuccessIcon />

        <Stack spacing={2} justifyContent="center" alignItems="center">
          <Typography variant="subtitle1" fontWeight={600}>
            {title}
          </Typography>

          <Typography textAlign="center">{description}</Typography>
        </Stack>

        {children}
      </Stack>
    </Stack>
  )
}

export default ConfirmationCard
