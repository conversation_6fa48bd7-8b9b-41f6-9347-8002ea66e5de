'use client'

import { Breadcrumbs, Typography } from '@mui/material'
import NavigateNextIcon from '@mui/icons-material/NavigateNext'
import { formatCustomDate } from '@dtbx/store/utils'

export interface SectionHeaderProps {
  children: React.ReactNode
  date: string
}
export interface AuctionBreadcrumbProps {
  title?: string
  date: string
}

export const AuctionBreadcrumb = ({ date }: AuctionBreadcrumbProps) => {
  const formatedDate = formatCustomDate(date)
  return (
    <>
      <Breadcrumbs
        aria-label="auction-breadcrumb"
        separator={<NavigateNextIcon color={'disabled'} fontSize="small" />}
      >
        <Typography
          sx={{
            color: '#475467',
            fontSize: '1rem',
            fontWeight: 500,
          }}
        >
          All Auctions
        </Typography>
        <Typography
          sx={{
            color: '#26B43B',
            fontSize: '1rem',
            fontWeight: 600,
          }}
        >
          {formatedDate}
        </Typography>
      </Breadcrumbs>
    </>
  )
}
