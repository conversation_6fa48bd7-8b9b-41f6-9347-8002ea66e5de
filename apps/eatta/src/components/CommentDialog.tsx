/**
 * <AUTHOR> on 30/05/2025
 */
import React, { FC, useState } from 'react'
import {
  <PERSON>ton,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  IconButton,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import CloseIcon from '@mui/icons-material/Close'
import { COMMENTS_REGEX } from '@/utils/validators'
import { ConfirmCancel } from './ConfirmCancel'

type CommentDialogProps = {
  open: boolean
  onClose: () => void
  onSubmit: (comment: string) => void
}

const validationSchema = Yup.object({
  comment: Yup.string()
    .required('Comment is required')
    .matches(COMMENTS_REGEX, 'Comment should not contain special characters'),
})

export const CommentDialog: FC<CommentDialogProps> = ({
  open,
  onClose,
  onSubmit,
}) => {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  const formik = useFormik({
    initialValues: {
      comment: '',
    },
    validationSchema,
    validateOnMount: true,
    onSubmit: async (values) => {
      onSubmit(values.comment)
    },
  })

  const handleClose = () => {
    if (formik.dirty) {
      setShowConfirmDialog(true)
    } else {
      onClose()
    }
  }

  const handleConfirmClose = () => {
    setShowConfirmDialog(false)
    onClose()
  }

  return (
    <>
      <Dialog fullWidth maxWidth="sm" open={open} onClose={handleClose}>
        <DialogTitle fontWeight={600}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography variant="subtitle1" fontWeight="bold">
              Comment
            </Typography>
            <IconButton
              sx={{
                height: '2rem',
                width: '2rem',
                border: '1px solid #D0D5DD',
                borderRadius: '0.5rem',
              }}
              onClick={handleClose}
            >
              <CloseIcon />
            </IconButton>
          </Stack>
        </DialogTitle>
        <Divider />
        <DialogContent>
          <FormikProvider value={formik}>
            <Form onSubmit={formik.handleSubmit}>
              <Stack width="100%" spacing={3}>
                <TextField
                  hiddenLabel
                  size="small"
                  type="text"
                  sx={{ marginBlock: '0' }}
                  multiline
                  rows={4}
                  margin={'normal'}
                  {...formik.getFieldProps('comment')}
                  fullWidth
                  onKeyDown={(e) => e.stopPropagation()}
                  error={Boolean(
                    formik.touched.comment && formik.errors.comment
                  )}
                  helperText={formik.touched.comment && formik.errors.comment}
                />

                <Stack spacing={2} direction="row">
                  <Button
                    fullWidth
                    type="button"
                    variant="outlined"
                    onClick={handleClose}
                  >
                    Cancel
                  </Button>
                  <Button
                    disabled={!formik.isValid}
                    fullWidth
                    type="submit"
                    variant="contained"
                  >
                    Submit
                  </Button>
                </Stack>
              </Stack>
            </Form>
          </FormikProvider>
        </DialogContent>
      </Dialog>

      <ConfirmCancel
        open={showConfirmDialog}
        title="Discard Changes"
        description="Are you sure you want to discard your changes?"
        confirmLabel="Discard"
        cancelLabel="Keep Editing"
        onConfirm={handleConfirmClose}
        onCancel={() => setShowConfirmDialog(false)}
      />
    </>
  )
}
