import React, { useState } from 'react'
import { Autocomplete, Stack, TextField, Select, MenuItem } from '@mui/material'
import { KeyboardArrowDownRounded } from '@mui/icons-material'

interface AutoCompleteOnFilterProps<T> {
  filterTypes: readonly string[]
  options: T[]
  loading?: boolean
  selectedFilterType?: string
  selectedOption?: T | null
  getOptionLabel: (option: T) => string
  onFilterTypeChange: (filterType: string) => void
  onOptionSelect: (option: T | null) => void
  onInputChange?: (inputValue: string) => void
  placeholder?: string
  noOptionsText?: string
  filterPlaceholder?: string
}

export const AutoCompleteOnFilter = <T,>({
  filterTypes,
  options,
  loading = false,
  selectedFilterType,
  selectedOption,
  getOptionLabel,
  onFilterTypeChange,
  onOptionSelect,
  onInputChange,
  placeholder,
  noOptionsText,
  filterPlaceholder,
}: AutoCompleteOnFilterProps<T>) => {
  const [inputValue, setInputValue] = useState('')

  const handleFilterTypeChange = (newType: string) => {
    onFilterTypeChange(newType)
    onOptionSelect(null)
    setInputValue('')
  }

  const handleOptionSelect = (option: T | null) => {
    onOptionSelect(option)
  }

  const handleInputChange = (newInputValue: string) => {
    setInputValue(newInputValue)
    onInputChange?.(newInputValue)
  }

  return (
    <Stack
      spacing={2}
      direction={{ xs: 'column', sm: 'row' }}
      width={{ xs: '100%', md: '50%' }}
      sx={{ minWidth: { xs: '100%', sm: 'auto' } }}
    >
      <FilterBox
        filterValues={filterTypes}
        selectedSearchBy={selectedFilterType || ''}
        onFilterChange={handleFilterTypeChange}
        placeholder={filterPlaceholder}
      />
      <Autocomplete
        sx={{
          width: { xs: '100%', sm: '70%' },
          minWidth: { xs: '200px', sm: 'auto' },
        }}
        size="small"
        loading={loading}
        options={options}
        getOptionLabel={getOptionLabel}
        value={selectedOption || null}
        onChange={(_, newValue) => handleOptionSelect(newValue)}
        inputValue={inputValue}
        onInputChange={(_, newInputValue) => handleInputChange(newInputValue)}
        renderInput={(params) => (
          <TextField
            {...params}
            hiddenLabel
            placeholder={
              placeholder || `Select ${selectedFilterType || 'option'}`
            }
          />
        )}
        noOptionsText={noOptionsText || 'No options found'}
      />
    </Stack>
  )
}

export const FilterBox = ({
  filterValues,
  selectedSearchBy,
  onFilterChange,
  placeholder,
}: {
  selectedSearchBy: string
  filterValues: readonly string[]
  onFilterChange: (value: string) => void
  placeholder?: string
}) => {
  return (
    <Stack direction={'row'}>
      <Select
        size="medium"
        onChange={(e) => onFilterChange(e.target.value)}
        IconComponent={(iconProps) => (
          <KeyboardArrowDownRounded
            {...iconProps}
            sx={{
              color: '#667085',
              marginLeft: '0.5rem',
              ...(iconProps?.sx || {}),
            }}
          />
        )}
        value={selectedSearchBy}
        displayEmpty
        sx={{
          width: '100%',
          '.MuiInputBase-input.MuiOutlinedInput-input': {
            py: '2px !important',
          },
          background: '#FFFFFF',
          '& fieldset': {
            border: `1px solid #D0D5DD !important`,
          },
          borderRadius: '8px',
          color: '#667085',
        }}
      >
        {placeholder && (
          <MenuItem value="" disabled>
            {placeholder}
          </MenuItem>
        )}
        {filterValues.map((value) => (
          <MenuItem key={value} value={value}>
            {value === 'Clear Filters' ? value : `Search for ${value}`}
          </MenuItem>
        ))}
      </Select>
    </Stack>
  )
}
