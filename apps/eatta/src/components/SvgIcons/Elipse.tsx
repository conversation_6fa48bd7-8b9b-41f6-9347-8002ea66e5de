import { ComponentProps } from 'react'

export const Elipse = (props: ComponentProps<'svg'>) => {
  const { width = 12, height = 12, fill = '#F59E0B', ...rest } = props
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 12 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <circle
        id="Ellipse 1"
        cx="6"
        cy="6"
        r="6"
        fill={fill}
        fillOpacity="0.933333"
      />
    </svg>
  )
}
