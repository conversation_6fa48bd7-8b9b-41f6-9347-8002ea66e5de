
import { ComponentProps } from 'react'

export const DownloadPng = (props: ComponentProps<'svg'>) => {
  const {
    width = '25',
    height = '24',
    fill = 'none',
    stroke = '#667085',
    strokeWidth = '2',
    ...rest
  } = props
  return (
    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4.7998 4C4.7998 1.79086 6.59067 0 8.79981 0H28.7998L43.1998 14.4V44C43.1998 46.2091 41.4089 48 39.1998 48H8.7998C6.59066 48 4.7998 46.2091 4.7998 44V4Z" fill="#00BC2D"/>
    <path opacity="0.3" d="M28.7998 0L43.1998 14.4H32.7998C30.5907 14.4 28.7998 12.6091 28.7998 10.4V0Z" fill="white"/>
    <path d="M14.9227 36.6V30.0545H17.5051C18.0015 30.0545 18.4245 30.1493 18.7739 30.339C19.1233 30.5265 19.3897 30.7875 19.5729 31.122C19.7583 31.4544 19.851 31.8379 19.851 32.2726C19.851 32.7072 19.7572 33.0907 19.5697 33.4231C19.3822 33.7555 19.1106 34.0144 18.7547 34.1998C18.401 34.3851 17.9728 34.4778 17.4699 34.4778H15.824V33.3688H17.2462C17.5125 33.3688 17.732 33.323 17.9046 33.2314C18.0793 33.1376 18.2093 33.0087 18.2945 32.8446C18.3819 32.6785 18.4255 32.4878 18.4255 32.2726C18.4255 32.0552 18.3819 31.8656 18.2945 31.7037C18.2093 31.5396 18.0793 31.4128 17.9046 31.3233C17.7299 31.2317 17.5083 31.1859 17.2398 31.1859H16.3066V36.6H14.9227ZM26.2246 30.0545V36.6H25.0293L22.1817 32.4803H22.1337V36.6H20.7498V30.0545H21.9643L24.7896 34.171H24.8472V30.0545H26.2246ZM31.7082 32.1703C31.6635 32.0147 31.6006 31.8773 31.5196 31.758C31.4387 31.6366 31.3396 31.5343 31.2224 31.4512C31.1074 31.366 30.9753 31.301 30.8261 31.2562C30.6791 31.2115 30.5161 31.1891 30.3371 31.1891C30.0026 31.1891 29.7086 31.2722 29.455 31.4384C29.2036 31.6046 29.0076 31.8464 28.8669 32.1639C28.7263 32.4792 28.656 32.8649 28.656 33.3209C28.656 33.7768 28.7253 34.1646 28.8638 34.4842C29.0022 34.8038 29.1983 35.0478 29.4518 35.2161C29.7054 35.3823 30.0047 35.4654 30.3499 35.4654C30.6631 35.4654 30.9305 35.41 31.1521 35.2992C31.3758 35.1863 31.5463 35.0275 31.6635 34.823C31.7828 34.6184 31.8424 34.3766 31.8424 34.0975L32.1237 34.139H30.4362V33.0971H33.1752V33.9217C33.1752 34.497 33.0537 34.9913 32.8108 35.4047C32.5679 35.8159 32.2334 36.1334 31.8073 36.3571C31.3812 36.5787 30.8932 36.6895 30.3435 36.6895C29.7299 36.6895 29.1908 36.5542 28.7263 36.2836C28.2618 36.0108 27.8996 35.6241 27.6397 35.1234C27.3819 34.6206 27.253 34.024 27.253 33.3336C27.253 32.8031 27.3297 32.3301 27.4831 31.9146C27.6386 31.497 27.8559 31.1433 28.1351 30.8535C28.4142 30.5638 28.7391 30.3432 29.1098 30.1919C29.4806 30.0407 29.8822 29.965 30.3147 29.965C30.6855 29.965 31.0307 30.0194 31.3503 30.128C31.6699 30.2346 31.9532 30.3858 32.2004 30.5819C32.4497 30.7779 32.6532 31.0112 32.8108 31.2818C32.9685 31.5503 33.0697 31.8464 33.1145 32.1703H31.7082Z" fill="white"/>
    </svg>
  )
}
