import { ComponentProps } from 'react'

/**
 * <AUTHOR> on 09/05/2025
 */
export const PreAuctionIcon = (props: ComponentProps<'svg'>) => {
  const {
    width = 24,
    height = 24,
    stroke = '#667085',
    strokeWidth = 2,
    ...rest
  } = props
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      {...rest}
    >
      <path
        d="M20 10.5001V6.80012C20 5.11996 20 4.27989 19.673 3.63815C19.3854 3.07366 18.9265 2.61472 18.362 2.3271C17.7202 2.00012 16.8802 2.00012 15.2 2.00012H8.8C7.11984 2.00012 6.27976 2.00012 5.63803 2.3271C5.07354 2.61472 4.6146 3.07366 4.32698 3.63815C4 4.27989 4 5.11996 4 6.80012V17.2001C4 18.8803 4 19.7204 4.32698 20.3621C4.6146 20.9266 5.07354 21.3855 5.63803 21.6731C6.27976 22.0001 7.11984 22.0001 8.8 22.0001H12M18 21.0001V15.0001M15 18.0001H21"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const CustomersIcon = (props: ComponentProps<'svg'>) => {
   const {
     width = 24,
     height = 24,
     stroke = '#667085',
     strokeWidth = 2,
     ...rest
   } = props
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M18.6344 17.5V15.8333C18.6344 14.2801 17.5721 12.9751 16.1344 12.605M13.2178 2.7423C14.4394 3.23679 15.3011 4.43443 15.3011 5.83333C15.3011 7.23224 14.4394 8.42988 13.2178 8.92437M14.4678 17.5C14.4678 15.9469 14.4678 15.1703 14.214 14.5577C13.8757 13.741 13.2268 13.092 12.4101 12.7537C11.7975 12.5 11.0209 12.5 9.46777 12.5H6.96777C5.41463 12.5 4.63807 12.5 4.0255 12.7537C3.20874 13.092 2.55982 13.741 2.22151 14.5577C1.96777 15.1703 1.96777 15.9469 1.96777 17.5M11.5511 5.83333C11.5511 7.67428 10.0587 9.16667 8.21777 9.16667C6.37682 9.16667 4.88444 7.67428 4.88444 5.83333C4.88444 3.99238 6.37682 2.5 8.21777 2.5C10.0587 2.5 11.5511 3.99238 11.5511 5.83333Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const PostAuctionIcon = (props: ComponentProps<'svg'>) => {
  const {
    width = 24,
    height = 24,
    stroke = '#667085',
    strokeWidth = 2,
    ...rest
  } = props
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
      viewBox="0 0 24 24"
      fill="none"
      {...rest}
    >
      <path
        d="M20 12.5001V6.80012C20 5.11996 20 4.27989 19.673 3.63815C19.3854 3.07366 18.9265 2.61472 18.362 2.3271C17.7202 2.00012 16.8802 2.00012 15.2 2.00012H8.8C7.11984 2.00012 6.27976 2.00012 5.63803 2.3271C5.07354 2.61472 4.6146 3.07366 4.32698 3.63815C4 4.27989 4 5.11996 4 6.80012V17.2001C4 18.8803 4 19.7204 4.32698 20.3621C4.6146 20.9266 5.07354 21.3855 5.63803 21.6731C6.27976 22.0001 7.11984 22.0001 8.8 22.0001H12M14 11.0001H8M10 15.0001H8M16 7.00012H8M14.5 19.0001L16.5 21.0001L21 16.5001"
        stroke="#667085"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
