/**
 * <AUTHOR> on 30/04/2025
 */
import { Accordion, AccordionProps, styled } from '@mui/material'
import React from 'react'

export const CustomAccordion = styled((props: AccordionProps) => (
  <Accordion {...props} />
))(({ theme }) => ({
  boxShadow: 'none',
  '&.MuiAccordion-root': {
    borderRadius: 0,
    overflow: 'hidden',
    margin: 0,
    '&:before': {
      display: 'none',
    },
  },
  '& .MuiAccordionSummary-root': {
    minHeight: '4rem',
    padding: '0 1.5rem',
  },
  '& .MuiAccordionSummary-content': {
    margin: '0.75rem 0',
    width: '100%',
  },
  '& .MuiAccordionDetails-root': {
    padding: 0,
  },
}))
