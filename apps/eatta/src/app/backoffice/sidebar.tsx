/**
 * <AUTHOR> on 29/10/2024
 */
import { ISidebarConfigItem } from '@dtbx/ui/components/Sidebar'
import {
  CompaniesIcon,
  RequestsApprovalIcon,
  SettingsIcon,
} from '@dtbx/ui/icons'
import {
  PostAuctionIcon,
  PreAuctionIcon,
  CustomersIcon,
} from '@/components/SvgIcons/AuctionIcon'
import { MoneyInIcon, MoneyOutIcon } from '@/components/SvgIcons/SidebarIcons'

export const sidebarConfig: ISidebarConfigItem[] = [
  {
    id: '1',
    title: 'Pre-Sale',
    path: '/backoffice/pre-auction',
    module: 'default',
    icon: <PreAuctionIcon />,
    isProductionReady: true,
  },
  {
    id: '2',
    title: 'Sale',
    path: '/backoffice/post-auction',
    module: 'default',
    icon: <PostAuctionIcon />,
    isProductionReady: true,
  },
  {
    id: '3',
    title: 'Money In',
    path: '/backoffice/payments',
    module: 'default',
    icon: <MoneyInIcon stroke="#667085" />,
    isProductionReady: true,
  },
  {
    id: '4',
    title: 'Money Out',
    path: '/backoffice/transactions',
    module: 'default',
    icon: <MoneyOutIcon stroke="#667085" />,
    isProductionReady: true,
  },
  {
    id: '5',
    title: 'Companies',
    path: '/backoffice/companies',
    module: 'default',
    icon: <CompaniesIcon stroke="#667085" />,
    isProductionReady: true,
  },
  {
    id: '6',
    title: 'Members',
    path: '/backoffice/members',
    module: 'default',
    icon: <CustomersIcon stroke="#667085" />,
    isProductionReady: true,
  },
  {
    id: '7',
    title: 'Approval Requests',
    path: '/backoffice/approval-requests',
    module: 'default',
    icon: <RequestsApprovalIcon stroke="#667085" />,
    isProductionReady: true,
  },
  {
    id: '8',
    title: 'Settings',
    path: '/backoffice/settings',
    module: 'default',
    icon: <SettingsIcon stroke="#667085" />,
    isProductionReady: true,
  },
]
