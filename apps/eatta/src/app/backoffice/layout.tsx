/**
 * <AUTHOR> on 23/10/2024
 */
'use client'

import React, { ReactNode } from 'react'
import { notFound } from 'next/navigation'
import { useAppSelector, useAppDispatch } from '@/store'
import { refreshToken } from '@dtbx/store/actions'
import { clearNotification, setSidebarCollapsed } from '@dtbx/store/reducers'
import { AuthWrapper, InActivity, LocalNotification } from '@dtbx/ui/components'
import { Box } from '@mui/material'
import { Sidebar } from '@dtbx/ui/components/Sidebar'
import { InternalNavBar } from '@dtbx/ui/components/Appbar'

import { sidebarConfig } from './sidebar'
import { isLoggedIn } from '@dtbx/store/utils'

export default function BackOfficeLayout({
  children,
}: {
  children: ReactNode
}) {
  const dispatch = useAppDispatch()
  if (process.env.NEXT_PUBLIC_EATTA_BUILD === 'client') {
    return notFound()
  }
  const { isSidebarCollapsed } = useAppSelector((state) => state.navigation)
  const profile = useAppSelector((state) => state.auth.decodedToken)
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  )
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    'info'
  return (
    <InActivity isLoggedIn={isLoggedIn}>
      <AuthWrapper requiresAuth={true} isLoggedIn={isLoggedIn}>
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'row' }}>
          <Sidebar
            bgColor="#FFFFFF"
            sidebarConfig={sidebarConfig}
            sidebarCollapsed={(val) => dispatch(setSidebarCollapsed(val))}
          />
          <Box
            sx={{
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
              width: isSidebarCollapsed ? '95vw' : '88vw',
            }}
          >
            <InternalNavBar profile={profile} refreshToken={refreshToken} />
            <Box
              sx={{
                overflow: 'hidden',
                width: '100%',
                height: '100%',
                backgroundColor: '#F2F4F7',
              }}
            >
              <LocalNotification
                clearNotification={() => dispatch(clearNotification())}
                notification={notification}
                notificationType={notificationType}
              />
              {children}
            </Box>
          </Box>
        </Box>
      </AuthWrapper>
    </InActivity>
  )
}
