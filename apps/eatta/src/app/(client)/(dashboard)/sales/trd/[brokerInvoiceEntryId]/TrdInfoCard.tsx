'use client'

import React from 'react'
import { Typo<PERSON>, Divider, Stack, Card, CardContent } from '@mui/material'
import { useAppSelector } from '@/store'

export interface TrdDocumentInfoProps {
  title: string
  trdNumber: string
  buyerName: string
}

export interface TrdSaleInfoProps {
  saleNumber: string
  auctionDate: string
  datePaid: string
  trdDate: string
}

export interface TrdBrokerInfoProps {
  broker: string
  brokerId: string
  warehouse: string
  warehouseId: string
}

export interface TrdPaymentInfoProps {
  title: string
  totalAmount: number
}

export interface TrdInfoCardProps {
  brokerInvoiceEntryId?: string
}

const TrdInfoCard: React.FC<TrdInfoCardProps> = ({}) => {
  const { isLoadingTRDInfo, invoiceData } = useAppSelector(
    (state) => state.catalogues
  )

  if (isLoadingTRDInfo) {
    return (
      <Stack alignItems="center" justifyContent="center" sx={{ p: 4 }}>
        <Typography>Loading TRD data...</Typography>
      </Stack>
    )
  }
  if (!invoiceData) return null

  return (
    <Stack
      sx={{
        p: 3,
        backgroundColor: '#FFFFFF',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
        borderRadius: '10px',
      }}
    >
      <Stack
        sx={{
          width: 84,
          height: 84,
          mx: 'auto',
          mb: 3,
        }}
      >
        <img src="/green-tea.png" alt="Tea leaf icon" />
      </Stack>

      <TrdDocumentInfo
        title={'Tea Release Document'}
        trdNumber={`TRD - ${invoiceData.lotNumber || ''}`}
        buyerName={invoiceData.buyerCode || ''}
      />
      <Divider sx={{ my: 2 }} />
      <TrdSaleInfo
        saleNumber={invoiceData.brokerInvoice?.saleNumber || ''}
        auctionDate={invoiceData.brokerInvoice?.auctionDate || ''}
        datePaid={invoiceData.datePaid || ''}
        trdDate={invoiceData.teaReleaseDocumentDate || ''}
      />
      <Divider sx={{ my: 2 }} />
      <TrdBrokerInfo
        broker={invoiceData.auctionEntry?.broker || ''}
        brokerId={invoiceData.brokerInvoice?.brokerCode || ''}
        warehouse={invoiceData.auctionEntry?.wareHouse || ''}
        warehouseId={invoiceData.auctionEntry?.wareHouse || ''}
      />
      <Divider sx={{ my: 2 }} />
      <TrdPaymentInfo
        title={'Amount Paid'}
        totalAmount={invoiceData.totalValue || 0}
      />
      <Stack spacing={2} alignItems="center">
        <Typography
          sx={{ fontSize: '0.875rem', fontWeight: 600, color: '#667085' }}
        >
          In Payment of
        </Typography>
        <Stack spacing={2} width="100%">
          <Card variant="outlined" sx={{ borderRadius: 2 }}>
            <CardContent>
              <Stack display="flex" flexDirection="column" gap={2}>
                <Stack direction="row" justifyContent="space-between">
                  <Typography
                    sx={{
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      color: '#667085',
                    }}
                  >
                    Lot No. {invoiceData.lotNumber}
                  </Typography>
                  <Typography
                    sx={{
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      color: '#667085',
                    }}
                  >
                    Net Weight: {invoiceData.netWeight}
                  </Typography>
                </Stack>
                <Stack direction="row" justifyContent="space-between">
                  <Typography
                    sx={{ fontSize: '1rem', fontWeight: 600, color: '#101828' }}
                  >
                    GDN Invoice: {invoiceData.gardenInvoice} (Grade:
                    {invoiceData.grade})
                  </Typography>
                  <Typography
                    sx={{ fontSize: '1rem', fontWeight: 600, color: '#101828' }}
                  >
                    $ {invoiceData.netAmount}
                  </Typography>
                </Stack>
              </Stack>
            </CardContent>
          </Card>
        </Stack>
      </Stack>
    </Stack>
  )
}

const TrdDocumentInfo: React.FC<TrdDocumentInfoProps> = ({
  title,
  trdNumber,
  buyerName,
}) => (
  <Stack spacing={1} alignItems="center">
    <Typography sx={{ fontSize: '1rem', fontWeight: 600, color: '#101828' }}>
      {title}
    </Typography>
    <Typography
      sx={{ fontSize: '0.875rem', fontWeight: 500, color: '#101828' }}
    >
      {trdNumber}
    </Typography>
    <Stack direction="row" spacing={1} alignItems="center">
      <Typography
        sx={{ fontSize: '0.875rem', fontWeight: 600, color: '#667085' }}
      >
        Buyer:
      </Typography>
      <Typography
        sx={{ fontSize: '0.875rem', fontWeight: 600, color: '#182230' }}
      >
        {buyerName}
      </Typography>
    </Stack>
  </Stack>
)

const TrdSaleInfo: React.FC<TrdSaleInfoProps> = ({
  saleNumber,
  auctionDate,
  datePaid,
  trdDate,
}) => (
  <Stack
    spacing={2}
    direction={{ xs: 'column', sm: 'row', md: 'row' }}
    justifyContent="space-between"
    sx={{ fontSize: '0.875rem', fontWeight: 600, color: '#101828' }}
  >
    <Stack spacing={1} sx={{ alignItems: 'flex-start' }}>
      <SaleDetail label="Sale number:" value={saleNumber || 'Sale 36 - M2'} />
      <SaleDetail label="Auction Date:" value={auctionDate || 'June 4'} />
    </Stack>
    <Stack spacing={1} sx={{ alignItems: 'flex-start' }}>
      <SaleDetail label="Date Paid:" value={datePaid} />
      <SaleDetail label="TRD Date:" value={trdDate} />
    </Stack>
  </Stack>
)

const TrdBrokerInfo: React.FC<TrdBrokerInfoProps> = ({
  broker,
  brokerId,
  warehouse,
  warehouseId,
}) => (
  <Stack
    spacing={2}
    direction={{ xs: 'column', sm: 'row', md: 'row' }}
    justifyContent="space-between"
  >
    <Stack spacing={1} sx={{ alignItems: 'flex-start' }}>
      <SaleDetail label="Broker:" value={broker} />
      <SaleDetail label="Broker ID:" value={brokerId} />
    </Stack>
    <Stack spacing={1} sx={{ alignItems: 'flex-start' }}>
      <SaleDetail label="Warehouse:" value={warehouse} />
      <SaleDetail label="Warehouse ID:" value={warehouseId} />
    </Stack>
  </Stack>
)

const TrdPaymentInfo: React.FC<TrdPaymentInfoProps> = ({
  title,
  totalAmount,
}) => (
  <Stack spacing={1} sx={{ alignItems: 'center', mb: 3 }}>
    <Typography
      sx={{ fontSize: '0.875rem', fontWeight: 400, color: '#344054' }}
    >
      {title}
    </Typography>
    <Typography sx={{ fontSize: '1.25rem', fontWeight: 700, color: '#344054' }}>
      USD {totalAmount}
    </Typography>
  </Stack>
)

const SaleDetail: React.FC<{ label: string; value: string }> = ({
  label,
  value,
}) => (
  <Stack direction="row" spacing={1}>
    <Typography
      component="span"
      sx={{ color: '#101828', fontWeight: 600, fontSize: '0.875rem' }}
    >
      {label}
    </Typography>
    <Typography
      component="span"
      sx={{ color: '#667085', fontWeight: 600, fontSize: '0.875rem' }}
    >
      {value}
    </Typography>
  </Stack>
)

export default TrdInfoCard
