'use server'
import { encryptAndSignFile } from '@/utils/fileEncryption'

/**
 * <AUTHOR> on 14/05/2025
 */
export type EncryptFileResponse = {
  encryptedFile?: File
  error?: string
}

export async function encryptFile(
  formData: FormData
): Promise<EncryptFileResponse> {
  const file = formData.get('file')

  if (!file || !(file instanceof File)) {
    return { error: 'File not found or invalid' }
  }

  const armoredPrivateKey = process.env.NEXT_PUBLIC_SIGNINGKEY
  const armoredPublicKey = process.env.NEXT_PUBLIC_ENCRYPTION_KEY
  const passphrase = process.env.NEXT_PUBLIC_PASSPHRASE

  if (!armoredPrivateKey || !armoredPublicKey || !passphrase) {
    return { error: 'Keys not found' }
  }

  const encrypted = await encryptAndSignFile(
    file,
    atob(armoredPrivateKey),
    atob(armoredPublicKey),
    passphrase
  )
  const encryptedFile = new File([encrypted], `${file.name}.pgp`, {
    type: 'application/octet-stream',
  })
  return { encryptedFile }
}
