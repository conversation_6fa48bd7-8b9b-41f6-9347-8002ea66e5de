'use client'
import { useAppSelector } from '@dtbx/store'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useEffect } from 'react'
import { ClientType } from '@dtbx/store/interfaces'

const Page = () => {
  const { decodedToken } = useAppSelector((state) => state.auth)
  const router = useCustomRouter()

  useEffect(() => {
    if (!decodedToken.clientType) {
      return
    }

    const routeMap: Record<ClientType, string> = {
      Buyer: '/pre-auction',
      Broker: '/pre-auction',
      Producer: '/post-auction',
      Partner: '/insights',
      Warehouse: '/pre-auction',
    }

    router.push(routeMap[decodedToken.clientType] || '/not-found')
  }, [decodedToken, router])

  if (!decodedToken) {
    return <div>Loading...</div> // Optional loading state if token isn't ready
  }

  return null
}

export default Page
