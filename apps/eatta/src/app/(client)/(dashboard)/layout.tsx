/**
 * <AUTHOR> on 23/10/2024
 */
'use client'
import { ReactNode, useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { clearNotification, setSidebarCollapsed } from '@dtbx/store/reducers'
import { AuthWrapper, InActivity, LocalNotification } from '@dtbx/ui/components'
import { Box } from '@mui/material'
import { useRouter } from 'next/navigation'
import { Sidebar } from '@dtbx/ui/components/Sidebar'
import { isLoggedIn } from '@dtbx/store/utils'

import { handleLogout } from '@/store/actions/eattaAuth'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { getSidebarConfig } from './sidebar'
import SideBarFooter from '@/components/SideBarFooter'
import NotFound from '@/app/not-found'
import { scheduleTokenRefresh } from '@/utils/refreshSchedule'

export default function ClientLayout({ children }: { children: ReactNode }) {
  const dispatch = useAppDispatch()
  const router = useRouter()

  const isBackOffice = checkIfBackOffice()

  if (isBackOffice) {
    return NotFound()
  }
  const { decodedToken } = useAppSelector((state) => state.auth)
  const { isSidebarCollapsed } = useAppSelector((state) => state.navigation)
  const notification = useAppSelector(
    (state) => state.notifications.localNotification
  )
  const notificationType =
    useAppSelector((state) => state.notifications.localNotificationType) ||
    'info'

  useEffect(() => {
    scheduleTokenRefresh()
  }, [])

  return (
    <InActivity
      isLoggedIn={isLoggedIn}
      timeout={1000 * 60 * 15} // 15 minutes
      onLogout={() => handleLogout(dispatch, router)}
    >
      <AuthWrapper
        requiresAuth={true}
        isLoggedIn={isLoggedIn}
        loginUrl="/auth"
        homeUrl="/"
      >
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'row' }}>
          <Sidebar
            bgColor="#FFFFFF"
            sidebarConfig={getSidebarConfig(decodedToken.clientType)}
            sidebarCollapsed={(val) => dispatch(setSidebarCollapsed(val))}
            footer={<SideBarFooter />}
          />

          <Box
            sx={{
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column',
              width: isSidebarCollapsed ? '95vw' : '88vw',
            }}
          >
            <Box
              sx={{
                width: '100%',
                height: '100%',
                backgroundColor: '#F2F4F7',
              }}
            >
              <LocalNotification
                clearNotification={() => dispatch(clearNotification())}
                notification={notification}
                notificationType={notificationType}
              />
              {children}
            </Box>
          </Box>
        </Box>
      </AuthWrapper>
    </InActivity>
  )
}
