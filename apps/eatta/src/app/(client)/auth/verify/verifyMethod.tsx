'use client'

import { useCustomRouter } from '@dtbx/ui/hooks'
import { Box, Button, Stack, Typography } from '@mui/material'
import { Logo } from '@/components/SvgIcons/Logo'
import { CustomRadioButton } from '@/components/CustomRadioButton'
import { Form, FormikProvider, useFormik } from 'formik'
import React from 'react'
import * as Yup from 'yup'
import { useAppDispatch, useAppSelector } from '@/store'
import { generateOTP } from '@/store/actions/eattaAuth'

export type DeliveryMode = 'EMAIL' | 'SMS'

export interface FormValues {
  deliveryMode: DeliveryMode | ''
}

const validationSchema = Yup.object({
  deliveryMode: Yup.string()
    .oneOf(['SMS', 'EMAIL'] as const, 'Please select a valid delivery mode')
    .required('Please select Email or Phone number'),
})

export const VerifyMethod = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { userInfo } = useAppSelector((state) => state.auth)

  const formik = useFormik<FormValues>({
    initialValues: {
      deliveryMode: '',
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      try {
        if (!userInfo?.username) {
          throw new Error('No username found')
        }

        // Generate OTP with selected delivery mode
        const response = await generateOTP(
          userInfo.username,
          values.deliveryMode as 'SMS' | 'EMAIL',
          dispatch,
          router
        )

        if (response && response.status === 'SUCCESS') {
          // Store delivery mode for masking
          localStorage.setItem('deliveryMode', values.deliveryMode)

          // Set OTP generation timestamp when first generated
          localStorage.setItem('otpGeneratedAt', Date.now().toString())

          // Store masked contact
          const contactInfo =
            values.deliveryMode === 'EMAIL'
              ? userInfo.email?.replace(
                  /(.{2})(.*)(?=@)/,
                  (_, start, rest) => start + '*'.repeat(rest.length)
                )
              : userInfo.phoneNumber?.replace(/(\d{4})(\d+)(\d{3})/, '$1****$3')

          localStorage.setItem('maskedContact', contactInfo || '')

          router.replace('/auth/confirm')
        }
      } catch (error) {
        console.error('Error generating OTP:', error)
      }
    },
  })

  const getContactPreview = (mode: DeliveryMode) => {
    if (!userInfo) return ''

    return mode === 'EMAIL'
      ? userInfo.email?.replace(
          /(.{2})(.*)(?=@)/,
          (_, start, rest) => start + '*'.repeat(rest.length)
        )
      : userInfo.phoneNumber?.replace(/(\d{4})(\d+)(\d{3})/, '$1****$3')
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        maxWidth: '22.5rem',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '2rem',
      }}
    >
      <Stack direction="column" alignItems="center" spacing={5} useFlexGap>
        <Logo />
        <Stack direction="column" alignItems="center" spacing={1} useFlexGap>
          <Typography variant="h5" fontWeight="600">
            Get a verification code
          </Typography>
          <Typography variant="body2" fontWeight="300" textAlign="center">
            Please choose how you would like to receive your <br /> verification
            code.
          </Typography>
        </Stack>
      </Stack>

      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <Stack spacing={3} useFlexGap>
            <Box>
              <Stack direction="column" useFlexGap spacing={2}>
                {[
                  { mode: 'EMAIL', label: 'Email' },
                  { mode: 'SMS', label: 'Phone number' },
                ].map(({ mode, label }) => (
                  <Box
                    key={mode}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: 2,
                      borderRadius: 2,
                      border:
                        formik.values.deliveryMode === mode
                          ? '2px solid #26b43b'
                          : '1px solid #ccc',
                      backgroundColor:
                        formik.values.deliveryMode === mode
                          ? '#f5fff5'
                          : 'transparent',
                      cursor: 'pointer',
                    }}
                    onClick={() => formik.setFieldValue('deliveryMode', mode)}
                  >
                    <Stack spacing={0.5}>
                      <Typography variant="body2">{label}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {getContactPreview(mode as DeliveryMode)}
                      </Typography>
                    </Stack>
                    <CustomRadioButton
                      checked={formik.values.deliveryMode === mode}
                      customColor={'#26b43b'}
                      style={{ padding: '2px' }}
                    />
                  </Box>
                ))}
              </Stack>
            </Box>

            <Button
              disabled={!formik.values.deliveryMode || !formik.isValid}
              variant="contained"
              type="submit"
              fullWidth
              sx={{
                minWidth: '300px',
                py: 2,
                backgroundColor: '#26b43b',
                '&:hover': {
                  backgroundColor: '#1a8f2c',
                },
                '&:disabled': {
                  backgroundColor: '#ccc',
                },
              }}
            >
              Continue
            </Button>
          </Stack>
        </Form>
      </FormikProvider>
    </Box>
  )
}
