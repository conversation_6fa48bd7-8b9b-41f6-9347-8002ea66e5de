'use client'

import React, { useRef, useState, useEffect } from 'react'
import { Box, Button, Stack, Typography } from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import { Logo } from '../../../../components/SvgIcons/Logo'
import { useAppDispatch, useAppSelector } from '@/store'
import { generateOTP, verifyOTP } from '@/store/actions/eattaAuth'
import { useRouter } from 'next/navigation'
import { setNotification } from '@dtbx/store/reducers'
import CountdownTimer from '@/components/CountdownTimer'

const validationSchema = Yup.object({
  otp: Yup.string()
    .matches(/^\d{6}$/, 'Please enter a valid 6-digit code.')
    .required('You entered an invalid or expired code.'),
})

export const OTPVerification = () => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const { userInfo } = useAppSelector((state) => state.auth)
  const [isInvalidOTP, setIsInvalidOTP] = useState(false)
  const [isGeneratingOtp, setIsGeneratingOtp] = useState(false)
  const [resendDisabled, setResendDisabled] = useState(true)
  const [timerResetKey, setTimerResetKey] = useState(0)
  const [maskedContact, setMaskedContact] = useState('')
  const [deliveryMode, setDeliveryMode] = useState<'SMS' | 'EMAIL'>('SMS')
  const [elapsedTime, setElapsedTime] = useState(0)

  const inputRefs = Array(6)
    .fill(null)
    .map(() => useRef<HTMLInputElement>(null))

  const formik = useFormik({
    initialValues: {
      otp: '',
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      if (!userInfo?.username) {
        dispatch(
          setNotification({
            message: 'Session expired. Please login again.',
            type: 'error',
          })
        )
        return
      }

      try {
        const success = await verifyOTP(
          {
            username: userInfo.username,
            otp: values.otp,
          },
          dispatch,
          router
        )

        if (!success) {
          setIsInvalidOTP(true)
          formik.setFieldError('otp', 'You entered an invalid or expired code.')
        }
      } catch (error: any) {
        setIsInvalidOTP(true)
        dispatch(
          setNotification({
            message: 'Verification failed. Please try again.',
            type: 'error',
          })
        )
      }
    },
  })

  useEffect(() => {
    if (typeof window === 'undefined') return

    const storedContact = window.localStorage.getItem('maskedContact')
    const storedDeliveryMode = window.localStorage.getItem('deliveryMode') as
      | 'SMS'
      | 'EMAIL'
    const otpGeneratedAt = window.localStorage.getItem('otpGeneratedAt')

    if (storedContact) {
      setMaskedContact(storedContact)
    }

    if (storedDeliveryMode) {
      setDeliveryMode(storedDeliveryMode)
    }

    if (!otpGeneratedAt) {
      router.replace('/auth')
      return
    }

    const elapsedMs = Date.now() - parseInt(otpGeneratedAt)
    const remainingMs = Math.max(0, 100000 - elapsedMs)

    setIsGeneratingOtp(true)
    setResendDisabled(true)

    if (remainingMs === 0) {
      handleTimerExpire()
    }
  }, [router])

  useEffect(() => {
    if (typeof window === 'undefined') return

    const otpGeneratedAt = window.localStorage.getItem('otpGeneratedAt')
    if (otpGeneratedAt) {
      const elapsed = Math.min(
        100,
        Math.floor((Date.now() - Number.parseInt(otpGeneratedAt)) / 1000)
      )
      setElapsedTime(elapsed)
    }
  }, [])

  const handleTimerExpire = () => {
    setIsGeneratingOtp(false)
    setResendDisabled(false)
  }

  const handleResendCode = async () => {
    if (resendDisabled || !userInfo?.username) return

    try {
      const response = await generateOTP(
        userInfo.username,
        deliveryMode,
        dispatch,
        router
      )

      if (response && 'status' in response && response.status === 'SUCCESS') {
        if (typeof window !== 'undefined') {
          window.localStorage.setItem('otpGeneratedAt', Date.now().toString())
        }
        formik.resetForm()
        inputRefs[0].current?.focus()

        setIsGeneratingOtp(true)
        setResendDisabled(true)
        setTimerResetKey((prev) => prev + 1)
      }
    } catch (error: any) {
      console.error(
        'Failed to send verification code. Please try to login again.'
      )
    }
  }

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const value = e.target.value.replace(/\D/g, '')
    if (value.length > 1) return

    setIsInvalidOTP(false)
    const newOtp =
      formik.values.otp.substring(0, index) +
      value +
      formik.values.otp.substring(index + 1)

    formik.setFieldValue('otp', newOtp)

    // Move to next input
    if (value && index < 5 && inputRefs[index + 1]?.current) {
      inputRefs[index + 1].current?.focus()
    }
  }

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === 'Backspace' && !formik.values.otp[index] && index > 0) {
      inputRefs[index - 1].current?.focus()
    }
  }

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault()
    const pastedData = e.clipboardData
      .getData('text')
      .replace(/\D/g, '')
      .slice(0, 6)
    if (pastedData) {
      formik.setFieldValue('otp', pastedData)
      const focusIndex = Math.min(pastedData.length, 5)
      inputRefs[focusIndex].current?.focus()
    }
  }

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        maxWidth: '22.5rem',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '2rem',
      }}
    >
      <Stack direction="column" alignItems="center" spacing={5}>
        <Logo />
        <Stack direction="column" alignItems="center" spacing={1}>
          <Typography variant="h5" fontWeight="600">
            Enter one-time code
          </Typography>
          <Typography variant="body2" fontWeight="300" textAlign="center">
            {`A new code is on its way to ${maskedContact}. This code will expire in 100 seconds.`}
          </Typography>
        </Stack>
        {isGeneratingOtp && (
          <CountdownTimer
            initialSeconds={100}
            isGeneratingOtp={isGeneratingOtp}
            onExpire={handleTimerExpire}
            resetKey={timerResetKey}
          />
        )}
      </Stack>

      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <Stack spacing={3} alignItems="center">
            <Box
              display="flex"
              gap={1}
              sx={{ flexDirection: 'column', alignItems: 'center' }}
            >
              <Box display="flex" gap={1}>
                {Array(6)
                  .fill(null)
                  .map((_, index) => (
                    <React.Fragment key={index}>
                      <input
                        ref={inputRefs[index]}
                        type="text"
                        value={formik.values.otp[index] || ''}
                        onChange={(e) => handleChange(e, index)}
                        onKeyDown={(e) => handleKeyDown(e, index)}
                        onPaste={index === 0 ? handlePaste : undefined}
                        aria-label={`OTP digit ${index + 1}`}
                        title={`Enter digit ${index + 1}`}
                        maxLength={1}
                        style={{
                          width: '2.5rem',
                          height: '3rem',
                          textAlign: 'center',
                          fontSize: '1.5rem',
                          fontWeight: '500',
                          borderRadius: '0.5rem',
                          border:
                            isInvalidOTP ||
                            (formik.errors.otp && formik.touched.otp)
                              ? '2px solid #d32f2f'
                              : formik.values.otp && !formik.errors.otp
                                ? '2px solid #26b43b'
                                : '2px solid #ccc',
                          color:
                            isInvalidOTP ||
                            (formik.errors.otp && formik.touched.otp)
                              ? '#d32f2f'
                              : formik.values.otp && !formik.errors.otp
                                ? '#26b43b'
                                : 'inherit',
                          outline: 'none',
                          backgroundColor: '#f9f9f9',
                          transition: 'all 0.3s ease',
                        }}
                      />
                      {index === 2 && (
                        <span
                          style={{ fontSize: '1.5rem', fontWeight: 'bold' }}
                        >
                          -
                        </span>
                      )}
                    </React.Fragment>
                  ))}
              </Box>
              {formik.errors.otp && formik.touched.otp && (
                <Typography color="#d32f2f" fontSize="0.75rem" sx={{ mt: 1 }}>
                  {formik.errors.otp}
                </Typography>
              )}
            </Box>

            <Button
              disabled={formik.values.otp.length !== 6}
              variant="contained"
              type="submit"
              fullWidth
              sx={{
                backgroundColor: '#26b43b',
                color: '#fff',
                minWidth: '300px',
                py: 2,
                '&:disabled': {
                  backgroundColor: '#ccc',
                },
              }}
            >
              Continue
            </Button>

            <Typography
              variant="body2"
              fontWeight="bold"
              textAlign="center"
              sx={{
                color: resendDisabled ? '#aaa' : '#26b43b',
                cursor: resendDisabled ? 'not-allowed' : 'pointer',
                pointerEvents: resendDisabled ? 'none' : 'auto',
              }}
              onClick={resendDisabled ? undefined : handleResendCode}
            >
              Resend code
            </Typography>
          </Stack>
        </Form>
      </FormikProvider>
    </Box>
  )
}
