import {
  <PERSON>,
  Button,
  Cir<PERSON>P<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
} from '@mui/material'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { CheckIcon } from '@dtbx/ui/components/SvgIcons'
import { Logo } from '@/components/SvgIcons/Logo'
import Image from 'next/image'
import { handleExpiredResetLink } from '@/store/actions/eattaAuth'

const validationSchema = Yup.object({
  email: Yup.string()
    .email('Please enter a valid email')
    .required('Email should not be empty'),
})

const maskEmail = (email: string) => {
  const [name, domain] = email.split('@')
  return name.length > 2
    ? `${name.slice(0, 2)}${'*'.repeat(name.length - 3)}${name.slice(-2)}@${domain}`
    : email
}

export const ExpiredLink = () => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { isLoadingLogin } = useAppSelector((store) => store.auth)
  const [isSuccess, setIsSuccess] = useState(false)

  const resetToken = useAppSelector((state) => state.passwordReset.resetToken)

  const formik = useFormik({
    initialValues: {
      email: '',
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      const handleSuccess = () => {
        setIsSuccess(true)
      }

      await handleExpiredResetLink(
        values.email,
        resetToken,
        dispatch,
        handleSuccess
      )

      setTimeout(() => {
        router.push('/auth')
      }, 5000)
    },
  })
  return (
    <Stack
      sx={{
        gap: '2rem',
        mb: '5rem',
      }}
    >
      {isSuccess ? (
        <Stack
          spacing={13}
          justifyContent="center"
          alignItems="center"
          sx={{ mb: 50 }}
        >
          <Logo />
          <Stack
            direction="column"
            alignItems="center"
            spacing={3}
            sx={{ width: '100%' }}
          >
            <CheckIcon />
            <Typography variant="h5" fontWeight="600" sx={{ fontSize: '30px' }}>
              Success!
            </Typography>
            <Typography
              variant="body2"
              fontWeight="500"
              textAlign="center"
              sx={{ color: '#000A12', fontSize: '16px' }}
            >
              If your email exists in our records, you will receive a link to
              reset your password.
            </Typography>
          </Stack>
        </Stack>
      ) : (
        <Stack
          spacing={3}
          sx={{
            width: '100%',
          }}
        >
          <Stack direction="column" alignItems="center" spacing={10} useFlexGap>
            <Logo />
            <Image
              src="/expiredLink.svg"
              alt="Picture of expired link"
              width={70}
              height={70}
            />
            <Stack direction="column" alignItems="center" useFlexGap>
              <Typography variant="h5" fontWeight="600" sx={{ mb: 2 }}>
                Password Reset Link Expired
              </Typography>
              <Typography
                variant="body2"
                fontWeight="400"
                textAlign="center"
                sx={{
                  maxWidth: 500,
                }}
              >
                The link you used is no longer active. Password reset links are
                only valid for 24 hours due to security reasons. Please enter
                your email so we can send you a link to reset your password.
              </Typography>
            </Stack>
          </Stack>

          <FormikProvider value={formik}>
            <Form onSubmit={formik.handleSubmit}>
              <Stack sx={{ width: '100%', height: '100%' }}>
                <Box>
                  <Typography
                    variant="body2"
                    component={'label'}
                    sx={{ color: '#344054', fontWeight: 500 }}
                  >
                    Email
                  </Typography>
                  <TextField
                    hiddenLabel
                    size="small"
                    type="text"
                    placeholder="<EMAIL>"
                    sx={{ marginBottom: '1rem' }}
                    {...formik.getFieldProps('email')}
                    fullWidth
                    error={Boolean(formik.touched.email && formik.errors.email)}
                    helperText={formik.touched.email && formik.errors.email}
                  />
                </Box>

                <Button
                  disabled={isLoadingLogin}
                  variant="contained"
                  type="submit"
                  sx={{
                    width: '100%',
                  }}
                  endIcon={
                    isLoadingLogin ? (
                      <CircularProgress size={20} thickness={3.0} />
                    ) : undefined
                  }
                >
                  Continue
                </Button>
              </Stack>
            </Form>
          </FormikProvider>
        </Stack>
      )}
    </Stack>
  )
}
