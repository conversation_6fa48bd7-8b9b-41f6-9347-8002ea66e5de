'use client'

import { Box } from '@mui/material'
import React, { ReactNode } from 'react'
import { AuthWrapper, LocalNotification } from '@dtbx/ui/components'
import { isLoggedIn } from '@dtbx/store/utils'
import { clearNotification } from '@dtbx/store/reducers'
import { useAppDispatch, useAppSelector } from '@/store'

export default function AuthLayout({ children }: { children: ReactNode }) {
  const dispatch = useAppDispatch()

  const { localNotification, localNotificationType } = useAppSelector(
    (state) => state.notifications
  )

  return (
    <AuthWrapper
      requiresAuth={false}
      isLoggedIn={isLoggedIn}
      loginUrl="/auth"
      homeUrl="/"
    >
      <Box
        sx={{
          display: 'flex',
          height: '100vh',
          width: '100%',
        }}
      >
        <LocalNotification
          clearNotification={() => dispatch(clearNotification())}
          notification={localNotification}
          notificationType={localNotificationType}
        />
        <Box
          sx={{
            width: '50%',
            background:
              'linear-gradient(0deg, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.40) 100%), url("/loginImage.jpeg"), lightgray',
            backgroundPosition: 'left',
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
            paddingLeft: '35px',
            paddingTop: '22px',
          }}
        />
        <Box
          sx={{
            padding: '2rem',
            width: '50%',
            height: '100vh',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {children}
        </Box>
      </Box>
    </AuthWrapper>
  )
}
