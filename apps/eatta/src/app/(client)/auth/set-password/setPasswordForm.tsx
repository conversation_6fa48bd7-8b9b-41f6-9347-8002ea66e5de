'use client'
import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  Box,
  Button,
  CircularProgress,
  InputAdornment,
  Link,
  Stack,
  TextField,
  Typography,
} from '@mui/material'

import { CustomCheckBox } from '@/components/CustomInputs'
import { Logo } from '@/components/SvgIcons/Logo'
import { CustomRadioButton } from '@/components/CustomRadioButton'
import { Form, FormikProvider, useFormik } from 'formik'
import React, { useState, useEffect } from 'react'
import * as Yup from 'yup'
import { VisibilityOffOutlined, VisibilityOutlined } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import { setNotification } from '@dtbx/store/reducers'
import {
  handleResetPassword,
  handleValidateToken,
} from '@/store/actions/eattaAuth'
import { setResetToken } from '@/store/reducers/passwordResetReducer'
import { useSearchParams } from 'next/navigation'

const validationSchema = Yup.object({
  password: Yup.string()
    .required('Password should not be empty')
    .min(8, 'Password should be at least 8 characters')
    .matches(
      /[!@#$%^&*(),.?":{}|<>]/,
      'Confirm password should contain one special character'
    ),
  confirm: Yup.string()
    .oneOf([Yup.ref('password'), ''], 'Passwords must match')
    .required('Confirm Password should not be empty'),
})

export const PasswordForm = () => {
  const router = useCustomRouter()
  const searchParams = useSearchParams()
  const token = searchParams.get('token')
  const dispatch = useAppDispatch()
  const [toggleTerms, setToggleTerms] = useState<boolean>(false)
  const [togglePass, setTogglePass] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const handleSuccess = () => {
    router.replace('/auth')
  }
  // Get token on component mount
  const resetToken = useAppSelector((state) => state.passwordReset.resetToken)
  const validateToken = async () => {
    const response = await handleValidateToken(
      token as string,
      dispatch,
      router
    )
    if (!response || !response.status) {
      router.replace('/auth')
      return
    }

    if (response.status === 'VALID') {
      return
    }
    if (response.status === 'USED') {
      router.replace('/auth')
    }
    if (response.status === 'EXPIRED') {
      router.replace('/auth/expired-link')
    }
  }

  useEffect(() => {
    if (token) {
      dispatch(setResetToken(token))
    } else {
      dispatch(
        setNotification({
          message:
            'Invalid or missing reset token. Please request a new password reset.',
          type: 'error',
        })
      )
      router.push('/auth/forgot-password')
    }

    validateToken()
  }, [dispatch, token])

  const formik = useFormik({
    initialValues: {
      password: '',
      confirm: '',
    },
    validateOnMount: true,
    validationSchema,
    onSubmit: async (values) => {
      try {
        if (!resetToken) {
          throw new Error('Reset token is required')
        }

        setIsLoading(true)
        await handleResetPassword(
          values.password,
          resetToken,
          dispatch,
          router,
          handleSuccess
        )
      } catch (error) {
        dispatch(
          setNotification({
            message:
              error instanceof Error ? error.message : 'Password reset failed',
            type: 'error',
          })
        )
      } finally {
        setIsLoading(false)
      }
    },
  })

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        maxWidth: '22.5rem',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '2rem',
      }}
    >
      <Stack direction="column" alignItems="center" spacing={5} useFlexGap>
        <Logo />

        <Stack direction="column" alignItems="center" spacing={1} useFlexGap>
          <Typography variant="h5" fontWeight="600">
            Nice to see you!
          </Typography>
          <Typography variant="body2" fontWeight="300" textAlign="center">
            Welcome to DTB commodities exchange. First things first, let's make
            sure your account is secure. Please create a new password below.
          </Typography>
        </Stack>
      </Stack>

      <FormikProvider value={formik}>
        <Form onSubmit={formik.handleSubmit}>
          <Stack spacing={3} useFlexGap>
            <Box>
              <Typography variant="body2" component="label">
                Enter New Password{' '}
                <span style={{ color: '#00BC2D', fontWeight: 'bolder' }}>
                  *
                </span>
              </Typography>

              <TextField
                hiddenLabel
                size="small"
                type={togglePass ? 'text' : 'password'}
                sx={{ marginBottom: '1rem' }}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment
                        sx={{ cursor: 'pointer' }}
                        position="end"
                        onClick={() => setTogglePass(!togglePass)}
                      >
                        {togglePass ? (
                          <VisibilityOffOutlined fontSize={'small'} />
                        ) : (
                          <VisibilityOutlined fontSize={'small'} />
                        )}
                      </InputAdornment>
                    ),
                  },
                }}
                {...formik.getFieldProps('password')}
                fullWidth
                error={Boolean(
                  formik.touched.password && formik.errors.password
                )}
                helperText={formik.touched.password && formik.errors.password}
              />

              <Typography variant="body2" component="label">
                Confirm Password{' '}
                <span style={{ color: '#00BC2D', fontWeight: 'bolder' }}>
                  *
                </span>
              </Typography>
              <TextField
                hiddenLabel
                size="small"
                type={togglePass ? 'text' : 'password'}
                margin={'none'}
                slotProps={{
                  input: {
                    endAdornment: (
                      <InputAdornment
                        sx={{ cursor: 'pointer' }}
                        position="end"
                        onClick={() => setTogglePass(!togglePass)}
                      >
                        {togglePass ? (
                          <VisibilityOffOutlined fontSize={'small'} />
                        ) : (
                          <VisibilityOutlined fontSize={'small'} />
                        )}
                      </InputAdornment>
                    ),
                  },
                }}
                {...formik.getFieldProps('confirm')}
                fullWidth
                error={Boolean(formik.touched.confirm && formik.errors.confirm)}
                helperText={formik.touched.confirm && formik.errors.confirm}
              />
            </Box>

            <Stack direction="column" useFlexGap spacing={2}>
              <Stack direction="row" useFlexGap spacing={1}>
                <CustomRadioButton
                  checked={formik.isValid}
                  customColor={'#26b43b'}
                  style={{
                    padding: '2px',
                  }}
                />
                <Typography variant="body2">
                  Must be at least 8 characters
                </Typography>
              </Stack>

              <Stack direction="row" useFlexGap spacing={1}>
                <CustomRadioButton
                  checked={formik.isValid}
                  customColor={'#26b43b'}
                  style={{
                    padding: '2px',
                  }}
                />
                <Typography variant="body2">
                  Must contain one special character
                </Typography>
              </Stack>

              <Stack
                direction="row"
                useFlexGap
                spacing={1}
                onClick={() => setToggleTerms(!toggleTerms)}
              >
                <CustomCheckBox
                  checked={toggleTerms}
                  customColor={'#26b43b'}
                  style={{
                    padding: '2px',
                  }}
                />
                <Typography variant="body2">
                  I consent that I've read the&nbsp;
                  <Link
                    href="/terms"
                    target="_blank"
                    rel="noopener noreferrer"
                    sx={{ textDecoration: 'none' }}
                  >
                    Terms & Conditions
                  </Link>
                  &nbsp;and have read the&nbsp;
                  <Link
                    href="https://dtbk.dtbafrica.com/privacy-policy"
                    target="_blank"
                    rel="noopener noreferrer"
                    sx={{ textDecoration: 'none' }}
                  >
                    Privacy Policy
                  </Link>
                </Typography>
              </Stack>
            </Stack>

            <Button
              disabled={!formik.isValid || !toggleTerms || isLoading}
              variant="contained"
              type="submit"
              fullWidth
            >
              {isLoading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Save Password'
              )}
            </Button>
          </Stack>
        </Form>
      </FormikProvider>
    </Box>
  )
}
