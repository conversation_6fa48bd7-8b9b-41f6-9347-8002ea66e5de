import { Dispatch } from '@reduxjs/toolkit'
import {
  PageFilters,
  AuctionSchedule,
  AuctionScheduleFilters,
} from '@/store/interfaces'
import { mapObjectToUrlParams } from '@/utils/objectUtil'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { apiclient } from '@/utils/apiclient'
import { setNotification } from '@dtbx/store/reducers'
import { setIsLoadingSchedules, setScheduleResponse } from '@/store/reducers'
import {
  setAuctionScheduleResponse,
  setIsLoadingAuctionSchedule,
} from '@/store/reducers'
import { AxiosRequestConfig } from 'axios'

export const getSalesSchedule = async (
  dispatch: Dispatch,
  params: PageFilters
) => {
  try {
    dispatch(setIsLoadingSchedules(true))
    const searchParams = mapObjectToUrlParams(params)
    const url = `/backoffice-bff/eatta-service/auction/schedule?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<AuctionSchedule>>(url)
    dispatch(setScheduleResponse(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingSchedules(false))
  }
}

export const uploadSalesSchedule = async (
  dispatch: Dispatch,
  file: File,
  year: string,
  startsOn: string,
  endsOn: string,
  config: AxiosRequestConfig
) => {
  try {
    dispatch(setIsLoadingSchedules(true))
    const url = `/backoffice-bff/eatta-service/auction/schedule?startsOn=${startsOn}&endsOn=${endsOn}`
    const res = await apiclient.postForm(
      url,
      {
        file: file,
      },
      config
    )

    dispatch(
      setNotification({
        message: res.data.message,
        type: 'success',
      })
    )
    const params = {
      page: 1,
      size: 10,
      year: parseInt(year),
    }
    await getSalesSchedule(dispatch, params)
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingSchedules(false))
  }
}

//auction schedule
export const getAuctionSchedule = async (
  dispatch: Dispatch,
  isBackOffice: boolean,
  filters: AuctionScheduleFilters
) => {
  try {
    dispatch(setIsLoadingAuctionSchedule(true))
    const searchParams = mapObjectToUrlParams({ ...filters, ascending: false })
    const url = isBackOffice
      ? `/backoffice-bff/eatta-service/auction/schedule?${searchParams}`
      : `/v1/eatta-service/auction/schedule?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<AuctionSchedule>>(url)
    dispatch(setAuctionScheduleResponse(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingAuctionSchedule(false))
  }
}
