import { Dispatch } from '@reduxjs/toolkit'
import {
  ApprovalRequestFilters,
  IApprovalRequest,
  MakerCheckerTypes,
  Types,
} from '../interfaces/makerChecker'
import { mapObjectToUrlParams } from '@/utils/objectUtil'
import { apiclient } from '@/utils/apiclient'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { setNotification } from '@dtbx/store/reducers'
import {
  setApprovalRequests,
  setIsLoadingApprovals,
} from '../reducers/approvalRequestsReducer'

export const getApprovalRequests = async (
  dispatch: Dispatch,
  filters: ApprovalRequestFilters
) => {
  try {
    dispatch(setIsLoadingApprovals(true))
    const searchParams = mapObjectToUrlParams({ ...filters })
    const url = `/backoffice-auth/maker-checker/approvals?${searchParams}`
    const res = await apiclient.get<PaginatedResponse<IApprovalRequest>>(url)
    dispatch(setApprovalRequests(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingApprovals(false))
  }
}

const BASE_URL = '/backoffice-bff/eatta-service'

type Status = 'PENDING' | 'APPROVED' | 'REJECTED'

const urlMap: Record<
  MakerCheckerTypes,
  (approvalId: string, authUserId?: string) => string
> = {
  [MakerCheckerTypes.AllocateInvoicePayment]: (id) =>
    `${BASE_URL}/invoice/payment/allocate/approve/${id}`,
  [MakerCheckerTypes.CreateCommission]: (id) =>
    `${BASE_URL}/configuration/commission/approve/${id}`,
  [MakerCheckerTypes.CreateOrganization]: (id) =>
    `${BASE_URL}/onboarding/register-organization/approve/${id}`,
  [MakerCheckerTypes.UpdateOrganization]: (id) =>
    `${BASE_URL}/onboarding/update-organization/approve/${id}`,
  [MakerCheckerTypes.CreateUser]: (id) =>
    `${BASE_URL}/onboarding/register-user/approve/${id}`,
  [MakerCheckerTypes.UpdateUser]: (id, authId) =>
    `${BASE_URL}/users/update-user/${authId}/approve/${id}`,
}

const isPostMethod = (type: MakerCheckerTypes) =>
  type === MakerCheckerTypes.AllocateInvoicePayment

export const approveRequest = async (
  dispatch: Dispatch,
  approvalId: string,
  authUserId: string,
  comments: string,
  status: Status,
  checkerId: string,
  type: Types,
  onSuccess: () => void
) => {
  try {
    const getUrl = urlMap[type.moduleType]
    if (!getUrl) throw new Error('Unsupported approval type')

    const url = getUrl(approvalId, authUserId)
    const payload = {
      comments,
      status,
      checkerId,
      ...(type.moduleType === MakerCheckerTypes.UpdateUser && { authUserId }),
    }

    if (isPostMethod(type.moduleType)) {
      await apiclient.post(url, payload)
    } else {
      await apiclient.put(url, payload)
    }

    dispatch(
      setNotification({
        message: 'Request approved successfully',
        type: 'success',
      })
    )
    onSuccess()
  } catch (e) {
    dispatch(
      setNotification({
        message: (e as Error).message,
        type: 'error',
      })
    )
  }
}
