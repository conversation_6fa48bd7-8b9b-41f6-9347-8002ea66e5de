/**
 * <AUTHOR> on 05/06/2025
 */
import { Dispatch } from '@reduxjs/toolkit'
import { mapObjectToUrlParams, removeFalsyValues } from '@/utils/objectUtil'
import { apiclient } from '@/utils/apiclient'
import { ApiResponse, PaginatedResponse } from '@dtbx/store/interfaces'
import { setNotification } from '@dtbx/store/reducers'
import {
  AllocatePaymentRequest,
  Payment,
  PaymentFilters,
  Transaction,
  TransactionFilters,
} from '@/store/interfaces/transactions'
import {
  setIsAllocatingPayment,
  setIsLoadingTransactions,
  setIsRetryingTransactions,
  setPaymentsResponse,
  setTransactionApprovalsRequests,
  setTransactionResponse,
} from '@/store/reducers'
import {
  ApprovalRequestFilters,
  IApprovalRequest,
} from '@/store/interfaces/makerChecker'

export const getPayments = async (
  dispatch: Dispatch,
  params: PaymentFilters
) => {
  try {
    dispatch(setIsLoadingTransactions(true))

    const searchParams = mapObjectToUrlParams({ ...params, ascending: false })

    const res = await apiclient.get<PaginatedResponse<Payment>>(
      `/backoffice-bff/eatta-service/invoice/payment?${searchParams}`
    )
    dispatch(setPaymentsResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingTransactions(false))
  }
}

export const getPaymentsApprovalRequests = async (
  dispatch: Dispatch,
  filters: ApprovalRequestFilters
) => {
  try {
    dispatch(setIsLoadingTransactions(true))

    const searchParams = mapObjectToUrlParams({ ...filters })

    const url = `/backoffice-auth/maker-checker/approvals?${searchParams}`

    const res = await apiclient.get<PaginatedResponse<IApprovalRequest>>(url)

    dispatch(setTransactionApprovalsRequests(res.data))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingTransactions(false))
  }
}

export const allocatePayment = async (
  isSuper: boolean,
  dispatch: Dispatch,
  data: AllocatePaymentRequest,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsAllocatingPayment(true))

    const url = `/backoffice-bff/eatta-service/invoice/payment/allocate/${isSuper ? '' : 'make'}`
    await apiclient.post(url, removeFalsyValues(data))
    dispatch(
      setNotification({
        message: `Transaction allocation saved successfully ${!isSuper ? 'and is pending approval' : ''}`,
        type: 'success',
      })
    )
    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message

    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsAllocatingPayment(false))
  }
}

export const approvePaymentRequest = async (
  dispatch: Dispatch,
  approvalId: string,
  comments: string,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsAllocatingPayment(true))

    const url = `/backoffice-bff/eatta-service/invoice/payment/allocate/approve/${approvalId}`
    await apiclient.post(url, { comments })
    dispatch(
      setNotification({
        message: 'Transaction allocation approved successfully',
        type: 'success',
      })
    )
    onSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsAllocatingPayment(false))
  }
}

export const getTransactions = async (
  dispatch: Dispatch,
  params: TransactionFilters
) => {
  try {
    dispatch(setIsLoadingTransactions(true))
    const organizationType = params.organizationType
      ? params.organizationType.toUpperCase()
      : ''
    const searchParams = mapObjectToUrlParams({
      ...params,
      organizationType,
      ascending: false,
    })

    const res = await apiclient.get<PaginatedResponse<Transaction>>(
      `/backoffice-bff/eatta-service/transaction?${searchParams}`
    )
    dispatch(setTransactionResponse(res.data))
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsLoadingTransactions(false))
  }
}

export const retryTransaction = async (
  dispatch: Dispatch,
  transactionId: string,
  onSuccess: () => void
) => {
  try {
    dispatch(setIsRetryingTransactions(true))
    const res = await apiclient.post<ApiResponse<Transaction>>(
      `/backoffice-bff/eatta-service/transaction/${transactionId}/retry`
    )
    const success = res.data.status !== 'FAILED'
    dispatch(
      setNotification({
        message: res.data.message,
        type: success ? 'success' : 'error',
      })
    )
    success && onSuccess()
  } catch (e: any) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
  } finally {
    dispatch(setIsRetryingTransactions(false))
  }
}
