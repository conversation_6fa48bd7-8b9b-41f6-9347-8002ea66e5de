import {
  IApprovalRequest,
  IMakerCheckerType,
  SelectedApprovalRequests,
} from './../interfaces/makerChecker'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

import { PaginatedResponse } from '@dtbx/store/interfaces'
import { BrokerCommissionResponse, BrokerCommissionStatements } from '@/store/interfaces/brokerCommissions'

export interface CommissionState {
  isLoadingCommissions: boolean
  isActivatingCommission: boolean
  commissionResponse: PaginatedResponse<BrokerCommissionResponse>
  selectedPayment: BrokerCommissionResponse | null
  brokerCommissionStatementsResponse: PaginatedResponse<BrokerCommissionStatements>
  isLoadingBrokerCommissionStatements: boolean
  //maker-checker
  pendingRequestsResponse: PaginatedResponse<IApprovalRequest>
  requestTypeResponse: IMakerCheckerType[]
  selectedApprovalRequests: SelectedApprovalRequests | null
}

const initialPaginatedState = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}

const initialState: CommissionState = {
  isLoadingCommissions: false,
  isActivatingCommission: false,
  commissionResponse: initialPaginatedState,
  selectedPayment: null,
  brokerCommissionStatementsResponse: initialPaginatedState,
  isLoadingBrokerCommissionStatements: false,
  //maker-checker
  pendingRequestsResponse: initialPaginatedState,
  requestTypeResponse: [],
  selectedApprovalRequests: null,
}
const brokerCommissionsSlice = createSlice({
  name: 'brokercommissions',
  initialState,
  reducers: {
    setCommissionsResponse: (
      state,
      action: PayloadAction<PaginatedResponse<BrokerCommissionResponse>>
    ) => {
      state.commissionResponse = action.payload
    },
    setIsLoadingCommissions: (state, action: PayloadAction<boolean>) => {
      state.isLoadingCommissions = action.payload
    },
    setIsActivatingCommissions: (state, action: PayloadAction<boolean>) => {
      state.isActivatingCommission = action.payload
    },
    setBrokerCommissionStatementsResponse: (
      state,
      action: PayloadAction<PaginatedResponse<BrokerCommissionStatements>>
    ) => {
      state.brokerCommissionStatementsResponse = action.payload
    },
    setIsLoadingBrokerCommissionStatements: (state, action: PayloadAction<boolean>) => {
      state.isLoadingBrokerCommissionStatements = action.payload
    },
    //maker-checker
    setPendingRequestsResponse: (
      state,
      action: PayloadAction<PaginatedResponse<IApprovalRequest>>
    ) => {
      state.pendingRequestsResponse = action.payload
    },
    setRequestTypeResponse: (
      state,
      action: PayloadAction<IMakerCheckerType[]>
    ) => {
      state.requestTypeResponse = action.payload
    },
    setSelectedApprovalRequests: (
      state,
      action: PayloadAction<SelectedApprovalRequests | null>
    ) => {
      state.selectedApprovalRequests = action.payload
    },
    clearSelectedCommissions: (state) => {
      state.selectedPayment = null
    },
  },
})

export const {
  setCommissionsResponse,
  setIsLoadingCommissions,
  setIsActivatingCommissions,
  setBrokerCommissionStatementsResponse,
  setIsLoadingBrokerCommissionStatements,
  //maker-checker
  setPendingRequestsResponse,
  setRequestTypeResponse,
  setSelectedApprovalRequests,
  clearSelectedCommissions,
} = brokerCommissionsSlice.actions
export default brokerCommissionsSlice.reducer
