/**
 * <AUTHOR> on 05/06/2025
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { Payment, Transaction } from '@/store/interfaces/transactions'
import { IApprovalRequest } from '@/store/interfaces/makerChecker'
import { number } from 'yup'
import { InvoiceEslip } from '@/store/interfaces'

export interface TransactionState {
  approvalRequestResponse: PaginatedResponse<IApprovalRequest>
  isLoading: boolean
  payments: PaginatedResponse<Payment>
  selectedPayment: Payment | null
  isAllocatingPayment: boolean
  allocateEslip: InvoiceEslip | null
  approvalRequest: IApprovalRequest | null
  isRetrying: boolean
  transactions: PaginatedResponse<Transaction>
  selectedTransaction: Transaction | null
}

const initialPaginatedState = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}

const initialState: TransactionState = {
  isLoading: false,
  payments: initialPaginatedState,
  selectedPayment: null,
  approvalRequestResponse: initialPaginatedState,
  isAllocatingPayment: false,
  allocateEslip: null,
  approvalRequest: null,
  isRetrying: false,
  transactions: initialPaginatedState,
  selectedTransaction: null,
}

const transactionsSlice = createSlice({
  name: 'transactions',
  initialState,
  reducers: {
    setIsLoadingTransactions: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setPaymentsResponse: (
      state,
      action: PayloadAction<PaginatedResponse<Payment>>
    ) => {
      state.payments = action.payload
    },
    setSelectedPayments: (state, action: PayloadAction<Payment>) => {
      state.allocateEslip = null
      state.selectedPayment = action.payload
    },
    setAllocateEslip: (state, action: PayloadAction<InvoiceEslip>) => {
      state.allocateEslip = action.payload
    },
    setTransactionApprovalsRequests: (
      state,
      action: PayloadAction<PaginatedResponse<IApprovalRequest>>
    ) => {
      state.approvalRequestResponse = action.payload
    },
    setSelectedTransactionApprovalsRequests: (
      state,
      action: PayloadAction<IApprovalRequest>
    ) => {
      state.approvalRequest = action.payload
    },
    setIsAllocatingPayment: (state, action: PayloadAction<boolean>) => {
      state.isAllocatingPayment = action.payload
    },
    setIsRetryingTransactions: (state, action: PayloadAction<boolean>) => {
      state.isRetrying = action.payload
    },
    setTransactionResponse: (
      state,
      action: PayloadAction<PaginatedResponse<Transaction>>
    ) => {
      state.transactions = action.payload
    },
    setSelectedTransaction: (state, action: PayloadAction<Transaction>) => {
      state.allocateEslip = null
      state.selectedTransaction = action.payload
    },
    resetTransactions: (state) => {
      state.transactions = initialPaginatedState
      state.payments = initialPaginatedState
    },
    resetAllocatePayment: (state) => {
      state.allocateEslip = null
      state.approvalRequest = null
      state.selectedPayment = null
    },
    resetState: (state) => initialState,
  },
})
export const {
  setIsLoadingTransactions,
  setPaymentsResponse,
  setTransactionApprovalsRequests,
  setSelectedTransactionApprovalsRequests,
  setIsAllocatingPayment,
  setSelectedPayments,
  setAllocateEslip,
  setIsRetryingTransactions,
  setTransactionResponse,
  setSelectedTransaction,
  resetTransactions,
  resetAllocatePayment,
} = transactionsSlice.actions
export default transactionsSlice.reducer
