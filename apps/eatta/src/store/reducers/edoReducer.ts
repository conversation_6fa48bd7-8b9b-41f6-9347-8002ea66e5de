import { PaginatedResponse } from '@dtbx/store/interfaces'
import { AuctionEntry } from '../interfaces/catalogues'
import { DeliveryOrderResponse } from '../interfaces/Edo'
import { IApprovalRequest } from '../interfaces/makerChecker'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export interface EdoState {
  //Delivery Orders
  deliveryOrdersResponse: PaginatedResponse<DeliveryOrderResponse>
  selectedDeliveryOrderEntries: DeliveryOrderResponse | null
  selectedAuctionEntry: AuctionEntry | null
  isSigningDeliveryOrder: boolean
  isSubmittingDeliveryOrder: boolean
  selectedDeliveryOrderApprovalsRequests: IApprovalRequest | null
  isLoading: boolean
  isDownloadingTrdEdo: boolean
}

const initialPaginatedState: PaginatedResponse<DeliveryOrderResponse> = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}

const initialState: EdoState = {
  //Delivery Orders
  deliveryOrdersResponse: initialPaginatedState,
  selectedDeliveryOrderEntries: null,
  selectedAuctionEntry: null,
  isSigningDeliveryOrder: false,
  isSubmittingDeliveryOrder: false,
  selectedDeliveryOrderApprovalsRequests: null,
  isLoading: false,
  isDownloadingTrdEdo: false,
}

const edoSlice = createSlice({
  name: 'edo',
  initialState: initialState,
  reducers: {
    //Delivery Orders
    setIsLoadingEdo: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload
    },
    setIsDownloadingTrdEdo: (state, action: PayloadAction<boolean>) => {
      state.isDownloadingTrdEdo = action.payload
    },
    setDeliveryOrdersResponse: (
      state,
      action: PayloadAction<PaginatedResponse<DeliveryOrderResponse>>
    ) => {
      state.deliveryOrdersResponse = action.payload
    },
    setSelectedDeliveryOrderEntries: (
      state,
      action: PayloadAction<DeliveryOrderResponse>
    ) => {
      state.selectedDeliveryOrderEntries = action.payload
    },
    setSelectedAuctionEntry: (
      state,
      action: PayloadAction<AuctionEntry | null>
    ) => {
      state.selectedAuctionEntry = action.payload
    },
    setIsSigningDeliveryOrder: (state, action: PayloadAction<boolean>) => {
      state.isSigningDeliveryOrder = action.payload
    },
    setIsSubmittingDeliveryOrder: (state, action: PayloadAction<boolean>) => {
      state.isSubmittingDeliveryOrder = action.payload
    },
    setSelectedDeliveryOrderApprovalsRequests: (
      state,
      action: PayloadAction<IApprovalRequest>
    ) => {
      state.selectedDeliveryOrderApprovalsRequests = action.payload
    },
    resetDeliveryOrders: (state) => {
      state.deliveryOrdersResponse = initialPaginatedState
    },
    resetEdoStore: () => initialState,
  },
})

export const {
  //Delivery Orders
  setIsLoadingEdo,
  setIsDownloadingTrdEdo,
  setDeliveryOrdersResponse,
  setSelectedDeliveryOrderEntries,
  setSelectedAuctionEntry,
  setIsSigningDeliveryOrder,
  setIsSubmittingDeliveryOrder,
  setSelectedDeliveryOrderApprovalsRequests,
  resetDeliveryOrders,
  resetEdoStore,
} = edoSlice.actions

export default edoSlice.reducer
