/**
 * <AUTHOR> on 01/07/2025
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { IApprovalRequest } from '../interfaces/makerChecker'
import { PaginatedResponse } from '@dtbx/store/interfaces'

export interface ApprovalState {
  isLoadingApprovals: boolean
  approvalRequests: PaginatedResponse<IApprovalRequest>
}
const initialPaginatedState = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}
const initialState: ApprovalState = {
  isLoadingApprovals: false,
  approvalRequests: initialPaginatedState,
}

const approvalsSlice = createSlice({
  name: 'approvals',
  initialState,
  reducers: {
    setIsLoadingApprovals: (state, action: PayloadAction<boolean>) => {
      state.isLoadingApprovals = action.payload
    },
    setApprovalRequests: (
      state,
      action: PayloadAction<PaginatedResponse<IApprovalRequest>>
    ) => {
      state.approvalRequests = action.payload
    },
    resetApprovalRequest: (state) => {
      state.approvalRequests = initialPaginatedState
    },
  },
})
export const {
  setIsLoadingApprovals,
  setApprovalRequests,
  resetApprovalRequest,
} = approvalsSlice.actions
export default approvalsSlice.reducer
