import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { AuctionGroup, AuctionResponse, InvoiceEntry, CatalogueFilters } from '../interfaces'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { getAuctionWeek } from '@dtbx/store/utils'

const initialPaginatedState = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}

const initialFilters: CatalogueFilters = {
  producer: '',
  amount: 0,
  lotNo: '',
  broker: '',
  buyerName: '',
  invoiceNo: '',
  factory: '',
  saleDate: getAuctionWeek().toString() || '',
  page: 1,
  size: 30,
  year: new Date().getFullYear() || 0,
}

export interface CatalogueState {
  isLoadingCatalogues: boolean
  isUploadingCatalogues: boolean
  isExportingCatalogues: boolean
  //Pre Auction catalogue
  preAuctionCataloguesResponse: AuctionResponse
  //Post Auction catalogue
  postAuctionCataloguesResponse: AuctionResponse
  //Filters
  postAuctionFilters: CatalogueFilters
  preAuctionFilters: CatalogueFilters
  //Invoice
  invoiceData: InvoiceEntry | null
  isLoadingTRDInfo: boolean
}

const initialState: CatalogueState = {
  isLoadingCatalogues: false,
  isUploadingCatalogues: false,
  isExportingCatalogues: false,
  //Pre Auction catalogue
  preAuctionCataloguesResponse: initialPaginatedState,
  postAuctionCataloguesResponse: initialPaginatedState,
  //Filters
  postAuctionFilters: initialFilters,
  preAuctionFilters: initialFilters,
  //Invoice
  invoiceData: null,
  isLoadingTRDInfo: false,
}

const cataloguesSlice = createSlice({
  name: 'catalogues`',
  initialState,
  reducers: {
    setIsLoadingCatalogues: (state, action: PayloadAction<boolean>) => {
      state.isLoadingCatalogues = action.payload
    },
    setIsUploadingCatalogues: (state, action: PayloadAction<boolean>) => {
      state.isUploadingCatalogues = action.payload
    },
    setIsExportingCatalogues: (state, action: PayloadAction<boolean>) => {
      state.isExportingCatalogues = action.payload
    },
    //Pre Auction catalogue
    setPreAuctionCataloguesResponse: (
      state,
      action: PayloadAction<PaginatedResponse<AuctionGroup>>
    ) => {
      if (action.payload.page > 1) {
        const existingData = state.preAuctionCataloguesResponse.data
        const newData = action.payload.data
        const mergedData = [...existingData, ...newData]
        state.preAuctionCataloguesResponse = {
          ...action.payload,
          data: mergedData,
        }
      } else {
        state.preAuctionCataloguesResponse = action.payload
      }
    },
    //Post Auction Catalogue
    setPostAuctionCataloguesResponse: (
      state,
      action: PayloadAction<AuctionResponse>
    ) => {
      if (action.payload.page === 1) {
        state.postAuctionCataloguesResponse = action.payload
        return
      }

      const existingData = state.postAuctionCataloguesResponse.data
      const newData = action.payload.data
      const mergedData = [...existingData, ...newData]
      state.postAuctionCataloguesResponse = {
        ...action.payload,
        data: mergedData,
      }
    },
    //Filters
    setPostAuctionFilters: (state, action: PayloadAction<CatalogueFilters>) => {
      state.postAuctionFilters = action.payload
    },
    setPreAuctionFilters: (state, action: PayloadAction<CatalogueFilters>) => {
      state.preAuctionFilters = action.payload
    },
    //Post Auction Catalogue
    setInvoiceData: (state, action: PayloadAction<InvoiceEntry | null>) => {
      state.invoiceData = action.payload
    },

    setIsLoadingTRDInfo: (state, action: PayloadAction<boolean>) => {
      state.isLoadingTRDInfo = action.payload
    },

    resetCataloguesStore: () => initialState,
  },
})

export const {
  //Catalogue list
  setIsLoadingCatalogues,
  setIsUploadingCatalogues,
  setIsExportingCatalogues,
  //Pre Auction catalogue
  setPreAuctionCataloguesResponse,
  //Post Auction catalogue
  setPostAuctionCataloguesResponse,
  //Filters
  setPostAuctionFilters,
  setPreAuctionFilters,
  //Checkout invoices
  setInvoiceData,
  setIsLoadingTRDInfo,
  //Reset
  resetCataloguesStore,
} = cataloguesSlice.actions
export default cataloguesSlice.reducer
