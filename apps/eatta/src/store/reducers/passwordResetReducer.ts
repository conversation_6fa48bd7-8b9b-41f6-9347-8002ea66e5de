import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export interface PasswordResetState {
  resetToken: string
}
const initialState: PasswordResetState = {
  resetToken: '',
}
const passwordResetSlice = createSlice({
  name: 'passwordReset',
  initialState,
  reducers: {
    setResetToken: (state, action: PayloadAction<string>) => {
      state.resetToken = action.payload
    },
  },
})

export const { setResetToken } = passwordResetSlice.actions
export default passwordResetSlice.reducer
