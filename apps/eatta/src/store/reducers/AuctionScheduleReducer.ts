import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { AuctionSchedule } from '@/store/interfaces'

export interface ScheduleState {
  isLoadingSchedules: boolean
  scheduleResponse: PaginatedResponse<AuctionSchedule>
  selectedSchedule: AuctionSchedule | null
  //Auction Schedule
  auctionScheduleResponse: PaginatedResponse<AuctionSchedule>
  isLoadingAuctionSchedule: boolean
}

const initialPaginatedState = {
  data: [],
  size: 0,
  page: 0,
  totalNumberOfPages: 0,
  totalElements: 0,
}

const initialState: ScheduleState = {
  isLoadingSchedules: false,
  scheduleResponse: initialPaginatedState,
  selectedSchedule: null,
  //Auction Schedule
  auctionScheduleResponse: initialPaginatedState,
  isLoadingAuctionSchedule: false,
}
const scheduleSalesSlice = createSlice({
  name: 'salesSchedule',
  initialState,
  reducers: {
    setScheduleResponse: (
      state,
      action: PayloadAction<PaginatedResponse<AuctionSchedule>>
    ) => {
      state.scheduleResponse = action.payload
    },
    setIsLoadingSchedules: (state, action: PayloadAction<boolean>) => {
      state.isLoadingSchedules = action.payload
    },
    //Auction Schedule
    setAuctionScheduleResponse: (
      state,
      action: PayloadAction<PaginatedResponse<AuctionSchedule>>
    ) => {
      state.auctionScheduleResponse = action.payload
    },
    setIsLoadingAuctionSchedule: (state, action: PayloadAction<boolean>) => {
      state.isLoadingAuctionSchedule = action.payload
    },
    clearSelectedSchedule: (state) => {
      state.selectedSchedule = null
    },
  },
})

export const {
  setScheduleResponse,
  setIsLoadingSchedules,
  clearSelectedSchedule,
  //Auction Schedule
  setAuctionScheduleResponse,
  setIsLoadingAuctionSchedule,
} = scheduleSalesSlice.actions
export default scheduleSalesSlice.reducer
