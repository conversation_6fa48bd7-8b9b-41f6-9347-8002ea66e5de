/**
 * <AUTHOR> on 14/05/2025
 */

import { encryptAndSignFile } from '@/utils/fileEncryption'
import { describe, expect, it, vi } from 'vitest'

const passphrase = 'Pn6ZKM5zJ0FJPP8ZgZ8e'
const pgpKeyPair = {
  privateKeyArmored:
    'LS0tLS1CRUdJTiBQR1AgUFJJVkFURSBLRVkgQkxPQ0stLS0tLQoKeGNhR0JHZ2wwd3NCRUFDNmZFZVVLYURoMjkxL21CNG9STG5jT2pHbWRmT1NBdklKMjdYN0R1SExNQzNkCmdjKzdsOGdvU05FNndqTEVWVzVPWkdDQkNseE8wZEc2d2JhdFEyVnk3SFBWR1lndWxnQzA4NEZyQmMxOQpOaDlwRmhUdHU0cDRNVEpETzJOOVoxVzhxSzVZWlNmalNtNDB3TDlpQjI3TnBzUHhENVVkdHFabGVSSysKNmx2dFhMLzRnY3J5UVZWYmtEWklSeFBqSGhTeE0wdEdJN3RwanZSNTMvRXE1QVpwMUxvQU54RFQyb2JmCmp1c0ZQTFhVb29OdFQ0YXRTWjZvZmphSmwvaXNRd1ZMdW5aUkhRR2F2UytmYklCSEhCQ091VEVNMXRSbApvT1pTZ21FK1daeWJ6eDJaKzBhNS8zNkZWN3BJeXM4U2FJYmVRUVRUdGp2ZDhQZndJcDFBZ01FeXFlQzEKRTJrNThVWnJKSExxbDlocXJWOURwR3YwQzI4RFdvRExtaVN2Y3ZKSU1zZkcyQW5jbkl1bzI4azdrUDduCjkvMTdkY0hiSU9RY1FUUm80TjFUTmt3dGJBZVIzd3M1Z2JDTlk5ZzRwZHBWRm1kTTYrZnQ5cWRBUmxlZAo2QWtLcGp4YkY1V0VhWlJobjd5ZHAwSHFhRUxYWjFxWXpBOE9vd3BOVUVZSUVQTzdmT2JYTkJqU0lmc0sKQ2NyR3VBK0dITGgzaTFMd0RyV0ZsMmp2UXc2V1VXam0ydkNEZmJ6Mmp3MU5BcWdOaVBCeUQzY0FWTTBQCmNXNXlPKzNDQ1NtRGd3a0wxVGlRck9hNVVLbjFBU3hmeXRlVEhFSFU5OGJNQjBQM3NHeGI3YmV2dnhwSwpab0hYWFkzYnJCb0prbW9MRTFJVm5BdXVoTU8wbEt4WkFraHZ4d0FSQVFBQi9na0RDQmtJc2dIREdvZVAKNE92RCtUUXYxTXN5L05lWEpEUHVLdHVHWUpveVR1K2l3VUo4cysydFZ3bjNNMS9RYUtDaFBFSG14a1l3CjB2b3F4cEJIR3FkQS9qcHNsNy9CZlYyaXVKMDQwV2xXaEp0WlB5eDlLcTBoNTloZERWb01NeXlpYzM1cApTMkFYKy8yYVdZZVUvMDQ1TU4yTVVFS0NKdjdNT2VSTFdkQ3dVOHBrbHFUakN0UC84OFlRUm44Nk5ORysKQzNtQ0VZMmRMSHdzOTNkcVJpRVM1SmV4b1UyeUhaTXBHVFAxNmpySXpaZ01zNVpCRlJHM3VMcjR3N0lVCnYrWHFKb3h5K2FtMXJkSlY2YzBBT05lS0xxVVYzeUg3WFh4MDZGdmpoNGRCWndrcEdRVGtyeGw1T3E2RgovUHM3Q2FTbVJSbHY1d0ZibVlDNHJTVmFONCtKdGpaNVpYUldPVDBhVWF4RVdkZ1g5eXUrVjNXcTQ3UlQKRHdzbjdnM2pEL1U1dGxYWE1FZ2grYXlXOHMwZ0xHM2FDS21UaS9pN2xWdityYll4QnFvdEdNV1FaNVRsCjVsOEtMMHNvcmlLcTZyaEpKem54dmt4aU42YXRBUDFFUjUrbDRIVjJmRlpoQlhweU0wRVRQVzFZVC9aSQpjZHBQeTJ3K1ZNOEowdlVjWUpzUEszd0ppVm5qaUNPSHRIMG5obUlRUTA2NFp4Qmw4MmhnV2N5aUk4eTkKN2Evb2N5MkpyQXNBWGJHUDBGM2tBT2t2YnNEVzFXM29XYjFieStiMXFLTTRyVndmSkU5ZFV0YXcxblhpCitvQjVvZTRTY2lzUjh1QWFiMmRnT2FmYXZOTWJ4dnF5OE41ZklRWEVpRG91Y0p1QnFpOEJLYUpxLzVpSAp5OW9nUVdERUphN2cxSjViRlMvZVpvc29XaWJhTjhZc09xT0xUWVhMYTVuU3NWSHg1Q0dxMEpQaVdWNUwKb0JsbTJFMFMzeENjTkZmNFh0YU9LcEtpVkIrcktKTWNSVzJIS01iQW9TNVJKbFlsRHNQQnpSY1JKN3VyCkxIcnZkV0x0ZzhkN1pmRGdpWkI0U2o4VlpJbUxIRUYwR0hQb0gzNkxteFk5dytXcFF3bWxINSs5ZnZhQgpkZCtrajNhRkszMnBYUzdTZzVEZWdzdWNBRWpYVEdKZFRHN1YvYnlGNXh2RncrOUpXSkplOEZyV0loSHIKMi9GQ0wvVXlLQ1pZWHlQZmdqTE54QjRuL3ozRHVUM0Fmdm4xdEowTnNYc1hzTWVPblFUSXpzdnlJakRmClJGSHUrcTErNmZsU2x3UjFZa2FZVEZSTnpUd3BneWlXWEtNMDh5SWYyaWZpU3F0QU4rTWVhalFoSHYzUgpsVzEzK3k4c2hXejREKzNQTU9DREVKK2g3RkFBekw3SS9tOUh1bG5vOEczQ2FKYnpET1VGeVM0MHYwbmcKaTRvTVdZb0laR0lyZXpodDhKYTByS2FiUllZb0t0YjZOa3ltUTNRRFIyZUFaaWZBUDg0dE9Ka0dTMnBHClRvQ1hCWjRXWlhLWVgvR3JDWTdvU1pkK0sxTE5iTE5yeVpFSVVGcXkzemxMY1U3Skp3d0JWSTBXRlQrYQpxcDNUQkJGR1lqQTVVanRqUlBZejJENUlXc0k1MXphUDJLUTF0d2xkc2t3Y0NTd3BaTms2MjVBVVRuTXQKaDMrd3l6VlhSOHlyTXR4bXArc25vRmVqM3FabEw5dXRUbVFacEJ5RkRHYWtuUExNWGVkOHpwbzJIeTNUClV0UGJWTTU4bGNvWm0yOGlQQndZTjhzMVZuUnpoV3NaeXZycmdEZVNGYVdXODNpa3hTUlF6Z1orbWJrMwpqdkJCMGtOckc4K1JsUnhMRGxhbDRhWVBRZmFxQnpTc1cwUk1MSmUvelE0bmtGcHAxUTRFYUxSVERpaE4KN0VZb1dFUC9MTVFMTDQ0ZFFUOE5PZlJaNTIxNXY0emJuZG9lNjB2V2huVktNRWRUbGlsWGppbWtyRjVUCnN6dVh2bGhMblhDd0Ewa1dUVmlBci8wU3NHV2tXRjJ5bFRuRHc1RmZ3emh1amNRdkgvUDdTWVBTbGVSOApNQ3FmeitLMkRwdnp2QzRDWXE3NVdYL2xqS0VpNDRJTXRXNGlRMTAxL2J6ZkJRVlhSNk9heFVhdGo4WnAKTnVZWTRMTXVlQ3JtQytka1JrZ1dRRW1VRHBkVEdaNjV6MXZpcDFHeTFmaGRUY0FJMGpHQmF2QVdUMEs0CkQzdFcra2ZFNGxtWitEZCtxbUhLa0lWMks0M3JrZy95blpYVjllUDFlQm1ic0NWTVgxeGVINjZsSnN5ZwovY1lsd1A3RmhhTENHOGJYbVd1aGczQTU1YnZORzBwdmJpQlRiV2wwYUNBOGFtOXVRR1Y0WVcxd2JHVXUKWTI5dFBzTEIwUVFUQVFvQWhRV0NhQ1hUQ3dNTENRY0prQ0FQeXY5cHFKeG5SUlFBQUFBQUFCd0FJSE5oCmJIUkFibTkwWVhScGIyNXpMbTl3Wlc1d1ozQnFjeTV2Y21jbVB1ZFRDOWdKWDg4RVA4ekdEcHhrbmdURgpUQ0J5aWQ0dzdIVTc4TERpS3dVVkNnZ09EQVFXQUFJQkFoa0JBcHNEQWg0QkZpRUUzejBORFpuZU1xQ3YKeEhCK0lBL0svMm1vbkdjQUFFRG1EL3dPVnRqVGZJOHFkc21HVlltZGJoUGkweFdjUlFhSkUyckE5aTh0Ci9Gdi9zaW5kMGhMdGJ4Nm5HeE9QdEkxL20rcnYxaTgreDFXTWp0TDAzblMrd3NZYklob05mUjRDRWZKcwplNXhNSEgrQS9ud0hPV25qU3NmdmFDOUFRdE9LdFJMdnR2MVpva1VNb0NTUUxIS1k0WUw2cHNLdnFQTGMKUFZVaTVHTTZTNnZzd0RpN2ZROHhYNGlUWXZXMzl5TDRRYU5jalNvM3NjcHNoZUVzb1dzN1dkMVhja0VXCjNpcDlBWkE1aXRGMkIrdHNSc2lHYmkvdzg1YU5teVJKeFVFQ2RjcVRPaUkxY3c4NjhESUYrcEx2bW00ZApvS055MXk2U3Y5bTgzWnlVSStOM2Q3YjRJK0c0b0JFa2xvN0V4WXZxYlZLaDcwQlY1d1h6bG5abzlmN24KQnMxeVNEL296NWZRTTlYS1FQcm4rNzhhUUwxa3pQWnlPUFFKakVXeXVSNUtzRlhNcmhhRk5oMzd6bEN3CmRnb1laRHBXb1dicDRCcFFBN1ZBclN5ZDFweDU5Y0NpN2x1VDZtc1FKdWQ2K1BsWTdXMWNUcklYVHByWgpLOVVSL1QwMHdvOW9BYVVCWFd4YktSaEt3c2hGU3ZwS0grdFYyZThLWGlsOVl4U2c0NUpGVWNzWHZXay8KSWx5RTgrRlljNTVSV29FM2YzNjdrMjBTeTlQSDZrYlRicHJsNDFxS0w2L2NUZTdtanVDRy9QSit0K3kwCnNPcE5KbFZKeU0rRGFXWHZrMGJLZnNMZjlzZm5hdkF2QmVPTnhzZmdqSDJ1NDJZOXN5enV2SUhMUkh3dApMcmtSRjY2VlluNldPZC9mcHEwSHBTOHZsMUpBNXBWTWJXdlNQZGRvZXdwYzBjZkdoZ1JvSmRNTEFSQUEKa2QrdXRqRlBHSExGZDhLa0x4V1NFVkdMcmJ1VUZvSDBoRjJkUVBlUUZyVmhwL3k4VDUxSXBNOTlNdFlPCmtDZ1BCZFUrQUpDSEc0b25GcE0vODV6TlU2L3NrU2FUeThDaWlodzAwMHlFVGpzTE92aFhkbDBCSUMvQgpkSlpvYUV1NzJCM1JzN2F0V2JnRFF1dThGNm92K1ppU3ZpM2d0Vndaa0VzbGw4UXZINDdIWmdZK2NtZkkKeWt5OWNiZ20wcm14S3Rqeis2c2w1cGFtaUtESWkvV2NNcCt4cndOR3lBbWxlLzV6SjNZTHc3WFB5TlU5Cmx3SjlRS1RiKzZQR1l5bXBmbVpGdFlBTmNQQmhOZnlxQVlSaDE5K1ZONE1SMjlBYmRjdVIzaDROQTgwegp0UmR0RHJxVFdIWUgxNTlycWlleWNOdUY2V0ovY0wyaEJtaEl3bTdUbW0yeHliWUZSQ3lQU3BJWm5mMW8KR3VHK01xblNxQWUyNldJQnFyM1VqdUVaMzEwOXMzR1NiK0pDS1RmdFlWdERremV4ZzdPcndnTHdXWVgyClJwdVlZZHN2SHh3dmhpZjJGellPU1dva0RPUm1aU2xMVUhSV1EyZUhBTGlzRjQzRG5PMjIvVGpyUUM4VwpvUFg2a2dnRzRBeWtXMFNlQXhLdDFiZkVHUW5Fcm9CVnlSL2JENzcySjU3SWplS2oydHdsV0ZZS284cXIKUXZNWlkyNkFzVW9FVUl6dlJpdHhpbENYRUJwZ0JDZVErV0xqcVZNZ2plbHlYUStGanFYZ1NYbUpRbWc4CmZGRmduTE0wWDBWZkV3VTBNaDQ3djJlbnJzdTA5b2xYRmN0THFOZXJkMm4vYVkwMXJhOGhZMmZlOStndQo3WjlmaSs3SVQ2dVNnc1BDbjBkTHVVOEFFUUVBQWY0SkF3Z0pCU1RrM0RxRC8rQTdvbTJjWExZQTJBaTQKOHdpRzNVZGgzejBSa1daQ0wrMkF2eGZVUDN1SkxoKytaRGNBZ0czTnFjWEUxcUp6QVlaNGN2N1pSd3k1Cjd5TktyS1pHNXlqb0ZsMkEyZkhsejY0dkRDdVFHYmdYdkRZcWo3K3h2enlBclozb2Zwc0hwMFBUeUkzSwpPWG9FTVRXQXdDQk0yNzFpMjl1Wi9tRG5yOFF6Smk4ZnFKVFBIN2o0WTg4Yk5uZFl0ZFhhOWpVN3FwNkoKNCtNN3RXZEZYeHJHRE53VWVGVUttOHBoZnpxV0RiNUs0U0lBMG5jYXRKaFhNU2hlSEhPbkZXZ21VZlBkCno5R0kyM3h3M1FqV3lxNkJUMnF3UlA2cUl3U3lMTDA0U05peUFSV21kWlhNNVBkamxERWgyMHJhNC9IWAp3Z1gvQ0dUdzh4c1paNGxKTTlaeXFZZUJ3UndxamNPUVJzYnlhZWZuNVR3THlIUUdqWG5XRHY4RXgxeHYKY0dSTVVTdzF4QVpIbm9ERFJueE1FR2g3M3FwUGxCNjdUeWl0aXVDTUNVeVJHWDBrTkwyeTRUMWZsdThZClZ1OUVDWFFOTXp5K0hNMDVmTWY1TUFqdXhRSVM5SjFXeFN1cFBIYnRiMDJGRGI0TDJYY3JVYlJBK0pudgpLR1djbXE4c3ZSTFRDS0tYN01RY2NYUGtaOUgyMFh1SlNUTXB1cmhOSlRqRnNHNTVwNjgycWhiNFFMemkKUUJwNWdqbGl6YTlaQm5DZDBGNnNUQWl6LzFXNnZ0aFFZbU5NVk9hU3JKU0dBWEc0bGdnVmpUTEtBaDY4CjBReGplMXorOUNUSE9zZXg5Z1ZOcDV1YkxLbnk4VFcxR2p5V2JXL3NuTERyUEZOaUxFanIxeXBXY2x0dApVZGZJZVlWc0pPRTYzZGw3TGZHR2VIUTdiRC84UHRuVjgxcWtsaWd0VXErSTI1SU16cWFsWjFnSGpMVG4KL040eFFFeVVDdll5anJVeGpKVFVFd0VwOGlpalgwVVluTGtsVVBWdExEYmFPc1ZvM2JpQVl0MDZiR1hoCml2MjJrNjBKbnhNNmpzcXk0OHduYTBka25aT1RkdGJtQXpnMUN3LzFqNFdxYm8yUzliL1Q4eDJiY0pwZApVeE1jcFpRSmJReFNsVFErcUcxaFhnQVMvQ2xac2tKcVdlT09aZE1QMG9DYUNEMDNuUXcyeG5kcUxEOC8KZmJEaHdvZCtWV0w0Mk42K3RKOGhvajhqTzJ3KzBsUHM1bXZOaFpGUm1qWm9LM0V2QU1mRkJOdTU2ZHc0CkI3Z0lsNlJGTGd5bXZjLzYyRHZKK2hkaWt2VGpZbE5LMSswd040aHcxUHpYZitKMllIVUZuVHZEeDNldgpPTExiNFk0dGZwZVIvb3pQRTlIOGhiS2xZaEIzcTNTcG0yWXVTQ3V4eDdDZUkxVnM5NnJSY1VTM0dGUXYKUFdQQ0FFRWtsNGJTdXhKMTNudHZQQzdHMGpQZWJwYWtFRmJzWlpEanYyc283aGludm9tQVZEU1RJMVFJClNMK3VOUzZIckhmQk9kWHpsaU45RnhOQU1DSXRhanZYUFlhR2dhOTZhZExRYWNONHU4bzVOZlNOT1BVNQo5QVFZZTQ0MXdmLytLa3FDaGlDSDdQS0pwd0h5RHpLTTRKb2V0V1NhNDRoT2pWekQ4UUY3WU9RS3NWRnoKUDhpNG9ZbmdWdjhmcUo3WHFqeHVLN0lSRkFCQTdxSnc1UkxHSzdWY0VMb0NGQ3BiVzN3dUxJVThLbnJHClZwV2pXUzg5T0c5OXhTenJIaDhIblNrN2N2N1R3Yk4xSEZLM2gwMVRwMG93KzZvS0xBU2wvaEVXOE9HdQpsYURmakhNY0FQY1FoTTRpNGMvK0xwYTU0WFhSWk1pYmF3aDNFNGNrOGlMUlJCaDNQUitKR0J6aG1SNFMKVE1tYzBPbnBiOEg1MkoxZXVQQURhMUhQcmU0YUJiVEM0ajdyNE56TFhaeFdXb2hoZTRaOXlVQ1kreisvCmF2amNvYUdjSTQ2bEswZGZweFpXR003TVczYlpBT2RGL0tuUm1GYXkwMWhHV000SGRyclg2eFJHL2FOMQp5a1p1L21TdG5ENWRoVjVVUWh3VEo4VzJEOVB0RUFVQ2pGSEl5ZzZ2akV2cUIyTzJNT1o1LzJkUjVrekYKS1dPQ0YrZHFqTGhaelAzY0FvMmdSeXZJUUlXeEFKVFZ0dmtzczJHZ0NKdVdaMmNaOGRnSEcwTVZ4MHVaCmFLNW1SVld0cndHcTlUWHEwRWE4SWdsZWNaR2RiYVFQSVhqRjgrTjFuVmlHYTZxaFh6azBNT3hYMm9LUwpoZ0p3bVdVQVBBZS93c0c4QkJnQkNnQndCWUpvSmRNTENaQWdEOHIvYWFpY1owVVVBQUFBQUFBY0FDQnoKWVd4MFFHNXZkR0YwYVc5dWN5NXZjR1Z1Y0dkd2FuTXViM0pua3lBRUpiZEpmVDkzUW5EdWVZbVFJenhJClovYUlEMlNYMTZBMmF2OE5QTEVDbXd3V0lRVGZQUTBObWQ0eW9LL0VjSDRnRDhyL2FhaWNad0FBQk5jUQpBSmhnU1JiUys0WURiSy9XQVlvdjlJTk05b2dNSFliY2xaeUZCQU0ySkIwblI3eUZTY1NYQ0pSQkhvbkMKeHBkSlhkaytqTEthMnQ3ckdoWGs4U0F4VUxoK1dId1FwcmFhMzdwb2JJRmxTVzFDaXpQRVByOHlocC8zCmRmaEhYTFllcG5oMjRLc2FDRlRiZ0VXNHZ5UUtldjRqSXJNd0ZpOEx6RzcrN1VxdVpDZmVjeFllZ25UOQpzOVcyQWJ0TE9UOUxpNnJmTWJHWk15dFQ5Vnd2TDhoZWo4ekkwVklTd1VyYmxZVG5rMmhCdEFsRDNJZksKUGx6Y05RdmhUOXh5MStsR2xCTDBFTU5JUDMzck1KM09GVjdUSVVNR05OcW51TXdrU1lmdHFSNjZpa2dRCnlmVURHNzFTQ2d3Wi9Qd1lacWdTZzFQZkxDclNpNWp2blBVQmZPaDB6WGxqbmg4ZnlmREFsbFZYY21STwpWaDR3bVF2dlpkUXArQkxOZk9WVlM1WkMzeEJsM2JjQThEaVlNVlFUcDNwL01SZkpxODFBUTJ3TnE5a24KTzc3aWYxcmxCbG9DT0FiMXRPeER3a0JhanRRWHZJSTBjRVdJNjJaNUc4aEpWYmIvUTdFOGxXRDhCUjNXClhLT3VyQXpKRHJsK1hML3g1WkhDTGRHUE1WMVlUNW0wT1MyU3JVZWk0K28zR0hlUjdjTHVsN29ZMVBYQQpwZDIwWTd4Q1NzdDNhMVQ5VlFpQTF1c1dMd0FES2wyTmxVelhKdVp6Z1lnV2dycUNSeEpObHpMRHptM3MKcXJGTlRYaEVTVjhMUTN4UGIxSnJwNFFGNmlXdXdvYmlYRi9TeFZzUDRyQ3ZUY3JuaGM3SW1iQjcyTFAwCnJsKzlDZ3QvcmhreHpRSU5RYSsrVTg3Qwo9MkcwQQotLS0tLUVORCBQR1AgUFJJVkFURSBLRVkgQkxPQ0stLS0tLQo=',
  publicKeyArmored:
    '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',
}

describe('File Encryption', () => {
  it('should encrypt and sign a file', async () => {
    const file = new File(['This is a test file content'], 'test.txt', {
      type: 'text/plain',
    })
    file.bytes = vi
      .fn()
      .mockResolvedValue(
        Uint8Array.from(
          Array.from('This is a test file content').map((letter) =>
            letter.charCodeAt(0)
          )
        )
      )
    const encrypted = await encryptAndSignFile(
      file,
      atob(pgpKeyPair.privateKeyArmored),
      atob(pgpKeyPair.publicKeyArmored),
      passphrase
    )

    expect(encrypted).equals('')
  })

  it('should decrypt and verify file file signature', async () => {})
})
