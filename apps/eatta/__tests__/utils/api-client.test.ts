/**
 * <AUTHOR> on 24/03/2025
 */

import { describe, it, vi } from 'vitest'

vi.mock('@dtbx/store/utils', () => ({
  refreshTokenIfNeeded: vi.fn(),
  secureapi: {
    interceptors: {
      request: {
        use: vi.fn(),
        clear: vi.fn(),
      },
    },
  },
}))

vi.mock('@/store/actions/eattaAuth', () => ({
  refreshUserToken: vi.fn(),
}))

describe('Api Client', () => {
  it('should set Authorization header if not back office', async () => {
    vi.stubEnv('NEXT_PUBLIC_EATTA_BUILD', 'client')
  })

  it('should not set Authorization header if back office', async () => {
    vi.stubEnv('NEXT_PUBLIC_EATTA_BUILD', 'backoffice')
  })
})
