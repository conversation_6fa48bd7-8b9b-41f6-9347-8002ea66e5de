/**
 * <AUTHOR> on 19/03/2025
 */

import { describe, expect, it } from 'vitest'
import { mapObjectToUrlParams, removeFalsyValues } from '@/utils/objectUtil'

describe('objectUtil', () => {
  describe('mapObjectToUrlParams', () => {
    it('should map object to url params', () => {
      const obj = {
        name: 'Ke<PERSON>',
        age: 23,
        isDeveloper: true,
        isStudent: false,
        isEmployed: true,
      }
      const result = mapObjectToUrlParams(obj)
      expect(result).toBe(
        'name=Kelvin&age=23&isDeveloper=true&isStudent=false&isEmployed=true'
      )
    })

    it('should only map boolean falsy object values to url params', () => {
      const obj = {
        name: '<PERSON><PERSON>',
        age: 23,
        isDeveloper: true,
        isStudent: undefined,
        isEmployed: null,
      }
      const result = mapObjectToUrlParams(obj)
      expect(result).toBe('name=<PERSON><PERSON>&age=23&isDeveloper=true')
    })
  })

  describe('removeFalsyValues', () => {
    it('should remove falsy values from an object', () => {
      const obj = {
        name: 'Kelvin',
        age: 23,
        gender: null,
      }
      const result = removeFalsyValues(obj)
      expect(result).toEqual({
        name: 'Kelvin',
        age: 23,
      })
    })

    it('should remove falsy values from an object that are not boolean', () => {
      const obj = {
        name: 'Kelvin',
        age: 23,
        isDeveloper: null,
        isStudent: false,
        isEmployed: true,
      }
      const result = removeFalsyValues(obj)
      expect(result).toEqual({
        name: 'Kelvin',
        age: 23,
        isStudent: false,
        isEmployed: true,
      })
    })
  })
})
