/**
 * <AUTHOR> on 25/04/2025
 */

import { describe, expect, it, vi } from 'vitest'
import { IDecodeToken } from '@dtbx/store/interfaces'
import { jwtDecode } from 'jwt-decode'
import { scheduleTokenRefresh } from '@/utils/refreshSchedule'

vi.mock('jwt-decode', () => ({
  jwtDecode: vi.fn(),
}))

describe('scheduleTokenRefresh', () => {
  const mockToken =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiYWRtaW4iOnRydWUsImlhdCI6MTUxNjIzOTAyMn0.KMUFsIDTnFmyG3nMiGM6H9FNFUROf3wh7SmqJp-QV30'
  const mockDecodedToken: IDecodeToken = {
    aud: '1a2b3c4d-5e6f-7g8h-9i10-j11k12l13m14',
    last_name: 'Do<PERSON>',
    first_name: '<PERSON>',
    user_id: 'usr123',
    authorities: ['CAN_VIEW', 'CAN_CREATE', 'CAN_UPDATE', 'CAN_DELETE'],
    sub: 'client',
    iat: Date.now(),
    exp: 1516242622 + 10000,
  }

  it('should not schedule refresh if no token exists in localStorage', () => {
    const mockSetSpy = vi
      .spyOn(Storage.prototype, 'getItem')
      .mockReturnValueOnce(null)

    const mockTimeOutSpy = vi.spyOn(global, 'setTimeout')

    scheduleTokenRefresh()

    expect(mockSetSpy).toHaveBeenCalledWith('accessToken')
    expect(mockTimeOutSpy).not.toHaveBeenCalled()
  })

  it('should schedule refresh', () => {
    const mockSetSpy = vi
      .spyOn(Storage.prototype, 'getItem')
      .mockReturnValueOnce(mockToken)

    const mockTimeOutSpy = vi.spyOn(global, 'setTimeout')
    const mockJwtDecode = vi.mocked(jwtDecode).mockReturnValue(mockDecodedToken)

    scheduleTokenRefresh()

    expect(mockJwtDecode).toHaveBeenCalledWith(mockToken)
    expect(mockSetSpy).toHaveBeenCalledWith('accessToken')
    expect(mockTimeOutSpy).toHaveBeenCalled()
  })
})
