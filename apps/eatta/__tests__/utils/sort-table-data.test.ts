/**
 * <AUTHOR> on 19/03/2025
 */

import { describe, expect, it } from 'vitest'
import { sortData } from '@/utils/sortTableData'

const sampleData = [
  { name: 'Alice', value: 30, createdDate: '2023-01-01' },
  { name: '<PERSON>', value: 25, createdDate: '2023-02-01' },
  { name: '<PERSON>', value: 35, createdDate: '2023-03-01' },
  { name: '<PERSON>', value: 20, createdDate: '2023-04-01' },
  { name: 'Eve', value: undefined, createdDate: '2023-05-01' },
]

describe('SortTableData', () => {
  it('should sort table data asc where key is string', () => {
    const result = sortData(sampleData, 'name', 'asc')
    expect(result).toEqual(sampleData)
  })

  it('should sort table data desc where key is string', () => {
    const result = sortData(sampleData, 'name', 'desc')
    expect(result).toEqual([
      { name: '<PERSON>', value: undefined, createdDate: '2023-05-01' },
      { name: '<PERSON>', value: 20, createdDate: '2023-04-01' },
      { name: '<PERSON>', value: 35, createdDate: '2023-03-01' },
      { name: 'Bob', value: 25, createdDate: '2023-02-01' },
      { name: 'Alice', value: 30, createdDate: '2023-01-01' },
    ])
  })

  it('should sort table data asc where key is number', () => {
    const result = sortData(sampleData, 'value', 'asc')
    expect(result).toEqual([
      { name: 'David', value: 20, createdDate: '2023-04-01' },
      { name: 'Bob', value: 25, createdDate: '2023-02-01' },
      { name: 'Alice', value: 30, createdDate: '2023-01-01' },
      { name: 'Charlie', value: 35, createdDate: '2023-03-01' },
      { name: 'Eve', value: undefined, createdDate: '2023-05-01' },
    ])
  })

  it('should sort table data desc where key is number', () => {
    const result = sortData(sampleData, 'value', 'desc')
    expect(result).toEqual([
      { name: 'Charlie', value: 35, createdDate: '2023-03-01' },
      { name: 'Alice', value: 30, createdDate: '2023-01-01' },
      { name: 'Bob', value: 25, createdDate: '2023-02-01' },
      { name: 'David', value: 20, createdDate: '2023-04-01' },
      { name: 'Eve', value: undefined, createdDate: '2023-05-01' },
    ])
  })

  it('should sort table data asc where key is date', () => {
    const result = sortData(sampleData, 'createdDate', 'asc')
    expect(result).toEqual([
      { name: 'Alice', value: 30, createdDate: '2023-01-01' },
      { name: 'Bob', value: 25, createdDate: '2023-02-01' },
      { name: 'Charlie', value: 35, createdDate: '2023-03-01' },
      { name: 'David', value: 20, createdDate: '2023-04-01' },
      { name: 'Eve', value: undefined, createdDate: '2023-05-01' },
    ])
  })

  it('should sort table data desc where key is date', () => {
    const result = sortData(sampleData, 'createdDate', 'desc')
    expect(result).toEqual([
      { name: 'Eve', value: undefined, createdDate: '2023-05-01' },
      { name: 'David', value: 20, createdDate: '2023-04-01' },
      { name: 'Charlie', value: 35, createdDate: '2023-03-01' },
      { name: 'Bob', value: 25, createdDate: '2023-02-01' },
      { name: 'Alice', value: 30, createdDate: '2023-01-01' },
    ])
  })
})
