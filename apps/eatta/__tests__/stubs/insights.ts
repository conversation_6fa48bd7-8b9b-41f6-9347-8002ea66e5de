/**
 * <AUTHOR> on 27/03/2025
 */

export const mockInsightsResponse = {
  status: "Success",
  message: "Request processed successfully.",
  data: {
    saleCode: "Sale 37 - M2",
    stats: {
      weightSold: 21840,
      weightUnsold: 0,
      valueUnsold: 0,
      lotCount: 8,
      valueSold: 34160,
      totalWeight: 21840,
      totalValue: 34160
    }
  }
}

export const mockTopMetricResponse = {
  status: "Success",
  message: "Request processed successfully.",
  data: {
    saleCode: "Sale 37 - M2",
    totalWeight: 21840,
    totalValue: 34160,
    topProducers: [
      {
        value: 13600,
        organizationCode: "ATE"
      },
      {
        value: 5520,
        organizationCode: "21313121Q"
      },
      {
        value: 2720,
        organizationCode: "ABX PRODUCER CO LIMITED"
      }
    ],
    topBuyers: [
      {
        value: 34160,
        organizationCode: "Buyer1002"
      }
    ]
  }
}