/**
 * <AUTHOR> on 04/04/2025
 */

import { describe, it, expect, vi } from 'vitest'
import { setSelectedEstateRecord, removeSelectedEstateRecord } from '@/store/actions'
import { setSelectedEstates, removeSelectedEstates } from '@/store/reducers'
import { Estates } from '@/store/interfaces'

const mockEstates: Estates[] = [
  {
    id: '40b5e70a-3d9b-4232-adc6-8adb3174c382',
    estateNumber: 'E123',
    estate: 'Green Acres',
    totalWeight: '1500kg',
    totalEarnings: '$3000',
    broker: '<PERSON>'
  },
  {
    id: '40b5e70a-3d9b-4232-adc6-8adb3174c384',
    estateNumber: 'E124',
    estate: 'Sunny Fields',
    totalWeight: '2000kg',
    totalEarnings: '$4000',
    broker: '<PERSON>'
  }
]

describe('Estates Actions', () => {
  const mockDispatch = vi.fn()

  it('should dispatch setSelectedEstates action', () => {



    setSelectedEstateRecord(mockDispatch, mockEstates)

    expect(mockDispatch).toHaveBeenCalledWith(setSelectedEstates(mockEstates))
  })

  it('should dispatch removeSelectedEstates action', () => {
    const id =  '40b5e70a-3d9b-4232-adc6-8adb3174c382'
    removeSelectedEstateRecord(mockDispatch, id)

    expect(mockDispatch).toHaveBeenCalledWith(removeSelectedEstates(id))
  })
})