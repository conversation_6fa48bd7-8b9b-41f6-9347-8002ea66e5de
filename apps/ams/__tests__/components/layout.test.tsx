import { render, screen, fireEvent } from '../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import RootLayout from '@/app/layout'
import { useAppDispatch, useAppSelector } from '@dtbx/store'

// Mock the dependencies
vi.mock('@dtbx/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

vi.mock('@dtbx/store/actions', () => ({
  refreshToken: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  clearNotification: vi.fn(),
  setDocumentToggle: vi.fn(),
  setSidebarCollapsed: vi.fn(),
}))

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
    pathname: '/test',
  }),
}))

// Mock the UI components
vi.mock('@dtbx/ui/components', () => ({
  AuthWrapper: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-wrapper">{children}</div>
  ),
  CustomScrollbar: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="custom-scrollbar">{children}</div>
  ),
  InActivity: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="inactivity">{children}</div>
  ),
  LocalNotification: () => <div data-testid="local-notification" />,
}))

vi.mock('@dtbx/ui/components/Overlay', () => ({
  IDView: () => <div data-testid="id-view" />,
}))

vi.mock('@dtbx/ui/components/Sidebar', () => ({
  Sidebar: () => <div data-testid="sidebar" />,
}))

vi.mock('@dtbx/ui/components/Appbar', () => ({
  InternalNavBar: () => <div data-testid="navbar" />,
}))

vi.mock('@dtbx/ui/theme', () => ({
  NextAppDirEmotionCacheProvider: ({
    children,
  }: {
    children: React.ReactNode
  }) => <div data-testid="emotion-cache">{children}</div>,
  ThemeConfig: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="theme-config">{children}</div>
  ),
}))

vi.mock('@/store/AppProvider', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="app-provider">{children}</div>
  ),
}))

vi.mock('@/app/sidebar', () => ({
  sidebarConfig: [
    {
      id: '1',
      title: 'Users',
      path: '/users',
      module: 'users',
      isProductionReady: true,
      icon: () => <div>Icon</div>,
    },
  ],
}))

describe('RootLayout', () => {
  const mockDispatch = vi.fn()
  const mockSelector = {
    auth: {
      isAuthenticated: true,
      decodedToken: {
        name: 'Test User',
        email: '<EMAIL>',
      },
      channelModules: [
        {
          channel: 'IAM',
          modules: ['users', 'roles'],
        },
      ],
    },
    notifications: {
      localNotification: null,
      localNotificationType: 'info',
    },
    navigation: {
      isSidebarCollapsed: false,
      documentToggle: {
        open: false,
      },
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      return selector(mockSelector as any)
    })
  })

  it('renders the layout with all required components', () => {
    render(
      <RootLayout>
        <div data-testid="test-content">Test Content</div>
      </RootLayout>
    )

    expect(screen.getByTestId('app-provider')).toBeInTheDocument()
    expect(screen.getByTestId('custom-scrollbar')).toBeInTheDocument()
    expect(screen.getByTestId('inactivity')).toBeInTheDocument()
    expect(screen.getByTestId('test-content')).toBeInTheDocument()
  })

  it('renders children content correctly', () => {
    render(
      <RootLayout>
        <div data-testid="child-content">Child Content</div>
      </RootLayout>
    )

    expect(screen.getByTestId('child-content')).toBeInTheDocument()
    expect(screen.getByText('Child Content')).toBeInTheDocument()
  })

  it('includes required theme and provider components', () => {
    render(
      <RootLayout>
        <div>Test</div>
      </RootLayout>
    )

    expect(screen.getByTestId('app-provider')).toBeInTheDocument()
    expect(screen.getByTestId('custom-scrollbar')).toBeInTheDocument()
    expect(screen.getByTestId('inactivity')).toBeInTheDocument()
  })

  it('renders with proper structure', () => {
    render(
      <RootLayout>
        <div data-testid="test-content">Test</div>
      </RootLayout>
    )

    // Check that the layout renders the content properly
    expect(screen.getByTestId('test-content')).toBeInTheDocument()
    expect(screen.getByText('Test')).toBeInTheDocument()

    // Verify the layout structure components are present
    expect(screen.getByTestId('app-provider')).toBeInTheDocument()
  })
})
