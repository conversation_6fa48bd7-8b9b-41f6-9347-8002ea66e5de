import { Box, Button, Typography } from '@mui/material'
import React from 'react'
import { useAppDispatch } from '@/store'
import { IUser } from '@/store/interfaces'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { activateUser, changeUserStatus, deactivateUser } from '@/store/actions'
import { Dialog } from '@dtbx/ui/components/Overlay/CustomDialog'

interface IDeactivate {
  user: IUser
}

const DeactivateUser: React.FC<IDeactivate> = ({ user }) => {
  const { firstName, status, id } = user
  const [open, setOpen] = React.useState(false)
  const dispatch = useAppDispatch()

  const reasonsForDeactivating: string[] = [
    'Regulatory Compliance',
    'Service Integration Issue',
    'Operational Changes',
    'Fraud Mitigation',
    'Other',
  ]

  const reasonsForActivating: string[] = [
    'Reinstatement',
    'Resolved Issues',
    'Other',
  ]

  const reasons =
    status === 'ACTIVE' ? reasonsForDeactivating : reasonsForActivating

  const handleDeactivate = async (reasons: string[]) => {
    const comments = reasons.join(', ')
    if (status === 'ACTIVE') {
      if (HasAccessToRights(['SUPER_DEACTIVATE_USER'])) {
        await changeUserStatus(id, 'deactivate', dispatch, comments)
      } else {
        await deactivateUser(id, 'make', dispatch, comments)
      }
    } else {
      if (HasAccessToRights(['SUPER_ACTIVATE_USERS'])) {
        await changeUserStatus(id, 'activate', dispatch, comments)
      } else {
        await activateUser(id, 'make', dispatch, comments)
      }
    }
    setOpen(false)
  }
  return (
    <Box>
      <AccessControlWrapper
        rights={[
          'MAKE_ACTIVATE_USERS',
          'SUPER_ACTIVATE_USERS',
          'SUPER_DEACTIVATE_USERS',
          'MAKE_DEACTIVATE_USERS',
        ]}
      >
        <Button
          sx={{
            border: '1px solid #D0D5DD',
            borderRadius: '6px',
            padding: '9px 28px',
          }}
          variant="outlined"
          onClick={() => {
            setOpen((prev) => !prev)
          }}
        >
          <Typography
            sx={{
              color: '#555C61',
              fontSize: '13px',
              lineHeight: '16px',
              fontWeight: 500,
            }}
          >
            {status === 'ACTIVE'
              ? `Deactivate ${firstName}`
              : `Activate ${firstName}`}
          </Typography>
        </Button>
      </AccessControlWrapper>
      <Dialog
        buttonText={status === 'ACTIVE' ? 'Deactivate' : 'Activate'}
        title={
          status === 'ACTIVE'
            ? `Deactivate ${firstName}`
            : `Activate ${firstName}`
        }
        open={open}
        descriptionText={`${firstName} will lose access to all assigned
        rights. Are you sure you want to deactivate
        ${firstName}?`}
        setOpen={setOpen}
        buttonProps={{
          color: status === 'ACTIVE' ? '#EB0045' : '#12B76A',
        }}
        reasons={reasons}
        onClick={(reason) => {
          handleDeactivate(reason)
        }}
      />
    </Box>
  )
}

export default DeactivateUser
