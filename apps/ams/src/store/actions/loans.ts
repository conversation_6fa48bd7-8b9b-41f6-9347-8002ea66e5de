import { Dispatch } from '@reduxjs/toolkit'
import { AxiosError } from 'axios'
import { jwtDecode } from 'jwt-decode'

import {
  setApprovalActions,
  setAssignBrokerProductFailure,
  setAssignBrokerProductLoading,
  setAssignBrokerProductSuccess,
  setBankBranches,
  setBrokerProducts,
  setBrokers,
  setBrokersSummary,
  setCreateBrokerFailure,
  setCreateBrokerSuccess,
  setCreatedBroker,
  setCreatedProduct,
  setCustomerChecks,
  setCustomerDocuments,
  setCustomerProfile,
  setEditOrganization,
  setGenerateBrokerSecretFailure,
  setGenerateBrokerSecretSuccess,
  setGeneratedLoanReportLoading,
  setGeneratedLoanReportSuccess,
  setGeneratedRequestReportLoading,
  setIsCheckRerunLoading,
  setIsGenerateBrokerSecretLoading,
  setIsLoadingAddValidation,
  setIsLoadingBrokers,
  setIsLoadingCancelRequest,
  setIsLoadingCreateCategory,
  setIsLoadingCreateProduct,
  setIsLoadingCustomerProfile,
  setIsLoadingEditOrganization,
  setIsLoadingLoanProducts,
  setIsLoadingOrganizations,
  setIsLoadingOverrideValidationCheck,
  setIsLoadingProductValidations,
  setIsLoadingUpdateCategory,
  setIsLoadingUpdateProduct,
  setIsLoadingUploadDocument,
  setLoadingCreateBroker,
  setLoadingCreateOrganization,
  setLoadingLoans,
  setLoadingUpdateBroker,
  setLoanProducts,
  setLoanProductsSummary,
  setLoanRepaymentHistory,
  setLoanReportLoading,
  setLoanReports,
  setLoanReportsSuccess,
  setLoanRequestReportLoading,
  setLoanRequestReportsResponse,
  setLoanRequestReportSuccess,
  setLoanRequests,
  setLoanRequestsSummary,
  setLoanValidations,
  setOrganizationLimitReport,
  setOrganizationLimitReportExportLoading,
  setOrganizationLimitReportExportSuccess,
  setOrganizationLimitReportLoading,
  setOrganizationLimitReportSuccess,
  setOrganizations,
  setOrganizationsSummary,
  setProductCategories,
  setProductCategoryTypes,
  setProductToView,
  setProductValidations,
  setRequestCheckReportsExportLoading,
  setRequestCheckReportsExportSuccess,
  setRequestCheckReportsLoading,
  setRequestCheckReportsResponse,
  setRequestCheckReportsSuccess,
  setSelectedRequest,
  setSingleProductValidations,
  setUpdateBrokerFailure,
  setUpdateBrokerSuccess,
  setUserProducts,
} from '../reducers'
import { setNotification } from '@dtbx/store/reducers'
import { getApprovals } from './approvalRequests'
import { downloadBlob, secureapi2 } from '@dtbx/store/utils'
import {
  IBrokerCreate,
  IBrokerDetails,
  ICustomerDocumentUpload,
  IDecodeToken,
  IGenerateBrokerCreds,
  ILoanProduct,
  ILoanProductCreate,
  ILoanRequestsSummary,
  IOrganizationCreate,
  IOrganizationMake,
  IProductValidationCreate,
  IWorldCheckOverride,
} from '../interfaces'

export const getLoanRequests = async (dispatch: Dispatch, params: string) => {
  dispatch(setLoadingLoans(true))
  dispatch(setLoanRequests([]))
  try {
    const response = await secureapi2.get(
      params
        ? `/lms/loan-requests?${params}`
        : '/lms/loan-requests?productId=7d8dbd81-4b7e-4418-a354-0b4c387da5b4'
    )
    dispatch(setLoadingLoans(false))
    dispatch(setLoanRequests(response.data.data))
    const { pageNumber, pageSize, totalNumberOfPages, totalElements } =
      response.data
    dispatch(
      setLoanRequestsSummary({
        pageNumber,
        pageSize,
        totalNumberOfPages,
        totalElements,
      })
    )
  } catch (error) {
    dispatch(setLoadingLoans(false))
    console.error('Error fetching loan requests: ', error)
  }
}
export const getLoanRequestById = async (
  dispatch: Dispatch,
  requestId: string
) => {
  dispatch(setLoadingLoans(true))
  try {
    const response = await secureapi2.get(`/lms/loan-requests?id=${requestId}`)
    dispatch(setLoadingLoans(false))
    dispatch(setSelectedRequest(response.data.data[0]))
    return response.data.data[0]
  } catch (error) {
    dispatch(setLoadingLoans(false))
    console.error('Error fetching loan request by id: ', error)
  }
}

export const getLoanCustomerProfile = async (
  dispatch: Dispatch,
  customerId: string,
  requestId: string
) => {
  dispatch(setIsLoadingCustomerProfile(true))
  try {
    const response = await secureapi2.get(`/lms/customers/${customerId}`)
    dispatch(setCustomerProfile(response.data))
    await getCustomerDocumentsByRequestId(dispatch, requestId)
    await getCustomerChecks(dispatch, requestId)
    dispatch(setIsLoadingCustomerProfile(false))
  } catch (error) {
    dispatch(setIsLoadingCustomerProfile(false))
    console.error('Error fetching loan requests: ', error)
  }
}
export const getCustomerDocuments = async (
  dispatch: Dispatch,
  customerId: string
) => {
  // dispatch(setCustomerDocuments([]))
  try {
    const response = await secureapi2.get(
      `/lms/customers/${customerId}/documents`
    )
    dispatch(setCustomerDocuments(response.data))
  } catch (error) {
    console.error('Error fetching customer documents: ', error)
  }
}
export const getCustomerDocumentsByRequestId = async (
  dispatch: Dispatch,
  requestId: string
) => {
  dispatch(setCustomerDocuments([]))
  try {
    const response = await secureapi2.get(
      `/lms/loan-requests/${requestId}/documents`
    )
    dispatch(setCustomerDocuments(response.data))
  } catch (error) {
    console.error('Error fetching customer documents by request id: ', error)
  }
}
export const getCustomerChecks = async (
  dispatch: Dispatch,
  requestId: string
) => {
  dispatch(setCustomerChecks([]))
  try {
    const response = await secureapi2.get(
      `/lms/loan-requests/${requestId}/checks`
    )
    dispatch(setCustomerChecks(response.data))
  } catch (error) {
    console.error('Error fetching customer checks: ', error)
  }
}
export const getLoanRepayments = async (dispatch: Dispatch, loanId: string) => {
  try {
    const response = await secureapi2.get(`/lms/loans/${loanId}/repayments`)
    dispatch(setLoanRepaymentHistory(response.data.repayments))
  } catch (error) {
    console.error('Error fetching loan repayments: ', error)
  }
}

//products apis
export const getLoanProducts = async (dispatch: Dispatch, params?: string) => {
  dispatch(setIsLoadingLoanProducts(true))
  try {
    const response = await secureapi2.get(`/lms/products?${params || ''}`)
    const { data, ...rest } = response.data
    dispatch(setLoanProducts(data))
    dispatch(setLoanProductsSummary(rest as ILoanRequestsSummary))
    const accessToken = localStorage.getItem('accessToken')
    if (accessToken) {
      const decodedToken: IDecodeToken = jwtDecode(accessToken)
      const userProducts = data.filter((product: ILoanProduct) => {
        if (decodedToken.resources && decodedToken.resources[0]) {
          return decodedToken.resources[0].resourceIds.includes(product.id)
        }
      })
      dispatch(setUserProducts(userProducts))
    }
    dispatch(setIsLoadingLoanProducts(false))
    return response.data
  } catch (e) {
    dispatch(setIsLoadingLoanProducts(false))
  }
}
export const getLoanProductById = async (
  productId: string,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingLoanProducts(true))
  try {
    const response = await secureapi2.get(`/lms/products/${productId}`)
    dispatch(setIsLoadingLoanProducts(false))
    dispatch(setProductToView(response.data))
    return response.data
  } catch (e) {
    console.error('Error fetching loan product: ', e)
    dispatch(setIsLoadingLoanProducts(false))
  }
}
export const createLoanProduct = async (
  data: ILoanProductCreate,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingCreateProduct(true))
  try {
    const response = await secureapi2.post('/lms/products', data)
    dispatch(setIsLoadingCreateProduct(false))
    dispatch(
      setNotification({
        message: 'Loan product created successfully',
        type: 'success',
      })
    )
    await getLoanProducts(dispatch, 'page=1&size=10')
    dispatch(setCreatedProduct(response.data))
    return response.data
  } catch (e) {
    console.error('Error creating loan product: ', e)
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingCreateProduct(false))
  }
}

export const updateLoanProduct = async (
  productId: string,
  data: ILoanProductCreate,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingUpdateProduct(true))
  try {
    const response = await secureapi2.put(`/lms/products/${productId}`, data)
    dispatch(setIsLoadingUpdateProduct(false))
    dispatch(
      setNotification({
        message: 'Loan product updated successfully',
        type: 'success',
      })
    )
    await getLoanProductById(productId, dispatch)
    return response.data
  } catch (e) {
    console.error('Error updating loan product: ', e)
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingUpdateProduct(false))
  }
}
//product categories apis
export const getLoanProductCategories = async (dispatch: Dispatch) => {
  try {
    dispatch(setIsLoadingLoanProducts(true))
    const response = await secureapi2.get('/lms/product-categories')
    dispatch(setIsLoadingLoanProducts(false))
    dispatch(setProductCategories(response?.data?.data))
    return response.data
  } catch (e) {
    console.error('Error fetching product categories: ', e)
    dispatch(setIsLoadingLoanProducts(false))
  }
}
export const createProductCategory = async (
  dispatch: Dispatch,
  data: { code: string; name: string }
) => {
  dispatch(setIsLoadingCreateCategory(true))
  try {
    await secureapi2.post('/lms/product-categories', data)

    dispatch(setIsLoadingCreateCategory(false))
    await getLoanProductCategories(dispatch)
    dispatch(
      setNotification({
        message: 'Product category created successfully',
        type: 'success',
      })
    )
  } catch (e) {
    console.error('Error creating loan product category: ', e)
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingCreateCategory(false))
  }
}
export const updateProductCategory = async (
  dispatch: Dispatch,
  data: { name: string },
  id: string
) => {
  dispatch(setIsLoadingUpdateCategory(true))
  try {
    await secureapi2.put(`/lms/product-categories/${id}`, data)

    dispatch(setIsLoadingUpdateCategory(false))
    await getLoanProductCategories(dispatch)
    dispatch(
      setNotification({
        message: 'Product category updated successfully',
        type: 'success',
      })
    )
  } catch (e) {
    console.error('Error updating loan product category: ', e)
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingUpdateCategory(false))
  }
}
export const getLoanProductCategoryTypes = async (
  dispatch: Dispatch,
  categoryId: string
) => {
  try {
    dispatch(setIsLoadingLoanProducts(true))
    const response = await secureapi2.get(
      `/lms/product-categories/${categoryId}/types`
    )
    dispatch(setIsLoadingLoanProducts(false))
    dispatch(setProductCategoryTypes(response.data))
    return response.data
  } catch (e) {
    console.error('Error fetching product category types: ', e)
    dispatch(setIsLoadingLoanProducts(false))
  }
}
export const getProductCategoryById = async (
  dispatch: Dispatch,
  categoryId: string
) => {
  try {
    const response = await secureapi2.get(
      `/lms/product-categories/${categoryId}`
    )
    return response.data
  } catch (e) {
    console.error('Error fetching product category types: ', e)
  }
}
//organization apis
export const getOrganizations = async (dispatch: Dispatch, params: string) => {
  try {
    dispatch(setIsLoadingOrganizations(true))
    const response = await secureapi2.get(`/lms/organizations?${params || ''}`)

    dispatch(setIsLoadingOrganizations(false))
    const { data, ...rest } = response.data
    dispatch(setOrganizations(data))
    dispatch(setOrganizationsSummary(rest as ILoanRequestsSummary))
    return response.data
  } catch (e) {
    console.error('Error fetching organizations: ', e)
    dispatch(setIsLoadingOrganizations(false))
  }
}
export const createOrganization = async (
  dispatch: Dispatch,
  data: IOrganizationCreate
) => {
  dispatch(setLoadingCreateOrganization(true))
  try {
    const response = await secureapi2.post('/lms/organizations', data)
    dispatch(setLoadingCreateOrganization(false))
    dispatch(
      setNotification({
        message: 'Organization created successfully',
        type: 'success',
      })
    )
    return response.data
  } catch (e) {
    console.error('Error creating organization: ', e)
    const message = (e as Error).message
    dispatch(setNotification({ message: message, type: 'error' }))
    dispatch(setLoadingCreateOrganization(false))
  }
}

export const updateOrganization = async (
  orgId: string,
  dispatch: Dispatch,
  params: IOrganizationMake
) => {
  dispatch(setIsLoadingEditOrganization(true))
  try {
    await secureapi2.put(`/lms/organizations/${orgId}`, params)
    dispatch(
      setNotification({
        message: 'Organization was successfully updated',
        type: 'success',
      })
    )
    dispatch(setIsLoadingEditOrganization(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditOrganization(false))
  }
}

export const makeCreateOrganization = async (
  dispatch: Dispatch,
  data: IOrganizationMake
) => {
  dispatch(setLoadingCreateOrganization(true))
  try {
    await secureapi2.post('/lms/organizations/make', data)
    dispatch(
      setNotification({
        message: 'Organization created successfully, awaiting approval',
        type: 'success',
      })
    )
    dispatch(setLoadingCreateOrganization(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setLoadingCreateOrganization(false))
  }
}

export const getOrganization = async (orgID: string, dispatch: Dispatch) => {
  try {
    const response = await secureapi2.get(`/lms/organizations/${orgID}`)
    dispatch(setEditOrganization(response.data))
  } catch (e) {
    console.error('Error fetching organizations: ', e)
  }
}

export const makeUpdateOrganization = async (
  dispatch: Dispatch,
  orgId: string,
  data: IOrganizationMake
) => {
  dispatch(setIsLoadingEditOrganization(true))
  try {
    await secureapi2.put(`/lms/organizations/${orgId}/make`, data)
    dispatch(
      setNotification({
        message: 'Organization updated successfully, awaiting approval',
        type: 'success',
      })
    )
    dispatch(setIsLoadingEditOrganization(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditOrganization(false))
  }
}

export const approveOrganizationRequest = async (
  dispatch: Dispatch,
  approvalId: string
) => {
  dispatch(setIsLoadingEditOrganization(true))
  try {
    await secureapi2.put(`/lms/organizations/approve/${approvalId}`, {
      comments: 'Approved',
    })
    dispatch(
      setNotification({
        message: 'Organization request was approved successfully',
        type: 'success',
      })
    )
    dispatch(setIsLoadingEditOrganization(false))
    await getApprovals(dispatch, '?channel=LMS')
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditOrganization(false))
  }
}

export const rejectOrganizationRequest = async (
  dispatch: Dispatch,
  approvalID: string
) => {
  dispatch(setIsLoadingEditOrganization(true))
  try {
    await secureapi2.put(`/lms/organizations/reject/${approvalID}`, {
      comments: 'Rejected',
    })
    dispatch(
      setNotification({
        message: 'Organization request was rejected successfully',
        type: 'success',
      })
    )
    dispatch(setIsLoadingEditOrganization(false))
    await getApprovals(dispatch, '?channel=LMS')
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingEditOrganization(false))
  }
}

//override worldcheck apis
export const initiateOverrideValidationCheck = async (
  data: IWorldCheckOverride,
  requestId: string,
  checkId: string,
  dispatch: Dispatch
) => {
  try {
    dispatch(setIsLoadingOverrideValidationCheck(true))
    const response = await secureapi2.put(
      `/lms/loan-requests/${requestId}/checks/${checkId}/override/make`,
      data
    )
    dispatch(setIsLoadingOverrideValidationCheck(false))
    dispatch(
      setNotification({
        message: 'PaymentDetails check override initiated successfully',
        type: 'success',
      })
    )
    await getCustomerChecks(dispatch, requestId)
    return response.data
  } catch (e) {
    console.error('Error initiating overriding validation check: ', e)
    dispatch(setIsLoadingOverrideValidationCheck(false))
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
export const superOverrideValidationCheck = async (
  data: IWorldCheckOverride,
  requestId: string,
  checkId: string,
  dispatch: Dispatch
) => {
  try {
    dispatch(setIsLoadingOverrideValidationCheck(true))
    const response = await secureapi2.put(
      `/lms/loan-requests/${requestId}/checks/${checkId}/override`,
      data
    )
    dispatch(
      setNotification({
        message: 'PaymentDetails check override was successful',
        type: 'success',
      })
    )
    dispatch(setIsLoadingOverrideValidationCheck(false))
    await getCustomerChecks(dispatch, requestId)
    return response.data
  } catch (e) {
    console.error('Error overriding validation check: ', e)

    dispatch(setIsLoadingOverrideValidationCheck(false))
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const completeValidationOverride = async (
  data: { comments: string },
  action: string, //reject or approve
  approvalId: string,
  dispatch: Dispatch
) => {
  try {
    dispatch(setIsLoadingOverrideValidationCheck(true))
    const response = await secureapi2.put(
      `/lms/loan-requests/checks/override/${action}/${approvalId}`,
      data
    )
    const message = action === 'approve' ? 'approved' : 'rejected'
    dispatch(
      setNotification({
        message: `World check override was ${message} successfully`,
        type: 'success',
      })
    )
    dispatch(setIsLoadingOverrideValidationCheck(false))
    return response.data
  } catch (e) {
    console.error('Error checking world check override: ', e)
    dispatch(setIsLoadingOverrideValidationCheck(false))
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
export const rerunCheck = async (
  requestId: string,
  data: { checkId: string },
  dispatch: Dispatch
) => {
  try {
    dispatch(setIsCheckRerunLoading(true))
    const response = await secureapi2.put(
      `/lms/loan-requests/${requestId}/check-rerun`,
      data
    )
    dispatch(setIsCheckRerunLoading(false))
    dispatch(
      setNotification({
        message: 'Checks rerun successfully',
        type: 'success',
      })
    )

    return response.data
  } catch (e) {
    console.error('Error rerunning checks: ', e)

    dispatch(setIsCheckRerunLoading(false))
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

// loan validations apis
export const getAllValidations = async (dispatch: Dispatch) => {
  try {
    const response = await secureapi2.get('/lms/validations')
    dispatch(setLoanValidations(response.data))
  } catch (e) {
    console.error('Error on getting validations: ', e)
  }
}

export const addProductValidation = async (
  data: IProductValidationCreate[],
  productId: string,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingAddValidation(true))
  try {
    const response = await secureapi2.put(
      `/lms/products/${productId}/validations`,
      data
    )
    dispatch(setIsLoadingAddValidation(false))
    dispatch(
      setNotification({
        message: 'Validations added successfully',
        type: 'success',
      })
    )
    return response.data
  } catch (e) {
    console.error('Error adding validation: ', e)
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingAddValidation(false))
  }
}
export const getValidationsByProductId = async (
  productId: string,
  dispatch: Dispatch
) => {
  dispatch(setIsLoadingProductValidations(true))
  dispatch(setSingleProductValidations([]))
  try {
    const response = await secureapi2.get(
      `/lms/products/${productId}/validations`
    )
    dispatch(setIsLoadingProductValidations(false))
    dispatch(setSingleProductValidations(response.data))
    dispatch(setProductValidations(response.data))
  } catch (e) {
    console.error('Error fetching product validations: ', e)

    dispatch(setIsLoadingProductValidations(false))
  }
}
export const getBankBranches = async (dispatch: Dispatch) => {
  try {
    const response = await secureapi2.get('/lms/bank-branches')
    dispatch(setBankBranches(response.data))
  } catch (e) {
    console.error('Error fetching bank branches: ', e)
  }
}
//lms reports
export const getOrganizationLimitReports = async ({
  dispatch,
  params,
}: {
  dispatch: Dispatch
  params: string
}) => {
  dispatch(setOrganizationLimitReportLoading(true))

  try {
    const res = await secureapi2.get(
      `/lms/loan-reports/organization-limit?${params || ''}`
    )
    dispatch(setOrganizationLimitReport(res.data))
    dispatch(setOrganizationLimitReportLoading(false))
    dispatch(setOrganizationLimitReportSuccess(true))
  } catch (err: unknown) {
    dispatch(setOrganizationLimitReportLoading(false))
    dispatch(setOrganizationLimitReportSuccess(false))

    let errorMessage = 'An unknown error occurred'
    if (err instanceof AxiosError) {
      errorMessage = err.message
    }
    dispatch(
      setNotification({
        message: errorMessage,
        type: 'error',
      })
    )
  }
}
export const generateOrganizationLimitReport = async ({
  dispatch,
  params,
}: {
  dispatch: Dispatch
  params: string
}) => {
  // const { page, size, organizationName } = params
  // let url = `/lms/loan-reports/organization-limit/export-to-excel?page=${page}&size=${size}`
  // url += organizationName ? `&organizationName=${organizationName}` : ''

  dispatch(setOrganizationLimitReportExportLoading(true))
  dispatch(setOrganizationLimitReportExportSuccess(false))
  try {
    const res = await secureapi2.get(
      `/lms/loan-reports/organization-limit/export-to-excel?${params}`,
      { responseType: 'blob' }
    )

    dispatch(setOrganizationLimitReportExportLoading(false))
    dispatch(setOrganizationLimitReportExportSuccess(true))
    dispatch(
      setNotification({
        message: 'Organization limit report generated successfully',
        type: 'success',
      })
    )
    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    downloadBlob(blob, 'Organization_Limit_Report.xlsx')
  } catch (e) {
    dispatch(setOrganizationLimitReportExportLoading(false))
    dispatch(setOrganizationLimitReportExportSuccess(false))
    dispatch(
      setNotification({
        message: (e as Error).message,
        type: 'error',
      })
    )
  }
}

export const getLoanRequestReports = async ({
  dispatch,
  params,
}: {
  dispatch: Dispatch
  params: {
    page: number
    size: number
    status?: string
    organizationName?: string
    requestStartDate?: string
    requestEndDate?: string
  }
}) => {
  const {
    page,
    size,
    status,
    organizationName,
    requestEndDate,
    requestStartDate,
  } = params
  let url = `/lms/loan-reports/requests?page=${page}&size=${size}`
  url += status ? `&status=${status}` : ''
  url += organizationName ? `&organizationName=${organizationName}` : ''
  url += requestStartDate ? `&requestStartDate=${requestStartDate}` : ''
  url += requestEndDate ? `&requestEndDate=${requestEndDate}` : ''
  dispatch(setLoanRequestReportLoading(true))
  try {
    const res = await secureapi2.get(url)

    dispatch(setLoanRequestReportLoading(false))
    dispatch(setLoanRequestReportsResponse(res.data))
    dispatch(setLoanRequestReportSuccess(true))
    dispatch(
      setNotification({
        message: 'Requests report was succesfully retrieved',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const generateLoanRequestReports = async ({
  dispatch,
  params,
}: {
  dispatch: Dispatch
  params: {
    page: number
    size: number
    status?: string
    organizationName?: string
    requestStartDate?: string
    requestEndDate?: string
  }
}) => {
  const {
    page,
    size,
    status,
    organizationName,
    requestEndDate,
    requestStartDate,
  } = params
  let url = `/lms/loan-reports/requests/export-to-excel?page=${page}&size=${size}`
  url += status ? `&status=${status}` : ''
  url += organizationName ? `&organizationName=${organizationName}` : ''
  url += requestStartDate ? `&requestStartDate=${requestStartDate}` : ''
  url += requestEndDate ? `&requestEndDate=${requestEndDate}` : ''
  dispatch(setGeneratedRequestReportLoading(true))
  try {
    const res = await secureapi2.get(url, { responseType: 'arraybuffer' })

    dispatch(setGeneratedRequestReportLoading(false))
    dispatch(setGeneratedLoanReportSuccess(true))
    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    dispatch(
      setNotification({
        message: 'Requests report was succesfully generated',
        type: 'success',
      })
    )
    downloadBlob(blob, 'Loan_Requests_Report.xlsx')
  } catch (e) {
    const message = (e as Error).message
    dispatch(setGeneratedRequestReportLoading(false))
    dispatch(setGeneratedLoanReportSuccess(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const getLoanReports = async ({
  dispatch,
  params,
}: {
  dispatch: Dispatch
  params: {
    page: number | 1
    size: number | 10
    status?: string
    organizationName?: string
    creationStartDate?: string
    creationEndDate?: string
    maturityStartDate?: string
    maturityEndDate?: string
  }
}) => {
  const {
    page,
    size,
    status,
    organizationName,
    creationStartDate,
    creationEndDate,
    maturityStartDate,
    maturityEndDate,
  } = params
  let url = `/lms/loan-reports/loans`
  url += `?page=${page !== undefined ? page : 1}&size=${size !== undefined ? size : 10}`
  url += status ? `&status=${status}` : ''
  url += organizationName ? `&organizationName=${organizationName}` : ''
  url += creationStartDate ? `&creationStartDate=${creationStartDate}` : ''
  url += creationEndDate ? `&creationEndDate=${creationEndDate}` : ''
  url += maturityStartDate ? `&maturityStartDate=${maturityStartDate}` : ''
  url += maturityEndDate ? `&maturityEndDate=${maturityEndDate}` : ''

  dispatch(setLoanRequestReportLoading(true))
  try {
    const res = await secureapi2.get(url)

    dispatch(setLoanReportLoading(false))
    dispatch(setLoanReports(res.data))
    dispatch(setLoanReportsSuccess(true))
    dispatch(
      setNotification({
        message: 'Loans report was succesfully retrieved',
        type: 'success',
      })
    )
  } catch (e) {
    const message = (e as Error).message
    dispatch(setLoanReportLoading(false))
    dispatch(setLoanReportsSuccess(false))
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}

export const generateLoanReports = async ({
  dispatch,
  params,
}: {
  dispatch: Dispatch
  params: {
    page: number
    size: number
    status?: string
    organizationName?: string
    creationStartDate?: string
    creationEndDate?: string
    maturityStartDate?: string
    maturityEndDate?: string
  }
}) => {
  const {
    page,
    size,
    status,
    organizationName,
    creationStartDate,
    creationEndDate,
    maturityStartDate,
    maturityEndDate,
  } = params
  let url = `/lms/loan-reports/loans/export-to-excel`
  url += `?page=${page}&size=${size}`
  url += status ? `&status=${status}` : ''
  url += organizationName ? `&organizationName=${organizationName}` : ''
  url += creationStartDate ? `&creationStartDate=${creationStartDate}` : ''
  url += creationEndDate ? `&creationEndDate=${creationEndDate}` : ''
  url += maturityStartDate ? `&maturityStartDate=${maturityStartDate}` : ''
  url += maturityEndDate ? `&maturityEndDate=${maturityEndDate}` : ''
  dispatch(setGeneratedLoanReportLoading(true))
  try {
    const res = await secureapi2.get(url, { responseType: 'arraybuffer' })
    dispatch(setGeneratedLoanReportSuccess(true))
    dispatch(setGeneratedLoanReportLoading(false))
    dispatch(
      setNotification({
        message: 'Loan report was successfully generated',
        type: 'success',
      })
    )

    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    downloadBlob(blob, 'Loan_Report.xlsx')

    dispatch(setGeneratedLoanReportLoading(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
    dispatch(setGeneratedLoanReportSuccess(false))
  }
}
/** functions to manage document uploads on validation checks **/
export const uploadDocument = async (
  data: ICustomerDocumentUpload,
  dispatch: Dispatch,
  requestId: string,
  type: string
) => {
  dispatch(setIsLoadingUploadDocument(true))
  try {
    const response = await secureapi2.put(
      `/lms/loan-requests/${requestId}/documents${type === 'super' ? '' : '/make'}`,
      data
    )
    dispatch(setIsLoadingUploadDocument(false))
    dispatch(
      setNotification({
        message: 'PaymentDetails document uploaded successfully',
        type: 'success',
      })
    )
    return response.data
  } catch (e) {
    const message = (e as Error).message
    dispatch(setIsLoadingUploadDocument(false))

    dispatch(
      setNotification({
        message,
        type: 'error',
      })
    )
  }
}
//cancel a  loan request
export const cancelLoanRequest = async (
  requestId: string,
  data: { comments: string },
  dispatch: Dispatch,
  type: string
) => {
  dispatch(setIsLoadingCancelRequest(true))
  try {
    await secureapi2.put(
      `/lms/loan-requests/${requestId}/cancel${type === 'super' ? '' : '/make'}`,
      data
    )
    const message =
      type === 'super'
        ? 'Loan request cancelled'
        : 'Loan request cancelled, awaiting approval'
    dispatch(setNotification({ message, type: 'success' }))
    dispatch(setIsLoadingCancelRequest(false))
  } catch (e) {
    dispatch(setNotification({ message: (e as Error).message, type: 'error' }))
    console.error('Error cancelling loan request: ', e)

    dispatch(setIsLoadingCancelRequest(false))
  }
}
export const acceptCancelLoanRequest = async ({
  requestId,
  approvalId,
  comments,
  dispatch,
}: {
  requestId: string
  approvalId: string
  comments: string
  dispatch: Dispatch
}) => {
  dispatch(setIsLoadingCancelRequest(true))
  try {
    await secureapi2.put(
      `/lms/loan-requests/${requestId}/cancel/accept/${approvalId}`,
      { comments }
    )
    dispatch(setApprovalActions(false))
    dispatch(
      setNotification({
        message: 'Cancel request has been approved successfully.',
        type: 'success',
      })
    )
    dispatch(setIsLoadingCancelRequest(false))
  } catch (e) {
    dispatch(setNotification({ message: (e as Error).message, type: 'error' }))
    console.error('Error cancelling loan request approval: ', e)

    dispatch(setIsLoadingCancelRequest(false))
  }
}
export const rejectCancelLoanRequest = async ({
  requestId,
  approvalId,
  comments,
  dispatch,
}: {
  requestId: string
  approvalId: string
  comments: string
  dispatch: Dispatch
}) => {
  dispatch(setIsLoadingCancelRequest(true))
  try {
    await secureapi2.put(
      `/lms/loan-requests/${requestId}/cancel/reject/${approvalId}`,
      { comments }
    )
    dispatch(setApprovalActions(false))
    dispatch(
      setNotification({
        message: 'Cancel request has been rejected successfully.',
        type: 'success',
      })
    )
    dispatch(setIsLoadingCancelRequest(false))
  } catch (e) {
    dispatch(setNotification({ message: (e as Error).message, type: 'error' }))
    console.error('Error cancelling loan request approval: ', e)

    dispatch(setIsLoadingCancelRequest(false))
  }
}
//update a loan request customer profile
export const updateCustomerProfile = async (
  data: {
    pepPipRemarks?: string
    pipPepCategoryId?: string
    pipPepDeclarationStatus?: string
    sourceOfFunds?: string
    sourceOfWealth?: string
    occupation?: string
    comments?: string
  },
  customerId: string,
  dispatch: Dispatch,
  type: string
) => {
  dispatch(setIsLoadingCustomerProfile(true))
  try {
    await secureapi2.patch(
      `/lms/customers/${customerId}/${type === 'super' ? '' : 'make'}`,
      data
    )
    dispatch(setIsLoadingCustomerProfile(false))
    dispatch(
      setNotification({
        message: 'Customer profile updated successfully',
        type: 'success',
      })
    )
  } catch (e) {
    dispatch(setNotification({ message: (e as Error).message, type: 'error' }))
    dispatch(setIsLoadingCustomerProfile(false))
  }
}

export const getRequestCheckReport = async (
  dispatch: Dispatch,
  params: string
) => {
  dispatch(setRequestCheckReportsLoading(true))

  try {
    const response = await secureapi2.get(
      `/lms/loan-reports/request-checks${params}`
    )
    dispatch(setRequestCheckReportsSuccess(true))
    dispatch(setRequestCheckReportsLoading(false))
    dispatch(setRequestCheckReportsResponse(response.data))
  } catch (e) {
    console.error('Error fetching request checks: ', e)
    dispatch(setRequestCheckReportsLoading(false))
    dispatch(setLoanRequestReportSuccess(false))
    dispatch(
      setNotification({
        message: (e as Error).message,
        type: 'error',
      })
    )
  }
}
export const generateRequestChecksReport = async (
  dispatch: Dispatch,
  params: string
) => {
  ///lms/loan-reports/requests/export-to-excel
  const url = `/lms/loan-reports/request-checks/export-to-excel${params}`
  dispatch(setRequestCheckReportsExportLoading(true))

  try {
    const res = await secureapi2.get(url, { responseType: 'arraybuffer' })
    const blob = new Blob([res.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    })
    dispatch(setRequestCheckReportsExportLoading(false))
    dispatch(setRequestCheckReportsExportSuccess(true))
    setNotification({
      message: 'Request checks report generated successfully',
      type: 'success',
    })
    downloadBlob(blob, 'Request_Checks_Report.xlsx')
  } catch (e) {
    dispatch(setRequestCheckReportsExportLoading(false))
    dispatch(setRequestCheckReportsExportSuccess(false))
    dispatch(setNotification({ message: (e as Error).message, type: 'error' }))
  }
}

//*************************Broker management apis ************************************

// list all brokers
export const getBrokers = async (dispatch: Dispatch, params: string) => {
  try {
    dispatch(setIsLoadingBrokers(true))
    const response = await secureapi2.get(`/lms/brokers?${params || ''}`)

    dispatch(setIsLoadingBrokers(false))
    const { data, ...rest } = response.data

    dispatch(setBrokers(data))
    dispatch(setBrokersSummary(rest as ILoanRequestsSummary))
    // return response.data
  } catch (e) {
    console.error('Error fetching brokers: ', e)
    dispatch(setIsLoadingBrokers(false))
  }
}

// Create a new Broker
export const createBroker = async (
  dispatch: Dispatch,
  data: IBrokerDetails,
  type: string
) => {
  dispatch(setLoadingCreateBroker(true))
  try {
    const response = await secureapi2.post(
      `/lms/brokers/${type === 'make' ? 'make' : ''}`,
      data
    )
    dispatch(setLoadingCreateBroker(false))

    dispatch(setCreateBrokerSuccess(true))
    dispatch(setCreatedBroker(response.data))
    dispatch(
      setNotification({
        message: 'Broker created successfully.',
        type: 'success',
      })
    )
    // return response.data
  } catch (e) {
    dispatch(setCreateBrokerSuccess(false))
    dispatch(setCreateBrokerFailure(true))
    dispatch(setLoadingCreateBroker(false))
    const message = (e as Error).message
    dispatch(setNotification({ message, type: 'error' }))
  }
}

// update broker

export const updateBroker = async (
  brokerID: string,
  dispatch: Dispatch,
  data: IBrokerDetails,
  type: string
) => {
  const url = `lms/brokers/${brokerID}/${type === 'make' ? 'make' : ''}`
  const messageBase =
    type === 'make'
      ? 'Broker Details update successful pending approval'
      : type === 'deactivate'
        ? 'Broker deactivated successfully.'
        : type === 'reactivate'
          ? 'Broker re-activated successfully.'
          : 'Broker activated successfully.'

  const updateStatus = (loading: boolean, success: boolean, error: boolean) => {
    dispatch(setLoadingUpdateBroker(loading))
    dispatch(setUpdateBrokerSuccess(success))
    dispatch(setUpdateBrokerFailure(error))
  }

  try {
    updateStatus(true, false, false)
    await secureapi2.put(url, data)
    updateStatus(false, true, false)
    dispatch(
      setNotification({
        message: messageBase,
        type: 'success',
      })
    )
  } catch (error) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
  }
}
// Generate broker secret

export const generateBrokerSecret = async (
  brokerID: string,
  dispatch: Dispatch,
  data: IGenerateBrokerCreds
) => {
  const url = `/lms/brokers/${brokerID}/generate-secret`
  const updateStatus = (
    loading: boolean,
    success: boolean,
    failure: boolean
  ) => {
    dispatch(setIsGenerateBrokerSecretLoading(loading))
    dispatch(setGenerateBrokerSecretSuccess(success))
    dispatch(setGenerateBrokerSecretFailure(failure))
  }

  try {
    updateStatus(true, false, false)
    const res = await secureapi2.put(url, data)
    updateStatus(false, true, false)
    dispatch(
      setNotification({
        message:
          'API credentials have been generated successfully and sent to the email address.',
        type: 'success',
      })
    )
    return res.data
  } catch (err) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({ message: (err as Error).message, type: 'error' })
    )
  }
}

export const createBrokerAndAssignProducts = async (
  dispatch: Dispatch,
  brokerData: IBrokerCreate,
  selectedProducts: ILoanProduct[],
  type: string
) => {
  dispatch(setLoadingCreateBroker(true))
  const messageBase = `Broker created successfully ${type === 'super' ? '' : 'pending approval'}`

  try {
    const createdBroker = await secureapi2.post(
      `/lms/brokers/${type === 'maker' ? 'make' : ''}`,
      brokerData
    )

    dispatch(setCreateBrokerSuccess(true))
    dispatch(setCreatedBroker(createdBroker.data))
    dispatch(
      setNotification({
        message: messageBase,
        type: 'success',
      })
    )

    for (const product of selectedProducts) {
      const productPayload = {
        productId: product.id,
      }
      dispatch(setAssignBrokerProductLoading(true))
      await secureapi2.post(
        `/lms/brokers/${createdBroker.data.id}/products`,
        productPayload
      )
    }
    dispatch(setAssignBrokerProductLoading(false))
    dispatch(setAssignBrokerProductSuccess(true))
    dispatch(
      setNotification({
        message: 'Product assignment completed successfully.',
        type: 'success',
      })
    )

    return createdBroker.data
  } catch (e) {
    dispatch(setCreateBrokerFailure(true))
    dispatch(setAssignBrokerProductFailure(true))
    dispatch(setNotification({ message: (e as Error).message, type: 'error' }))
    dispatch(setLoadingCreateBroker(false))
  }
}

export const assignProductToBroker = async (
  brokerID: string,
  selectedProducts: ILoanProduct[],
  dispatch: Dispatch
) => {
  const updateStatus = (loading: boolean, success: boolean, error: boolean) => {
    dispatch(setLoadingUpdateBroker(loading))
    dispatch(setUpdateBrokerSuccess(success))
    dispatch(setUpdateBrokerFailure(error))
  }

  try {
    updateStatus(true, false, false)

    for (const product of selectedProducts) {
      const productPayload = {
        productId: product.id,
      }
      await secureapi2.post(`/lms/brokers/${brokerID}/products`, productPayload)
    }
    updateStatus(false, true, false)
    dispatch(
      setNotification({
        message: 'Broker Product(s) Assigned successfully',
        type: 'success',
      })
    )
  } catch (err) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({ message: (err as Error).message, type: 'error' })
    )
  }
}

export const getBrokerProducts = async (
  brokerID: string,
  dispatch: Dispatch
) => {
  const url = `/lms/brokers/${brokerID}/products`
  const updateStatus = (loading: boolean, success: boolean, error: boolean) => {
    dispatch(setLoadingUpdateBroker(loading))
    dispatch(setUpdateBrokerSuccess(success))
    dispatch(setUpdateBrokerFailure(error))
  }
  try {
    const res = await secureapi2.get(url)
    dispatch(setBrokerProducts(res.data))
    updateStatus(false, true, false)
  } catch (error) {
    dispatch(
      setNotification({ message: (error as Error).message, type: 'error' })
    )
    updateStatus(false, false, true)
  }
}

export const removeProductFromBroker = async (
  brokerID: string,
  productID: string,
  dispatch: Dispatch
) => {
  const url = `/lms/brokers/${brokerID}/products/${productID}`
  try {
    await secureapi2.delete(url)
    dispatch(
      setNotification({
        message: 'Product deleted successfully',
        type: 'success',
      })
    )
  } catch (error) {
    dispatch(
      setNotification({
        message: (error as Error).message,
        type: 'error',
      })
    )
  }
}

// Broker maker checker
export const approveUpdateBroker = async (
  brokerID: string,
  approvalID: string,
  dispatch: Dispatch,
  type: string,
  comments: string
) => {
  const url = `/lms/brokers/${brokerID}/${type === 'accept' ? 'approve' : 'reject'}${approvalID}`
  const updateStatus = (loading: boolean, success: boolean, error: boolean) => {
    dispatch(setLoadingUpdateBroker(loading))
    dispatch(setUpdateBrokerSuccess(success))
    dispatch(setUpdateBrokerFailure(error))
  }
  const messageBase = `Update broker request ${type === 'accept' ? 'completed' : 'rejected'} successfully`
  try {
    updateStatus(true, false, false)
    secureapi2.put(url, { comments })
    updateStatus(false, true, false)
    dispatch(
      setNotification({
        message: messageBase,
        type: 'success',
      })
    )
  } catch (err) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({
        message: (err as Error).message,
        type: 'error',
      })
    )
  }
}

export const approveMakeCreateBroker = async (
  approvalId: string,
  dispatch: Dispatch,
  type: string,
  comments: string
) => {
  const url = `lms/brokers/${type === 'accept' ? 'approve' : 'reject'}/${approvalId}`
  const messageBase = `Create broker request ${type === 'accept' ? 'completed' : 'rejected'} successfully`

  const updateStatus = (
    loading: boolean,
    success: boolean,
    failure: boolean
  ) => {
    dispatch(setLoadingUpdateBroker(loading))
    dispatch(setUpdateBrokerSuccess(success))
    dispatch(setUpdateBrokerFailure(failure))
  }
  try {
    updateStatus(true, false, false)
    await secureapi2.put(url, { comments })
    updateStatus(false, true, false)
    dispatch(
      setNotification({
        message: messageBase,
        type: 'success',
      })
    )
  } catch (error) {
    updateStatus(false, false, true)
    dispatch(
      setNotification({
        message: (error as Error).message,
        type: 'error',
      })
    )
  }
}
export const getExistingLoansByCustomerNo = async (
  customerNo: string,
  _dispatch: Dispatch
) => {
  try {
    // dispatch()
    const response = await secureapi2.get(
      `lms/customers/term-loan/${customerNo}`
    )
  } catch (e) {
    console.error('ERROR GETTING EXISTING CUSTOMER LOANS BY CUSTOMER NO: ', e)
  }
}

export const fetchDropdownData = async (
  endpoint: string
): Promise<{ id: number; name: string }[]> => {
  try {
    const res = await secureapi2.get(endpoint)
    return res.data.map(
      (item: { id: number; value: string; description: string }) => ({
        id: item.id,
        name: item.value,
        description: item.description,
      })
    )
  } catch (error) {
    console.error('Error fetching dropdown data: ', error)
    return []
  }
}

export const fetchPepPipCategories = async () => {
  try {
    const res = await secureapi2.get('/lms/customers/pep-pip-categories')
    return res.data
  } catch (error) {
    console.error('Error fetching PEP Pip categories: ', error)
    return []
  }
}
