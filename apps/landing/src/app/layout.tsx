'use client'
import { ReactNode } from 'react'
import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import { ReduxProvider } from '@dtbx/store'
import { CustomScrollbar, InActivity } from '@dtbx/ui/components'
import { isLoggedIn } from '@dtbx/store/utils'

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <ReduxProvider>
          <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
            <ThemeConfig themeType='main'>
              <CustomScrollbar>
                <InActivity isLoggedIn={isLoggedIn}>{children}</InActivity>
              </CustomScrollbar>
            </ThemeConfig>
          </NextAppDirEmotionCacheProvider>
        </ReduxProvider>
      </body>
    </html>
  )
}
