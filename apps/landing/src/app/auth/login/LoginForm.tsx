'use client'
import Image from 'next/image'
import { Box, Button, Stack, Typography } from '@mui/material'
import { LoadingButton } from '@dtbx/ui/components/Loading'
import { useAppDispatch, useAppSelector } from '@dtbx/store'
import { useEffect, useState } from 'react'
import { setIsLoginError, setLoginErrorMessage } from '@dtbx/store/reducers'
import { clearStore } from '@dtbx/store/utils'

export const LoginForm = ({
  serverSessionKey,
}: {
  serverSessionKey: string
}) => {
  const dispatch = useAppDispatch()
  const [clientSessionKey, setClientSessionKey] = useState<string>('')
  const { isLoadingLogin, isLoginError, loginErrorMessage } = useAppSelector(
    (state) => state.auth
  )
  const redirectUrl = process.env.NEXT_PUBLIC_CLIENT_REDIRECT_URL as string
  useEffect(() => {
    sessionStorage.setItem('tokenSecret', serverSessionKey)
    setClientSessionKey(serverSessionKey)
  }, [serverSessionKey])

  useEffect(() => {
    clearStore()
  }, [])

  useEffect(() => {
    if (isLoginError) {
      setTimeout(() => {
        dispatch(setIsLoginError(false))
        dispatch(setLoginErrorMessage(''))
      }, 1000 * 20)
    }
  }, [])
  return (
    <>
      <Box
        sx={{
          backgroundImage: 'url("bg-login-pattern.svg")',
          backgroundSize: '816px 716px',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'absolute',
          backgroundPositionX: '59px',
          backgroundPositionY: '0px',
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: '20px',
          }}
        >
          <form
            method="POST"
            action={`${process.env.NEXT_PUBLIC_OPEN_API_BASE_URL}/login/authenticate`}
          >
            <input type="hidden" name="tokenSecret" value={clientSessionKey} />
            <input type="hidden" name="redirectUrl" value={redirectUrl} />
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '6px',
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  fontWeight: '300',
                }}
              >
                Hi there.
              </Typography>
              <Typography
                variant="h3"
                sx={{
                  fontWeight: '400',
                }}
              >
                Welcome to DTBx Back Office Portal
              </Typography>
            </Box>
            {isLoadingLogin ? (
              <LoadingButton />
            ) : (
              <Button
                type="submit"
                variant="outlined"
                sx={{
                  width: '100%',
                }}
              >
                <Image src="ms-logo.svg" alt="ms-logo" width={20} height={20} />
                <Typography
                  variant="subtitle2"
                  sx={{
                    textTransform: 'none',
                  }}
                >
                  Sign in with Microsoft
                </Typography>
              </Button>
            )}
            {isLoginError && (
              <Stack
                sx={{
                  px: '10vw',
                }}
              >
                <Typography color="error" sx={{ textAlign: 'center' }}>
                  {loginErrorMessage
                    ? loginErrorMessage
                    : 'Your access has been temporarily disabled due to 30 days of inactivity. Please reach out to the system administrator to request reactivation.'}
                </Typography>
              </Stack>
            )}
          </form>
        </Box>
      </Box>
    </>
  )
}
