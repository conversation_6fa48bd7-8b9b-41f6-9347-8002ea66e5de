import { Box } from '@mui/material'
import { Logo } from '@dtbx/ui/components'

import { LoginForm } from '@/app/auth/login/LoginForm'
import crypto from 'crypto'

export default function LoginPage() {
  const serverSessionKey = crypto.randomBytes(32).toString('hex')

  return (
    <Box
      sx={{
        display: 'flex',
        height: '100vh',
        width: '100%',
      }}
    >
      <Box
        sx={{
          width: '50%',
          background:
            'linear-gradient(0deg, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.40) 100%), url("landingBG.svg"), lightgray',
          backgroundPosition: 'left',
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          paddingLeft: '35px',
          paddingTop: '22px',
        }}
      >
        <Logo />
      </Box>
      <Box
        sx={{
          width: '50%',
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <LoginForm serverSessionKey={serverSessionKey} />
      </Box>
    </Box>
  )
}
