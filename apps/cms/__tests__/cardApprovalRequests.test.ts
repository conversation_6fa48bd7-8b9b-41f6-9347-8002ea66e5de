import { describe, it, expect, vi } from 'vitest'
import * as apiFunctions from '@/store/actions'
import { secureapi } from '@dtbx/store/utils'
import {
  setIsLoadingApprovals,
  setCardApprovalRequests,
  setCardApprovalsPagination,
} from '@/store/reducers'
import { ICardApprovalRequest } from '@/store/interfaces'

vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
  },
}))

const mockData: ICardApprovalRequest[] = [
  {
    id: '1',

    status: 'PENDING',
    maker: '<PERSON>',
    dateCreated: '2022-01-01T12:00:00.000Z',
    dateModified: '2022-01-01T12:00:00.000Z',
    makerCheckerType: {
      channel: 'CARDS',
      checkerPermissions: [],
      description: '',
      makerPermissions: [],
      module: '',
      name: '',
      overridePermissions: [],
      type: '',
    },
    diff: [{ field: 'field', oldValue: 'old value', newValue: 'new value' }],
  },
]

describe('getAllCardsApprovalRequests', () => {
  it('should fetch card approval requests with no parameters', async () => {
    const dispatch = vi.fn()
    const mockResponse = {
      data: {
        data: mockData,
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      },
    }
    vi.spyOn(secureapi, 'get').mockResolvedValue(mockResponse)

    await apiFunctions.getAllCardsApprovalRequests(dispatch, '')

    expect(secureapi.get).toHaveBeenCalledWith(
      '/backoffice-auth/maker-checker/approvals?channel=CARDS'
    )
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingApprovals(true))
    expect(dispatch).toHaveBeenCalledWith(
      setCardApprovalRequests(mockResponse.data.data)
    )
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingApprovals(false))
  })
  it('should fetch card approval requests with additional parameters', async () => {
    const dispatch = vi.fn()
    const params = 'pageNumber=2&pageSize=20'
    const mockResponse = {
      data: {
        data: mockData,
        pageNumber: 2,
        pageSize: 20,
        totalElements: 40,
        totalNumberOfPages: 2,
      },
    }
    vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

    await apiFunctions.getAllCardsApprovalRequests(dispatch, params)

    expect(secureapi.get).toHaveBeenCalledWith(
      '/backoffice-auth/maker-checker/approvals?channel=CARDS&pageNumber=2&pageSize=20'
    )
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingApprovals(true))
    expect(dispatch).toHaveBeenCalledWith(
      setCardApprovalRequests(mockResponse.data.data)
    )
  })
  it('should dispatch card approval requests data correctly', async () => {
    const dispatch = vi.fn()

    const mockResponse = {
      data: {
        data: mockData,
        pageNumber: 1,
        pageSize: 10,
        totalElements: 2,
        totalNumberOfPages: 1,
      },
    }
    vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

    await apiFunctions.getAllCardsApprovalRequests(dispatch, '')

    expect(dispatch).toHaveBeenCalledWith(setCardApprovalRequests(mockData))
    expect(dispatch).toHaveBeenCalledTimes(4) // Loading true, data, pagination, loading false
  })
  it('should dispatch pagination data correctly', async () => {
    const dispatch = vi.fn()
    const paginationData = {
      pageNumber: 3,
      pageSize: 15,
      totalElements: 45,
      totalNumberOfPages: 3,
    }
    const mockResponse = {
      data: {
        data: [{ id: '1' }],
        ...paginationData,
      },
    }
    vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

    await apiFunctions.getAllCardsApprovalRequests(
      dispatch,
      'pageNumber=3&pageSize=10'
    )

    expect(dispatch).toHaveBeenCalledWith(
      setCardApprovalsPagination(paginationData)
    )
    const paginationCallIndex = dispatch.mock.calls.findIndex(
      (call) => call[0].type === setCardApprovalsPagination.type
    )
    expect(paginationCallIndex).toBeGreaterThan(-1)
  })
  it('should handle empty response data', async () => {
    const dispatch = vi.fn()
    const mockResponse = {
      data: {
        data: [],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 0,
        totalNumberOfPages: 0,
      },
    }
    vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

    await apiFunctions.getAllCardsApprovalRequests(dispatch, '')

    expect(dispatch).toHaveBeenCalledWith(setCardApprovalRequests([]))
    expect(dispatch).toHaveBeenCalledWith(
      setCardApprovalsPagination({
        pageNumber: 1,
        pageSize: 10,
        totalElements: 0,
        totalNumberOfPages: 0,
      })
    )
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingApprovals(false))
  })
})
