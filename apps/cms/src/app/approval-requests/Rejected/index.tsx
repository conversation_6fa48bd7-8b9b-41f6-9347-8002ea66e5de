import {
  Box,
  Button,
  Chip,
  ChipProps,
  Paper,
  Stack,
  styled,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import dayjs from 'dayjs'

import {
  CustomPagination,
  CustomTableCell,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { CustomSearchByInput } from '@dtbx/ui/components/Input'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { ICardApprovalRequest } from '@/store/interfaces/Approvals'
import { getAllCardsApprovalRequests } from '@/store/actions/ApprovalRequests'
import { LoadingListsSkeleton } from '@dtbx/ui/components'
import { ApprovalRequestRouting } from '@/app/approval-requests/RequestRouting'
import { CustomerStatusChip } from '@dtbx/ui/components/Chip'
import { getCardById } from '@/store/actions/CardsActions'
import { useDebounce } from '@dtbx/ui/hooks'
import { AllApprovalRequestsMoreMenu } from '../MoreMenu'
export const RequestChip = styled(Chip)<ChipProps>(() => ({
  padding: '2px 8px',
  borderRadius: '16px',
  background: '#F3F5F5',
  height: '24px',
  width: 'auto',
  minWidth: '0',
}))
const headerRow = [
  {
    id: 'requestType',
    label: 'Request Type',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'module',
    label: 'Module',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker',
    label: 'Maker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'maker_timestamp',
    label: 'Maker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checker',
    label: 'Checker',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'checker_timestamp',
    label: 'Checker Timestamp',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'status',
    label: 'Status',
    alignCenter: false,
    alignRight: false,
  },
  {
    id: 'action',
    label: 'Action',
    alignCenter: false,
    alignRight: false,
  },
]

export const searchByItems: {
  label: string
  value: Array<keyof ICardApprovalRequest>
}[] = [
  { label: 'Maker First Name', value: ['makerFirstName'] },
  { label: 'Maker Last Name', value: ['makerLastName'] },
]
export default function Rejected() {
  const { cardApprovalRequests, isLoadingApprovals, cardApprovalsPagination } =
    useAppSelector((state) => state.approvals)
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [paginationOptions, setPaginationOptions] = useState({
    page: cardApprovalsPagination?.pageNumber,
    size: cardApprovalsPagination?.pageSize,
    totalPages: cardApprovalsPagination?.totalNumberOfPages,
  })
  const [searchBy, setSearchBy] = useState<{
    label: string
    value: Array<keyof ICardApprovalRequest>
  }>(searchByItems[0])
  const [searchTerm, setSearchTerm] = useState<string>('')

  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    await getAllCardsApprovalRequests(
      dispatch,
      `page=${newOptions.page}&size=${newOptions.size}`
    )
  }
  const handleView = async (request: ICardApprovalRequest) => {
    if (request.status === 'PENDING') {
      await ApprovalRequestRouting(request, dispatch, router)
    } else {
      request.entityId && (await getCardById(dispatch, request.entityId))
      router.push('credit-cards/c-card')
    }
  }

  const debouncedSearchTerm = useDebounce(searchTerm, 500)
  useEffect(() => {
    if (!debouncedSearchTerm) {
      getAllCardsApprovalRequests(
        dispatch,
        `page=1&size=${paginationOptions.size}`
      )
    } else {
      getAllCardsApprovalRequests(
        dispatch,
        `${searchBy.value[0]}=${encodeURIComponent(debouncedSearchTerm)}`
      )
    }
  }, [debouncedSearchTerm, paginationOptions.size])
  return (
    <Stack sx={{ p: '2%', gap: '1.5vh' }}>
      <CustomSearchByInput
        searchByDropDownItems={searchByItems}
        onChange={async (value: string) => {
          setSearchTerm(value)
        }}
        value={searchTerm}
        onSearchBySelect={(value) => setSearchBy(value)}
        searchByValue={searchBy}
        onKeyDown={async () => {
          await getAllCardsApprovalRequests(dispatch, `page=1&size=10`)
        }}
        width={'100%'}
      />
      {isLoadingApprovals ? (
        <LoadingListsSkeleton />
      ) : (
        <Paper
          sx={{
            width: '100%',
            overflow: 'hidden',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FEFEFE',
            boxShadow:
              '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
          }}
          elevation={0}
        >
          <Stack sx={{ px: '1vw', py: '0.5vh' }}>
            <Typography variant="body2">All Approval Requests</Typography>
            <Typography variant="label2">
              Showing {cardApprovalRequests?.length} of{' '}
              {cardApprovalsPagination?.totalElements} records
            </Typography>
          </Stack>
          <TableContainer
            component={Paper}
            sx={{
              boxShadow: 'none',
            }}
          >
            <Table
              sx={{ minWidth: 650 }}
              aria-label="rejected approvals table"
              size="small"
            >
              <CustomTableHeader
                order={'asc'}
                orderBy={'id'}
                headLabel={headerRow}
                showCheckbox={false}
                rowCount={0}
                numSelected={0}
                onRequestSort={() => {}}
                onSelectAllClick={() => {}}
              />
              <TableBody>
                {cardApprovalRequests &&
                  cardApprovalRequests.map((row, index) => (
                    <TableRow key={index || row.id}>
                      <CustomTableCell sx={{ padding: '10px 24px 10px 16px' }}>
                        <Box>
                          <RequestChip
                            label={sentenceCase(row.makerCheckerType.name)}
                            sx={{ width: 'auto' }}
                          />
                        </Box>
                      </CustomTableCell>
                      <CustomTableCell>
                        {sentenceCase(row.makerCheckerType.module)}
                      </CustomTableCell>
                      <CustomTableCell>{row.maker}</CustomTableCell>
                      <CustomTableCell>
                        {dayjs(row.dateCreated).format('MMMM D, YYYY hh:mm A')}
                      </CustomTableCell>
                      <CustomTableCell>{row.checker || '-'}</CustomTableCell>
                      <CustomTableCell>
                        {dayjs(row.dateModified).format(
                          'MMMM D, YYYY hh:mm A'
                        ) || '-'}
                      </CustomTableCell>
                      <CustomTableCell>
                        <CustomerStatusChip label={row.status} />
                      </CustomTableCell>
                      <CustomTableCell>
                        <AllApprovalRequestsMoreMenu request={row} />
                      </CustomTableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
          <CustomPagination
            options={{
              ...paginationOptions,
              totalPages: cardApprovalsPagination?.totalNumberOfPages,
            }}
            handlePagination={handlePagination}
          />
        </Paper>
      )}
    </Stack>
  )
}
