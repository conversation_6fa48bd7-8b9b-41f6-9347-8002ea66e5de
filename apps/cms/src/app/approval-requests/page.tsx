'use client'
import React, { useEffect, useState } from 'react'
import { Divider, Stack } from '@mui/material'
import { useAppDispatch } from '@/store'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'

import Pending from './Pending'
import Rejected from './Rejected'
import { ICardApprovalRequest } from '@/store/interfaces/Approvals'
import { setSelectedCardApprovalRequest } from '@/store/reducers/approvals'
import { getAllCardsApprovalRequests } from '@/store/actions/ApprovalRequests'

export default function ApprovalsPage() {
  const [value, setValue] = useState<number>(0)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }
  const dispatch = useAppDispatch()
  useEffect(() => {
    dispatch(setSelectedCardApprovalRequest({} as ICardApprovalRequest))
    getAllCardsApprovalRequests(dispatch, `status=PENDING&page=1&size=10`)
  }, [])
  return (
    <Stack>
      <AntTabs
        sx={{
          marginLeft: '1%',
          borderBottomColor: 'secondary.main',
          '& .MuiTabs-indicator': {
            backgroundColor: 'secondary.main',
          },
        }}
        onChange={handleChange}
        value={value}
      >
        <AntTab label={`Pending Requests`} />
        <AntTab label={`All Requests`} />
      </AntTabs>
      <Divider />
      <TabPanel value={value} index={0}>
        <Pending />
      </TabPanel>
      <TabPanel value={value} index={1}>
        <Rejected />
      </TabPanel>
    </Stack>
  )
}
