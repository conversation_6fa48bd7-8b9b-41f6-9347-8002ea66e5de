'use client'
import React, { useEffect } from 'react'
import { Stack, Button, Box } from '@mui/material'
import PageHeader from './PageHeader'

import ArrowBackIcon from '@mui/icons-material/ArrowBack'

import { CardView } from '@/app/credit-cards/c-card/CardView'
import { useAppDispatch, useAppSelector } from '@/store'
import { getCardById } from '@/store/actions/CardsActions'

const CreditCardPage = () => {
  const dispatch = useAppDispatch()
  const { selectedCardToView } = useAppSelector((state) => state.cards)

  useEffect(() => {
    getCardById(dispatch, selectedCardToView.cardId)
  }, [])
  return (
    <Stack sx={{ gap: '1rem' }}>
      <PageHeader />
      <Box>
        <Button
          onClick={() => window.history.back()}
          sx={{
            border: '1px solid #D0D5DD',
            borderRadius: '4px',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            height: '2.5rem',
            color: '#555C61',
            fontSize: '15px',
            fontWeight: '500',
          }}
        >
          <ArrowBackIcon /> Back
        </Button>
      </Box>
      <CardView />
    </Stack>
  )
}

export default CreditCardPage
