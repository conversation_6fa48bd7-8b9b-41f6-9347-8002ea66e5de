'use client'
import React, { useState, useEffect } from 'react'
import { Box, Breadcrum<PERSON>, Link, Stack, Typography } from '@mui/material'
import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  CustomActiveChip,
  CustomerInfoChip,
  CustomErrorChip,
} from '@dtbx/ui/components/Chip'
import { useSearchParams } from 'next/navigation'
import {
  CardsChangesLogDrawer,
  CardsApprovalRequestDrawer,
} from '@/app/Drawers'
import { useAppSelector } from '@/store'

interface ICustomerCards {
  firstName: string
  lastName: string
}

const PageHeader = () => {
  const router = useCustomRouter()
  const { selectedCardToView } = useAppSelector((state) => state.cards)
  const { selectedCardApprovalRequest } = useAppSelector(
    (state) => state.approvals
  )
  const searchParams = useSearchParams()

  const origin = searchParams.get('origin')

  const [isDrawerOpen, setIsDrawerOpen] = useState(
    origin === 'cardsPage'
      ? false
      : Object.keys(selectedCardApprovalRequest).length > 0
  )
  useEffect(() => {
    if (origin !== 'cardsPage') {
      setIsDrawerOpen(
        Object.keys(selectedCardApprovalRequest).length > 0 &&
          selectedCardApprovalRequest.status === 'PENDING'
      )
    }
  }, [selectedCardApprovalRequest, origin])

  return (
    <Stack
      sx={{
        padding: '1% 2%',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderTop: '1px solid #D0D5DD',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          gap: '0.5rem',
          justifyContent: 'center',
          alignItems: 'center',
          alignContent: 'center',
        }}
      >
        <Breadcrumbs>
          <Link
            sx={{
              cursor: 'pointer',
              color: '#555C61',
            }}
            onClick={() => router.push('/credit-cards')}
          >
            Credit Cards
          </Link>
          <Typography sx={{ color: '#000' }}>
            {selectedCardToView && <>{selectedCardToView.customerName}</>}
          </Typography>
        </Breadcrumbs>
        {selectedCardToView.active ? (
          <CustomActiveChip label={'Active'} />
        ) : (
          <CustomErrorChip label={'Inactive'} />
        )}
        <Box
          onClick={() => setIsDrawerOpen(true)}
          sx={{
            cursor: 'pointer',
            display:
              Object.keys(selectedCardApprovalRequest).length > 0
                ? 'flex'
                : 'none',
          }}
        >
          <CustomerInfoChip
            label={`Pending approval :`}
            requests={['Activate credit card']}
          />
        </Box>
      </Stack>
      <Stack direction={'row'} spacing={1}>
        {Object.keys(selectedCardApprovalRequest).length > 0 && (
          <CardsApprovalRequestDrawer
            origin={origin ?? ''}
            open={isDrawerOpen}
            setOpen={setIsDrawerOpen}
            selectedCardApprovalRequest={selectedCardApprovalRequest}
          />
        )}
        <CardsChangesLogDrawer />
      </Stack>
    </Stack>
  )
}

export default PageHeader
