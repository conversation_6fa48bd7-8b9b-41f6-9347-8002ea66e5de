'use client'

import { <PERSON><PERSON><PERSON>rol<PERSON><PERSON><PERSON>, Stack, Switch, Typography } from '@mui/material'
import React from 'react'
import { CardHeaderIcon } from '@dtbx/ui/icons'
import { useAppDispatch, useAppSelector } from '@/store'
import { setIsBranchListView } from '@/store/reducers'

export default function CreditCardsLayout(props: {
  children: React.ReactNode
}) {
  const dispatch = useAppDispatch()
  const { isBranchListView } = useAppSelector((state) => state.cards)
  return (
    <Stack>
      <Stack direction="row" justifyContent="space-between">
        <Stack
          sx={{
            marginLeft: '2%',
            marginTop: '0.2%',
            flexDirection: 'row',
            justifyContent: 'flex-start',
            alignItems: 'center',
            gap: '8px',
            py: '8px',
          }}
        >
          <CardHeaderIcon width="30" height="28" />
          <Typography variant="h5">Credit Cards</Typography>
        </Stack>
        <FormControlLabel
          checked={isBranchListView}
          onChange={() => dispatch(setIsBranchListView(!isBranchListView))}
          value="end"
          control={<Switch color="primary" />}
          label={isBranchListView ? 'Branch View' : 'Ops View'}
          labelPlacement="end"
          sx={{ display: 'none' }}
        />
      </Stack>

      <Stack>{props.children}</Stack>
    </Stack>
  )
}
