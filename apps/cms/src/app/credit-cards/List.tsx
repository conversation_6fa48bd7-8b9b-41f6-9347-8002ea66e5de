import {
  Box,
  Button,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'

import { useAppDispatch, useAppSelector } from '@/store'
import { EmptyStateCardsView } from './c-card/EmptyStateCardsView'

import {
  CustomPagination,
  CustomTableCell,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import React, { useState, useEffect } from 'react'
import { CustomActiveChip, CustomErrorChip } from '@dtbx/ui/components/Chip'
import { sentenceCase } from 'tiny-case'
import { ICreditCard, IListCreditCard } from '@/store/interfaces/CardInterfaces'
import { CardsQueryParams, getCardById } from '@/store/actions/CardsActions'
import { CustomSearchByInput } from '@dtbx/ui/components/Input'
import { useCustomRouter, useDebounce } from '@dtbx/ui/hooks'
import { getAllCards } from '@/store/actions/CardsActions'
import { LoadingListsSkeleton } from '@dtbx/ui/components'
import { setSelectedCardApprovalRequest } from '@/store/reducers'
import { ICardApprovalRequest } from '@/store/interfaces'
const headerRow = [
  {
    id: 'customerName',
    label: 'Customer',
  },
  {
    id: 'active',
    label: 'Status',
  },
  {
    id: 'pan',
    label: 'Last Four PAN digits',
  },
  {
    id: 'domicileBranch',
    label: 'Domicile Branch',
  },
  {
    id: 'cif',
    label: 'CIF',
  },
  {
    id: 'email',
    label: 'Email',
  },
  {
    id: 'postalAddress',
    label: 'Postal Address',
  },
  {
    id: 'dateOfBirth',
    label: 'Date of Birth',
  },
  {
    id: '',
    label: 'Actions',
  },
]

export const searchByItems: {
  label: string
  value: Array<keyof ICreditCard>
}[] = [
  { label: 'Customer', value: ['customerName'] },
  { label: 'Domicile Branch', value: ['domicileBranch'] },
  { label: 'PAN Digits', value: ['pan'] },
  { label: 'CIF', value: ['cif'] },
  { label: 'Email ', value: ['email'] },
  { label: 'ID Number ', value: ['idNumber'] },
]
export const CreditCardList = () => {
  const {
    creditCardsList,
    creditCardResponse,
    isLoadingCards,
    selectedCardStatus,
  } = useAppSelector((state) => state.cards)
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
  })

  const totalPages = creditCardResponse.totalNumberOfPages
  const [searchBy, setSearchBy] = useState(searchByItems[0])
  const [searchTerm, setSearchTerm] = useState('')
  const debouncedSearchTerm = useDebounce(searchTerm, 500)
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    setHasMounted(true)
  }, [])

  useEffect(() => {
    if (!hasMounted) return
    setPaginationOptions((prev) => ({ ...prev, page: 1 }))
  }, [debouncedSearchTerm, searchBy])

  const handlePagination = (newOptions: PaginationOptions) => {
    setPaginationOptions({
      page: newOptions.page,
      size: newOptions.size,
    })
  }

  useEffect(() => {
    if (!hasMounted) return

    const params: CardsQueryParams = {
      page: paginationOptions.page,
      size: paginationOptions.size,
      cardType: 'CREDIT',
      active:
        selectedCardStatus === 'active' || selectedCardStatus === 'blocked',
      isBlocked: selectedCardStatus === 'blocked',
    }

    if (debouncedSearchTerm) {
      const componentKey = searchBy.value[0]
      const apiParam = componentKey === 'pan' ? 'pan' : componentKey
      // @ts-ignore
      params[apiParam as keyof CardsQueryParams] = debouncedSearchTerm
    }

    getAllCards(dispatch, params).then((r) => r)
  }, [
    selectedCardStatus,
    paginationOptions.page,
    paginationOptions.size,
    debouncedSearchTerm,
    searchBy,
  ])

  const handleView = async (card: IListCreditCard) => {
    await getCardById(dispatch, card.cardId)
    router.push('credit-cards/c-card?origin=cardsPage')
    dispatch(setSelectedCardApprovalRequest({} as ICardApprovalRequest))
  }

  return (
    <Stack sx={{ p: '2%', gap: '1.5vh' }}>
      <CustomSearchByInput
        searchByDropDownItems={searchByItems}
        onChange={(value: string) => {
          setSearchTerm(value)
        }}
        value={searchTerm}
        onSearchBySelect={(value) => setSearchBy(value)}
        searchByValue={searchBy}
        onKeyDown={(e) => {
          if (e.key === 'Enter')
            setPaginationOptions((prev) => ({ ...prev, page: 1 }))
        }}
        width={'100%'}
      />
      {isLoadingCards ? (
        <LoadingListsSkeleton />
      ) : creditCardsList?.length === 0 ? (
        <EmptyStateCardsView />
      ) : (
        <Paper
          sx={{
            width: '100%',
            overflow: 'hidden',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FEFEFE',
            boxShadow:
              '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
          }}
          elevation={0}
        >
          <Stack sx={{ px: '1vw', py: '0.5vh' }}>
            <Typography variant="body2">Credit Cards</Typography>
            <Typography variant="label2">
              Showing {creditCardsList?.length} of {creditCardsList?.length}{' '}
              records
            </Typography>
          </Stack>
          <TableContainer
            component={Paper}
            sx={{
              boxShadow: 'none',
            }}
          >
            <Table
              sx={{ minWidth: 650 }}
              aria-label="designations table"
              size="small"
            >
              <CustomTableHeader
                order={'asc'}
                orderBy={'id'}
                headLabel={headerRow}
                showCheckbox={false}
                rowCount={0}
                numSelected={0}
                onRequestSort={() => {}}
                onSelectAllClick={() => {}}
              />
              <TableBody>
                {creditCardsList.length > 0 ? (
                  creditCardsList.map((row: IListCreditCard) => {
                    const {
                      cardId,
                      customerName,
                      active,
                      pan,
                      domicileBranch,
                      cif,
                      email,
                      postalAddress,
                      dateOfBirth,
                    } = row
                    return (
                      <TableRow hover key={cardId} tabIndex={-1}>
                        <TableCell component="th" scope="row" id={cardId}>
                          {sentenceCase(customerName)}
                        </TableCell>
                        <CustomTableCell>
                          {active ? (
                            <CustomActiveChip label={'Active'} />
                          ) : (
                            <CustomErrorChip label={'Inactive'} />
                          )}
                        </CustomTableCell>
                        <TableCell>{pan}</TableCell>
                        <TableCell>{sentenceCase(domicileBranch)}</TableCell>
                        <TableCell>{cif}</TableCell>
                        <TableCell>{email}</TableCell>
                        <TableCell>{postalAddress || '-'}</TableCell>
                        <TableCell>{dateOfBirth || '-'}</TableCell>
                        <TableCell>
                          <Button
                            variant="outlined"
                            sx={{
                              border: '1px solid #AAADB0',
                              borderRadius: '7px',
                              fontSize: '12px',
                              fontWeight: 400,
                              padding: '5px 8px',
                            }}
                            onClick={() => handleView(row)}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    )
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={headerRow.length} align="center">
                      No records found
                    </TableCell>
                  </TableRow>
                )}
                {/* EMPTY STATE */}
              </TableBody>
            </Table>
          </TableContainer>
          <CustomPagination
            options={{
              page: paginationOptions.page,
              size: paginationOptions.size,
              totalPages: totalPages,
            }}
            handlePagination={handlePagination}
          />
        </Paper>
      )}
    </Stack>
  )
}
