'use client'

import React, { useEffect, useState } from 'react'
import { <PERSON>, Divider, Stack, Typo<PERSON> } from '@mui/material'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'
import { CreditCardList } from '@/app/credit-cards/List'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  setCreditCardsList,
  setCurrentTabIndex,
  setSelectedCardStatus,
} from '@/store/reducers'
import { ICardStatus } from '@/store/interfaces/CardInterfaces'
import { CardsQueryParams } from '@/store/actions/CardsActions'
import { BranchViewList } from '@/app/credit-cards/BranchViewList'

const CreditCardsPage = () => {
  const dispatch = useAppDispatch()
  const statusMapping = [
    'inactive',
    'active',
    'blocked',
    'unblocked',
  ] as ICardStatus[]
  const {
    creditCardResponse,
    selectedCardStatus,
    isBranchListView,
    branchCardListPagination,
    currentTabIndex,
  } = useAppSelector((state) => state.cards)
  const [statusTotals, setStatusTotals] = useState<Record<ICardStatus, number>>(
    {
      inactive: 0,
      active: 0,
      blocked: 0,
      unblocked: 0,
    }
  )

  useEffect(() => {
    if (creditCardResponse && selectedCardStatus && !isBranchListView) {
      setStatusTotals((prev) => ({
        ...prev,
        [selectedCardStatus]: creditCardResponse.totalElements,
      }))
    }
  }, [creditCardResponse, selectedCardStatus, isBranchListView])

  const handleChange = async (
    _event: React.SyntheticEvent,
    newValue: number
  ) => {
    // setValue(newValue)
    dispatch(setCurrentTabIndex(newValue))
    const selectedStatus = statusMapping[newValue]
    dispatch(setSelectedCardStatus(selectedStatus))

    const params: CardsQueryParams = {
      cardType: 'CREDIT',
      page: 1,
      size: 10,
      active: undefined,
      isBlocked: undefined,
    }

    switch (selectedStatus) {
      case 'active':
        params.active = true
        params.isBlocked = false

        break
      case 'blocked':
        params.isBlocked = true

        break
      case 'inactive':
        params.active = false
        params.isBlocked = false

        break
    }
    dispatch(setCreditCardsList([]))
  }

  const tabs = [
    {
      label: 'Inactive',
      key: 'inactive',
      view: 'both',
    },
    {
      label: 'Active',
      key: 'active',
      view: 'ops',
    },
    {
      label: 'Blocked',
      key: 'blocked',
      view: 'ops',
    },
    // {
    //   label: 'Unblocked',
    //   key: 'unblocked',
    //   view: 'ops',
    // },
  ]
  return (
    <Stack>
      <AntTabs
        sx={{
          marginLeft: '1%',
          borderBottomColor: 'secondary.main',
          '& .MuiTabs-indicator': {
            backgroundColor: 'secondary.main',
          },
        }}
        onChange={handleChange}
        value={currentTabIndex}
      >
        {tabs
          .filter((toFilter) =>
            isBranchListView ? toFilter.view === 'both' : toFilter
          )
          .map((tab, index) => (
            <AntTab
              label={
                <Stack
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    gap: '8px',
                  }}
                >
                  <Typography>{tab.label}</Typography>
                  <Chip
                    label={
                      isBranchListView
                        ? branchCardListPagination?.totalElements || 0
                        : statusTotals[tab.key as ICardStatus] || 0
                    }
                    sx={{
                      minWidth: '39px',
                      height: '22px',
                      border: '1px solid #EAECF0',
                      backgroundColor: '#F9FAFC',
                      color: currentTabIndex === index ? '#EB0045' : '#2A3339',
                      fontSize: '12px',
                      fontWeight: '500',
                      lineHeight: '18px',
                    }}
                  />
                </Stack>
              }
            />
          ))}
      </AntTabs>
      <Divider />
      <TabPanel value={currentTabIndex} index={0} key="tab-inactive">
        {isBranchListView ? <BranchViewList /> : <CreditCardList />}
      </TabPanel>
      <TabPanel value={currentTabIndex} index={1} key="tab-active">
        {isBranchListView ? <BranchViewList /> : <CreditCardList />}
      </TabPanel>
      <TabPanel value={currentTabIndex} index={2} key="tab-blocked">
        {isBranchListView ? <BranchViewList /> : <CreditCardList />}
      </TabPanel>
      {/* <TabPanel value={currentTabIndex} index={3}>
        {isBranchListView ? <BranchViewList /> : <CreditCardList />}
      </TabPanel> */}
    </Stack>
  )
}

export default CreditCardsPage
