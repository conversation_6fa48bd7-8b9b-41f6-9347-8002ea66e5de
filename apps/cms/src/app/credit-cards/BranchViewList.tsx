import {
  Button,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  TextField,
  Typography,
} from '@mui/material'
import { LoadingListsSkeleton } from '@dtbx/ui/components'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { EmptyStateBranchView } from '@/app/credit-cards/EmptyStateBranchView'
import { setCreditCardsList } from '@/store/reducers'
import { Search } from '@mui/icons-material'
import { MuiTelInput } from 'mui-tel-input'
import {
  getBranchCardsByPhonePan,
  getCardById,
} from '@/store/actions/CardsActions'
import {
  CustomPagination,
  CustomTableCell,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { IListCreditCard } from '@/store/interfaces'
import { sentenceCase } from 'tiny-case'
import { CustomActiveChip, CustomErrorChip } from '@dtbx/ui/components/Chip'
import { useCustomRouter } from '@dtbx/ui/hooks'
const headerRow = [
  {
    id: 'customerName',
    label: 'Customer',
  },
  {
    id: 'active',
    label: 'Status',
  },
  {
    id: 'pan',
    label: 'Last Four PAN digits',
  },
  {
    id: 'domicileBranch',
    label: 'Domicile Branch',
  },
  {
    id: 'cif',
    label: 'CIF',
  },
  {
    id: '',
    label: 'Actions',
  },
]

export const BranchViewList = () => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const {
    isLoadingBranchCards,
    branchCardsList,
    isBranchListView,
    branchCardListPagination,
  } = useAppSelector((state) => state.cards)
  const [phone, setPhone] = useState<string>('')
  const [pan, setPan] = useState<string>('')
  useEffect(() => {
    isBranchListView && dispatch(setCreditCardsList([]))
  }, [isBranchListView])
  const handleSearch = async () => {
    const params = new URLSearchParams()
    pan && params.append('pan', String(pan))
    phone && params.append('phoneNumber', String(phone.replace(/[\s+]/g, '')))
    await getBranchCardsByPhonePan(dispatch, params.toString())
  }
  const handleView = async (card: IListCreditCard) => {
    await getCardById(dispatch, card.cardId)
    router.push('credit-cards/c-card?origin=cardsPage')
  }
  const handlePagination = async (newOptions: PaginationOptions) => {
    const params = new URLSearchParams()
    params.append('size', String(newOptions.size))
    params.append('page', String(newOptions.page))
    pan && params.append('pan', String(pan))
    phone && params.append('phoneNumber', String(phone.replace(/[\s+]/g, '')))
    await getBranchCardsByPhonePan(dispatch, params.toString())
  }
  return (
    <Stack sx={{ p: '2%', gap: '1.5vh' }}>
      <Stack>
        <Typography variant="body1">Activate credit card</Typography>
        <Typography variant="label2" sx={{ fontWeight: 400 }}>
          To activate a credit card, please input the following customer details
          then click on search.
        </Typography>
      </Stack>
      <Stack
        direction="row"
        sx={{
          width: '100%',
          height: '3.5vh',
          justifyContent: 'space-between',
          gap: '10px',
        }}
      >
        <Stack
          direction="row"
          sx={{
            border: '1px solid #EAECF0 ',
            borderRadius: '8px',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            px: '15px',
            py: '1.4rem',
            width: '80%',
            alignItems: 'center',
            gap: '5px',
          }}
        >
          <Typography sx={{ textWrap: 'nowrap' }}>
            Search by mobile number
          </Typography>
          <MuiTelInput
            value={phone}
            name="phoneNumber"
            defaultCountry="KE"
            onlyCountries={['KE', 'UG', 'TZ', 'BI']}
            onChange={(value) => setPhone(value as string)}
            size="small"
            fullWidth
          />
          <Typography sx={{ textWrap: 'nowrap' }}>
            and last four pan digits
          </Typography>
          <TextField
            size="small"
            fullWidth
            value={pan}
            onChange={(e) => {
              const value = e.target.value.replace(/\D/g, '')
              setPan(value.slice(0, 4))
            }}
            inputProps={{
              inputMode: 'numeric',
              pattern: '[0-9]*',
              maxLength: 4,
            }}
          />
        </Stack>
        <Button
          variant="contained"
          endIcon={<Search />}
          onClick={handleSearch}
          loading={isLoadingBranchCards}
          disabled={!phone || pan.length !== 4}
          sx={{ height: '6vh', width: '20%' }}
        >
          Search
        </Button>
      </Stack>
      {isLoadingBranchCards ? (
        <LoadingListsSkeleton />
      ) : !branchCardsList || branchCardsList?.length < 1 ? (
        <EmptyStateBranchView />
      ) : (
        <Paper
          sx={{
            width: '100%',
            overflow: 'hidden',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FEFEFE',
            boxShadow:
              '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
          }}
          elevation={0}
        >
          <Stack sx={{ px: '1vw', py: '0.5vh' }}>
            <Typography variant="body2">Credit Cards</Typography>
            <Typography variant="label2">
              Showing {branchCardListPagination.totalElements} matches
            </Typography>
          </Stack>
          <TableContainer
            component={Paper}
            sx={{
              boxShadow: 'none',
            }}
          >
            <Table
              sx={{ minWidth: 650 }}
              aria-label="designations table"
              size="small"
            >
              <CustomTableHeader
                order={'asc'}
                orderBy={'id'}
                headLabel={headerRow}
                showCheckbox={false}
                rowCount={0}
                numSelected={0}
                onRequestSort={() => {}}
                onSelectAllClick={() => {}}
              />
              <TableBody>
                {branchCardsList &&
                  branchCardsList.map((row: IListCreditCard) => {
                    const {
                      cardId,
                      customerName,
                      active,
                      pan,
                      domicileBranch,
                      cif,
                    } = row
                    return (
                      <TableRow hover key={cardId} tabIndex={-1}>
                        <TableCell component="th" scope="row" id={cardId}>
                          {sentenceCase(customerName)}
                        </TableCell>
                        <CustomTableCell>
                          {active ? (
                            <CustomActiveChip label={'Active'} />
                          ) : (
                            <CustomErrorChip label={'Inactive'} />
                          )}
                        </CustomTableCell>
                        <TableCell>{pan}</TableCell>
                        <TableCell>{sentenceCase(domicileBranch)}</TableCell>
                        <TableCell>{cif}</TableCell>
                        <TableCell>
                          <Button
                            variant="outlined"
                            sx={{
                              border: '1px solid #AAADB0',
                              borderRadius: '7px',
                              fontSize: '12px',
                              fontWeight: 400,
                              padding: '5px 8px',
                            }}
                            onClick={() => handleView(row)}
                          >
                            View
                          </Button>
                        </TableCell>
                      </TableRow>
                    )
                  })}
              </TableBody>
            </Table>
          </TableContainer>
          <CustomPagination
            options={{
              page: branchCardListPagination?.pageNumber,
              size: branchCardListPagination?.pageSize,
              totalPages: branchCardListPagination?.totalNumberOfPages,
            }}
            handlePagination={handlePagination}
          />
        </Paper>
      )}
    </Stack>
  )
}
