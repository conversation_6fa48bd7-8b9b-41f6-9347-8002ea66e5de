'use client'
import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DialogTitle,
  IconButton,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { IHeadCell } from '@dtbx/store/interfaces'
import { CustomSkeleton } from '@dtbx/ui/components'
import { CustomTableHeader } from '@dtbx/ui/components/Table'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { SearchRounded } from '@mui/icons-material'
import { useAppSelector } from '@/store'

export const CardsChangesLogDrawer = () => {
  const [open, setOpen] = useState(false)
  const { selectedCardApprovalRequest } = useAppSelector(
    (state) => state.approvals
  )
  const header: IHeadCell[] = [
    {
      id: 'event',
      label: 'Event Type',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'maker',
      label: 'Maker',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'makerTimestamp',
      label: 'Maker Timestamp',
      alignCenter: false,
      alignRight: false,
    },

    {
      id: 'checker',
      label: 'Checker',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'checkerTimestamp',
      label: 'Checker Timestamp',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'actions',
      label: 'Actions',
      alignCenter: false,
      alignRight: false,
    },
  ]
  interface ICardHistoryLog {
    id: string
    name: string
    maker: string
    makerTimestamp: string
    checker: string
    checkerTimestamp: string
    actions: string
  }

  const cardHistoryLogs: ICardHistoryLog[] = [
    {
      id: 'T001',
      name: 'Activate Card',
      maker: 'Derrick Karanja',
      makerTimestamp: '2022-01-01',
      checker: 'Jacob Luseno',
      checkerTimestamp: '2022-01-01',
      actions: 'view',
    },
  ]

  const handleClose = (e: React.SyntheticEvent, reason: string) => {
    if (reason === 'backdropClick') {
      return
    }
    setOpen(false)
  }

  const isLoading = false

  return (
    <>
      {' '}
      <Button
        variant="outlined"
        onClick={() => setOpen(!open)}
        sx={{
          textWrap: 'noWrap',
          border: '1px solid #D0D5DD',
          borderRadius: '4px',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          height: '2.5rem',
          color: '#555C61',
          fontSize: '15px',
          fontWeight: '500',
          display: selectedCardApprovalRequest ? 'none' : 'flex',
        }}
      >
        Changes Log
      </Button>
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '95%',
          },
        }}
        open={open}
        anchor={'right'}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: '95%',
          },
        }}
      >
        {' '}
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
            height: '3.5rem',
            display: 'flex',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
              px: '2%',
            }}
          >
            <DialogTitle
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                alignContent: 'center',
                py: '5px',
              }}
            >
              <Typography variant="subtitle2" color={'primary.main'}>
                Changes Log
              </Typography>
            </DialogTitle>
          </Stack>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Stack spacing={2} padding={{ xs: 1.5, sm: 1.5, md: '1.5rem 2.5rem' }}>
          <CustomSearchInput
            sx={{
              width: '30%',
              '&.Mui-focused': {
                width: '40%',
              },
              borderRadius: '4px',
              '& fieldset': {
                border: '1px solid #D0D5DD !important',
              },
            }}
            startAdornment={<SearchRounded />}
            placeholder="Search"
          />
          <Stack>
            <Paper
              elevation={0}
              sx={{
                border: '1px solid #EAECF0',
                boxShadow:
                  '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
              }}
            >
              <TableContainer
                component={Paper}
                elevation={0}
                sx={{
                  boxShadow: 'none',
                }}
              >
                {!isLoading ? (
                  cardHistoryLogs?.length > 0 ? (
                    <Table
                      sx={{ minWidth: 650 }}
                      aria-label="designations table"
                    >
                      <CustomTableHeader
                        order={'desc'}
                        orderBy={''}
                        rowCount={0}
                        headLabel={[...header]}
                        numSelected={0}
                      />
                      <TableBody>
                        {cardHistoryLogs &&
                          cardHistoryLogs.map((row) => {
                            return (
                              <TableRow key={row.id} hover>
                                <TableCell>{row.name}</TableCell>
                                <TableCell>{row.maker}</TableCell>
                                <TableCell>{row.makerTimestamp}</TableCell>
                                <TableCell>{row.checker || 'N/A'}</TableCell>
                                <TableCell>{row.checkerTimestamp}</TableCell>
                                <TableCell>
                                  {/* <ChangesLogMoreMenu
                                    onClick={() => handleRowClick(row)}
                                    title={''}
                                    description={''}
                                  /> */}{' '}
                                  <Button
                                    variant="outlined"
                                    sx={{
                                      border: '1px solid #D0D5DD',
                                      height: '2.5rem',
                                      boxShadow:
                                        '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                                      color: '#555C61',
                                      fontSize: '15px',
                                      fontWeight: '500',
                                    }}
                                  >
                                    view
                                  </Button>
                                </TableCell>
                              </TableRow>
                            )
                          })}
                      </TableBody>
                    </Table>
                  ) : (
                    // <EmptyPage />
                    'EMPTY PAGE'
                  )
                ) : (
                  <CustomSkeleton
                    variant="rectangular"
                    animation="wave"
                    sx={{
                      height: '70vh',
                      width: '100%',
                    }}
                  />
                )}
              </TableContainer>
            </Paper>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}
