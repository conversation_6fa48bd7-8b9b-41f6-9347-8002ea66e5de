import { IPagination } from '@/store/interfaces/Approvals'
export type ICardStatus = 'active' | 'inactive' | 'blocked' | 'unblocked'
export interface InitialState {
  isLoadingCards: boolean
  isLoadingSingleCard: boolean
  isLoadingActivateCard: boolean
  isLoadingSetPinCard: boolean
  isLoadingResetPinRetries: boolean
  cardsError: boolean
  cardsSuccess: boolean
  creditCardsList: IListCreditCard[]
  creditCardResponse: ICardResponse
  selectedCardStatus: ICardStatus
  selectedCardToView: ICreditCard
  isBranchListView: boolean
  isLoadingBranchCards: boolean
  branchCardsList: IListCreditCard[]
  branchCardListPagination: IPagination
  currentTabIndex: number
}
export interface IListCreditCard {
  cardId: string
  customerName: string
  active: boolean
  pan: string
  domicileBranch: string
  cif: string
  email: string
  postalAddress: string
  dateOfBirth: string
}
export interface ICreditCard {
  cardId: string
  customerName: string
  active: boolean
  pan: string
  phoneNumber: string
  cif: string
  domicileBranch: string
  cardName: string
  account: string
  cardType: string
  productName: string
  isPrimary: boolean
  isSupplementary: boolean
  isStaff: boolean
  email: string
  idNumber: string
  postalAddress: string
  dateOfBirth: string
}
export interface ICardResponse {
  pageNumber: number
  pageSize: number
  totalNumberOfPages: number
  totalElements: number
  data: IListCreditCard[]
}
