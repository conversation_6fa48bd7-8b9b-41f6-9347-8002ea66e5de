import * as React from 'react'
import IconButton from '@mui/material/IconButton'
import Menu from '@mui/material/Menu'
import MenuItem from '@mui/material/MenuItem'
import MoreVertIcon from '@mui/icons-material/MoreVert'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { IBroker } from '@/store/interfaces'
import { useAppDispatch } from '@/store'
import { setSelectedBroker } from '@/store/reducers'

import GenerateAPIForm from '../GenerateApiForm'
import BrokerStatusModal from '../BrokerStatusModal'

const ITEM_HEIGHT = 48

interface BrokerMoreMenuProps {
  row: IBroker
}

export default function BrokerMoreMenu({ row }: BrokerMoreMenuProps) {
  const router = useCustomRouter()
  const [apiDialogOpen, setApiDialogOpen] = React.useState(false)
  const [statusDialogOpen, setStatusDialogOpen] = React.useState(false)
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const dispatch = useAppDispatch()

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleMenuItemClick = (option: string) => {
    switch (option) {
      case 'View details':
        router.push('/settings/brokers/view/')
        dispatch(setSelectedBroker(row))
        break
      case 'Manage API Credentials':
        dispatch(setSelectedBroker(row))
        setApiDialogOpen(true)
        break
      case 'Deactivate broker':
      case 'Activate broker':
      case 'Re-activate broker':
        dispatch(setSelectedBroker(row))
        setStatusDialogOpen(true)
        break
      default:
        console.error('Unknown option:', option)
    }
    handleClose()
  }

  const handleApiDialogClose = () => {
    setApiDialogOpen(false)
  }

  const options = [
    'View details',
    'Manage API Credentials',
    row.status === 'Active'
      ? 'Deactivate broker'
      : row.status === 'Blocked'
        ? 'Activate broker'
        : 'Re-activate broker',
  ]

  return (
    <div>
      <IconButton
        aria-label="more"
        id="long-button"
        aria-controls={open ? 'long-menu' : undefined}
        aria-expanded={open ? 'true' : undefined}
        aria-haspopup="true"
        onClick={handleClick}
      >
        <MoreVertIcon />
      </IconButton>
      <Menu
        id="long-menu"
        MenuListProps={{
          'aria-labelledby': 'long-button',
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        slotProps={{
          paper: {
            style: {
              maxHeight: ITEM_HEIGHT * 4.5,
              width: '20ch',
            },
          },
        }}
      >
        {options.map((option) => (
          <MenuItem
            key={option}
            selected={option === 'View details'}
            onClick={() => handleMenuItemClick(option)}
          >
            {option}
          </MenuItem>
        ))}
      </Menu>
      <GenerateAPIForm
        open={apiDialogOpen}
        setOpen={setApiDialogOpen}
        email={row.email}
        onClose={handleApiDialogClose}
      />
      <BrokerStatusModal
        open={statusDialogOpen}
        setOpen={setStatusDialogOpen}
        broker={row}
      />
    </div>
  )
}
