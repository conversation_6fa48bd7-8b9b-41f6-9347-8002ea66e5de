'use client'
import React from 'react'
import { IconButton, Stack } from '@mui/material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'

import ManageProducts from './ManageProducts'
import BasicDetails from './BasicDetails'
import DetailsHeader from './pageHeader'
import { useAppSelector } from '@/store'

export type Status = 'Active' | 'Inactive' | 'Pending Approval' | 'Blocked'

const getValidBrokerStatus = (status: string | undefined): Status => {
  switch (status) {
    case 'Active':
    case 'Inactive':
    case 'Pending Approval':
    case 'Blocked':
      return status
    default:
      return 'Active'
  }
}
function Page() {
  const { selectedBroker } = useAppSelector((state) => state.loans)
  const { physicalAddress, town, country } =
    selectedBroker?.physicalAddress || {}

  return (
    <Stack
      sx={{
        px: '3%',
        background: '#F7F7F7',
        height: '100%',
        justifyContent: 'center',
        py: '3%',
        flexDirection: 'column',
        position: 'relative',
        // gap: '3vh',
      }}
    >
      <Stack
        direction="row"
        position="absolute"
        top="1.25rem"
        left="1rem"
        mb={2}
      >
        <IconButton
          sx={{
            background: '#FFFFFF',
            borderRadius: '8px',
            border: '1px solid #D0D5DD',
            width: '3vw',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          onClick={() => window.history.back()}
        >
          <ArrowBackIosNewOutlinedIcon />
        </IconButton>
      </Stack>
      <DetailsHeader
        Orgname={selectedBroker.name}
        Maker={'Maker:'}
        MakerEmail={'<EMAIL>'}
        Checker={'Checker:'}
        CheckerEmail={' <EMAIL>'}
        brokerStatus={getValidBrokerStatus(selectedBroker?.status)}
      />

      <BasicDetails
        details={{
          id: selectedBroker.id!,
          brokerName: selectedBroker.name,
          email: selectedBroker.email,
          phone: selectedBroker.mobile,
          bankName: selectedBroker.bankName,
          bankCode: selectedBroker.bankCode,
          swiftCode: selectedBroker.swiftCode,
          accountNumber: selectedBroker.bankAccountNumber,
          branchCode: selectedBroker.branchCode,
          branchName: selectedBroker.accountBranchName,
          address: physicalAddress + ',' + town + ',' + country,
          exposureLimit: '20,000,000',
          organization: selectedBroker.organization,
          callbackurl: selectedBroker.callBackUrl || '',
        }}
      />
      <ManageProducts />
    </Stack>
  )
}

export default Page
