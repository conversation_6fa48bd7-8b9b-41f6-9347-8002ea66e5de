'use client'

import React from 'react'
import { <PERSON><PERSON>, <PERSON>, IconButton, Stack, Typography } from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import { useCustomRouter } from '@dtbx/ui/hooks'

const SectionHeader = () => {
  const router = useCustomRouter()
  return (
    <Stack
      component="header"
      direction="row"
      sx={{
        flexWrap: 'wrap',
        gap: 2,
        justifyContent: 'space-between',
        alignItems: 'flex-end',
        px: 3,
        pt: 6,
        pb: 3,
        width: '100%',
        backgroundColor: 'white',
        borderTop: '1px solid',
        borderBottom: '1px solid',
        borderColor: 'grey.300',
      }}
    >
      <Stack sx={{ display: 'flex', flexDirection: 'column' }}>
        <Card
          sx={{
            display: 'flex',
            gap: 1,
            justifyContent: 'center',
            alignItems: 'center',
            p: 2,
            width: 36,
            height: 36,
            backgroundColor: 'white',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'grey.300',
            boxShadow: 1,
          }}
        >
          <IconButton
            sx={{
              background: '#FFFFFF',
              borderRadius: '8px',
              border: '1px solid #D0D5DD',
              boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            }}
            onClick={() => window.history.back()}
          >
            <ArrowBackIosNewOutlinedIcon />
          </IconButton>
        </Card>
        <Typography
          variant="h5"
          sx={{
            mt: 2,
            fontWeight: 'medium',
            color: '#000A12',
            fontSize: '24px',
          }}
        >
          Manage Brokers
        </Typography>
      </Stack>
      <Stack
        component="nav"
        sx={{ display: 'flex', gap: 3, alignItems: 'flex-end' }}
      >
        <Button
          onClick={() => router.push('/settings/brokers/create')}
          endIcon={<AddIcon sx={{ color: 'white' }} />}
          variant="contained"
          sx={{
            display: 'flex',
            gap: 1,
            justifyContent: 'center',
            alignItems: 'center',
            px: 3,
            py: 2,
            textTransform: 'none',
            backgroundColor: '#101828',
            borderRadius: '8px',
          }}
        >
          <Typography
            variant="body3"
            sx={{
              textWrap: 'nowrap',
              color: 'white',
              fontWeight: '700',
              fontSize: '14px',
            }}
          >
            Add New Broker
          </Typography>
        </Button>
      </Stack>
    </Stack>
  )
}

export default SectionHeader
