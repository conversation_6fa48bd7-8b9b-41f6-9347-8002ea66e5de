'use client'
import {
  Button,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import FilterListOutlinedIcon from '@mui/icons-material/FilterListOutlined'
import React, { useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { SearchRounded } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import {
  CustomActiveBrokerChip,
  CustomBlockedChip,
  CustomInactiveBrokerChip,
  CustomPendingApprovalChip,
} from '@dtbx/ui/components/Chip'
import { getBrokers } from '@/store/actions'

import BrokerMoreMenu from './create/BrokerMoreMenu'

const headerList = [
  { id: 'name', label: 'Brokers', alignRight: false },
  { id: 'status', label: 'Status', alignRight: false },
  { id: 'organization', label: 'Organization', alignRight: false },
  { id: 'totalDisbursements', label: 'Total Disbursements', alignRight: false },
  { id: 'bankAccount', label: 'Bank Account', alignRight: false },
  { id: 'contact', label: 'Contact', alignRight: false },
  { id: 'actions', label: '', alignRight: false },
]

export const BrokersList = () => {
  const dispatch = useAppDispatch()
  const { brokersSummary, brokers } = useAppSelector((state) => state.loans)
  const [selected, setSelected] = useState<readonly string[]>([])
  const [paginationOptions, setPaginationOptions] = useState({
    page: brokersSummary.pageNumber,
    size: 10,
    totalPages: brokersSummary.totalNumberOfPages,
  })

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    let newSelected: readonly string[] = []
    if (event.target.checked) {
      newSelected = brokers
        .map((Broker) => Broker.id)
        .filter((id): id is string => !!id)
    }
    setSelected(newSelected)
  }

  const handleSelectOne = (_event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: readonly string[] = []
    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
  }

  /********* start pagination handlers***********/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    const params = `page=${newOptions.page}&size=${newOptions.size}`
    await getBrokers(dispatch, params)
  }

  /************* end pagination handlers ********/

  return (
    <Stack
      sx={{
        border: '1px solid #EAECF0',
        background: '#F7F7F7',
        padding: '2%',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <Stack>
        <CustomSearchInput
          placeholder="Search broker by Name"
          endAdornment={
            <SearchRounded
              sx={{
                color: 'black',
              }}
            />
          }
          sx={{
            width: '33vw',
            background: '#FFFFFF',
            borderRadius: '8px',
            mb: '10px',
            border: '1px solid #D0D5DD',
          }}
        />
      </Stack>

      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          padding: '2%',
          background: '#FFFFFF',
          border: '1px solid #EAECF0',
          borderRadius: '8px',
        }}
      >
        <Stack>
          <Typography variant="subtitle1">Brokers</Typography>
          <Typography variant="subtitle3">
            Showing {brokers?.length} Brokers
          </Typography>
        </Stack>
        <Button variant="outlined" startIcon={<FilterListOutlinedIcon />}>
          Filter
        </Button>
      </Stack>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="designations table"
          size="small"
        >
          <CustomTableHeader
            order={'asc'}
            orderBy={'id'}
            headLabel={headerList}
            showCheckbox={true}
            rowCount={10}
            numSelected={0}
            onRequestSort={() => {}}
            onSelectAllClick={handleSelectAll}
          />
          <TableBody>
            {brokers &&
              brokers.map((row) => {
                const {
                  id,
                  limit,
                  bankAccountNumber,
                  bankName,
                  mobile,
                  name,
                  email,
                  status,
                  totalDisbursements,
                  organization,
                } = row
                const isItemSelected = selected.indexOf(id ? id : '') !== -1
                return (
                  <TableRow
                    hover
                    key={id}
                    tabIndex={-1}
                    role="checkbox"
                    onClick={(event) =>
                      handleSelectOne(event, row.id ? row.id : '')
                    }
                    selected={isItemSelected}
                    aria-checked={isItemSelected}
                  >
                    <TableCell padding="checkbox">
                      <CustomCheckBox
                        checked={isItemSelected}
                        inputProps={{
                          'aria-labelledby': id,
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Stack direction="column">
                        <Typography
                          variant="subtitle2"
                          sx={{ color: 'primary.main' }}
                        >
                          {name}
                        </Typography>
                        <Typography variant="subtitle3">ID: {id}</Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      {status === 'Active' ? (
                        <CustomActiveBrokerChip label={sentenceCase(status)} />
                      ) : status === 'Inactive' ? (
                        <CustomInactiveBrokerChip
                          label={sentenceCase(status)}
                        />
                      ) : status === 'Pending Approval' ? (
                        <CustomPendingApprovalChip
                          label={sentenceCase(status)}
                        />
                      ) : status === 'Blocked' ? (
                        <CustomBlockedChip label={sentenceCase(status)} />
                      ) : null}
                    </TableCell>

                    <TableCell>
                      <Typography variant="body2">
                        {organization?.name || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Stack>
                        <Typography variant="body2">
                          {totalDisbursements}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Stack direction="column">
                        <Typography variant="subtitle3" color={'primary.main'}>
                          {bankAccountNumber || 'N/A'}
                        </Typography>
                        <Typography variant="subtitle3">
                          {bankName || 'N/A'}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Stack direction="column">
                        <Typography variant="subtitle3" color={'primary.main'}>
                          {mobile || 'N/A'}
                        </Typography>
                        <Typography variant="body2">{email}</Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <BrokerMoreMenu row={row} />
                    </TableCell>
                  </TableRow>
                )
              })}
          </TableBody>
        </Table>
        {brokersSummary.totalNumberOfPages > 0 && (
          <CustomPagination
            options={{
              ...paginationOptions,
              totalPages: brokersSummary.totalNumberOfPages,
            }}
            handlePagination={handlePagination}
          />
        )}
      </TableContainer>
    </Stack>
  )
}
