'use client'
import React, { useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { LoadingListsSkeleton } from '@dtbx/ui/components/Loading'

import SectionHeader from './SectionHeader'
import { BrokersList } from './BrokersList'
import { getBrokers } from '@/store/actions'

function BrokersPage() {
  const { isLoadingBrokers } = useAppSelector((state) => state.loans)
  const dispatch = useAppDispatch()
  useEffect(() => {
    getBrokers(dispatch, `page=1&size=10`)
  }, [])

  return (
    <>
      <SectionHeader />
      {isLoadingBrokers ? <LoadingListsSkeleton /> : <BrokersList />}
    </>
  )
}

export default BrokersPage

