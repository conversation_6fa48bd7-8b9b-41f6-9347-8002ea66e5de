import React from 'react'
import { getBrokers, updateBroker } from '@/store/actions'
import { useAppDispatch } from '@/store'
import { IBrokerCreate, IBrokerDetails } from '@/store/interfaces'
import { Dialog } from '@dtbx/ui/components/Overlay/CustomDialog'

interface BrokerStatusModalProps {
  open: boolean
  broker: IBrokerCreate
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
}
const reasonsForDeactivating = [
  'Broker has violated terms of service',
  '<PERSON><PERSON><PERSON> has engaged in fraudulent activity',
  '<PERSON>roke<PERSON> has failed to meet performance standards',
  'Other',
]

const reasonsForActivating = [
  '<PERSON>roker has completed onboarding process',
  '<PERSON><PERSON><PERSON> has provided necessary documentation',
  'Administrative decision',
  'Other',
]

const reasonsForReactivating = [
  'Broker has resolved previous issues',
  '<PERSON><PERSON><PERSON> has completed necessary training',
  'Administrative decision',
  'Other',
]

const BrokerStatusModal: React.FC<BrokerStatusModalProps> = ({
  open,
  setOpen,
  broker,
}) => {
  const dispatch = useAppDispatch()

  const handleStatusUpdate = async (reasons: string[]) => {
    const { status, id } = broker
    const comments = reasons.join(', ')

    if (!id) {
      console.error('Broker ID is undefined.')
      return
    }

    try {
      const data: IBrokerDetails = {
        name: broker.name || '',
        status:
          status === 'Active'
            ? 'Inactive'
            : status === 'Blocked'
              ? 'Active'
              : 'Active',
        callBackUrl: broker.callBackUrl || '',
        comments: comments,
        accountBranchName: broker.accountBranchName || '',
        bankAccountNumber: broker.bankAccountNumber || '',
        bankCode: broker.bankCode || '',
        bankName: broker.bankName || '',
        branchCode: broker.branchCode || '',
        email: broker.email || '',
        mobile: broker.mobile || '',
        physicalAddress: broker.physicalAddress || undefined,
        product: [],
        swiftCode: broker.swiftCode || '',
        dateCreated: broker.dateCreated,
        dateModified: broker.dateModified,
        createdBy: broker.createdBy || null,
        modifiedBy: broker.modifiedBy || null,
      }

      if (status === 'Active') {
        await updateBroker(id, dispatch, data, 'deactivate')
      } else if (status === 'Inactive' || status === 'Blocked') {
        await updateBroker(id, dispatch, data, 'activate')
      }
      await getBrokers(dispatch, 'page=1&size=10')

      setOpen(false)
    } catch (error) {
      console.error('Error updating broker status:', error)
    }
  }

  return (
    <Dialog
      buttonText={
        broker.status === 'Active'
          ? 'Deactivate'
          : broker.status === 'Inactive'
            ? 'Re-activate'
            : 'Activate'
      }
      title={
        broker.status === 'Active'
          ? 'Deactivate Broker'
          : broker.status === 'Inactive'
            ? 'Re-activate Broker'
            : 'Activate Broker'
      }
      open={open}
      descriptionText={
        broker.status === 'Active'
          ? 'Are you sure you want to deactivate this broker? Deactivating a broker will prevent them from selling any products linked to their account.'
          : broker.status === 'Inactive'
            ? 'Are you sure you want to re-activate this broker? Re-activating a broker will allow them to sell any products linked to their account.'
            : 'Are you sure you want to activate this broker? Activating a broker will allow them to sell products linked to their account.'
      }
      setOpen={setOpen}
      buttonProps={{
        color: broker.status === 'Active' ? '#EB0045' : '#12B76A',
      }}
      onClick={(reason: string[]) => {
        handleStatusUpdate(reason)
      }}
      reasons={
        broker.status === 'Active'
          ? reasonsForDeactivating
          : broker.status === 'Inactive'
            ? reasonsForReactivating
            : reasonsForActivating
      }
    />
  )
}
export default BrokerStatusModal
