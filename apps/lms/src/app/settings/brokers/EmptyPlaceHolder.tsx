'use client'
import { Button, Stack, Typography } from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { EmptyFolder } from '@dtbx/ui/icons'

export const EmptyBrokerListPlaceHolder = () => {
  const router = useCustomRouter()
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        alignItems: 'center',
        alignContent: 'center',
        gap: '10px',
        paddingTop: '10%',
        height: '80vh',
        border: '1px solid #EAECF0',
        background: '#F7F7F7',
      }}
    >
      <EmptyFolder />
      <Typography
        sx={{
          fontWeight: 700,
        }}
      >
        There are no records to show.
      </Typography>
      <Typography variant="body2">
        Please create a broker to get started.
      </Typography>
      <Button
        onClick={() => router.push('//brokers/create')}
        startIcon={<AddIcon sx={{ color: '#000A12' }} />}
        variant="outlined"
        sx={{
          display: 'flex',
          gap: 1,
          justifyContent: 'center',
          alignItems: 'center',
          px: 3,
          py: 2,
          textTransform: 'none',
          backgroundColor: 'white',
          borderRadius: '8px',
          borderColor: '#D0D5DD',
        }}
      >
        <Typography
          variant="body3"
          sx={{
            textWrap: 'nowrap',
            color: '#000A12',
            fontWeight: '700',
            fontSize: '14px',
          }}
        >
          Create New Broker
        </Typography>
      </Button>
    </Stack>
  )
}
