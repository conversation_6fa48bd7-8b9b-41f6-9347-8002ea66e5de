'use client'
import {
  Autocomplete,
  Button,
  FormControl,
  FormHelperText,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import React, { useEffect, useState } from 'react'
import { Form, FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import { matchIsValidTel, MuiTelInput } from 'mui-tel-input'
import { IBrokerCreate, ILoanProduct } from '@/store/interfaces'
import { HasAccessToRights } from '@dtbx/store/utils'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  getBankBranches,
  getLoanProducts,
  removeProductFromBroker,
  updateBroker,
} from '@/store/actions'
import { MultiSelectAutocomplete } from '@dtbx/ui/components/Input'
import { LoadingButton } from '@dtbx/ui/components/Loading'

interface BrokerDetailsProps {
  brokerDetails: IBrokerCreate
}

//validation
const brokerValidation = Yup.object({
  mobile: Yup.string()
    .required('Phone must not be empty')
    .test('is-valid-phone', 'Invalid phone number', (value) => {
      return matchIsValidTel(value || '')
    }),
  name: Yup.string()
    .matches(/^[A-Za-z\s]+$/, 'Only alphabets are allowed')
    .required('Name must not be empty'),
  email: Yup.string()
    .email('Invalid email')
    .required('Email must not be empty'),
  bankName: Yup.string().required('Bank Name must not be empty'),
  bankAccountNumber: Yup.string().required(
    'Bank Account Number must not be empty'
  ),
  bankCode: Yup.string().required('Bank Code must not be empty'),
  swiftCode: Yup.string().required('Swift Code must not be empty'),
  branchCode: Yup.string().required('Branch Code must not be empty'),
  accountBranchName: Yup.string().required(
    'Account Branch Name must not be empty'
  ),
  physicalAddress: Yup.object().shape({
    country: Yup.string().required('Country is required'),
    town: Yup.string().required('Town is required'),
    physicalAddress: Yup.string().required(
      'Physical Address must not be empty'
    ),
  }),
  productIds: Yup.array()
    .of(Yup.string().required('Product is required'))
    .min(1, 'At least one product must be selected'),
  callBackUrl: Yup.string()
    .url('Value must be a valid URL (e.g., https://example.com)')
    .required('Callback URL must not be empty'),
  status: Yup.string().oneOf(['Active', 'Inactive', 'Blocked']),
})

const EditBrokerDetails: React.FC<BrokerDetailsProps> = ({ brokerDetails }) => {
  const router = useCustomRouter()
  const {
    isLoadingCreateBroker,
    bankBranches,
    selectedBroker,
    loanProducts,
    brokerProducts,
  } = useAppSelector((state) => state.loans)
  const dispatch = useAppDispatch()
  const [phone, setPhone] = useState<string>(selectedBroker.mobile || '')
  const [selectedProducts, setSelectedProducts] = useState<ILoanProduct[]>([])

  useEffect(() => {
    getBankBranches(dispatch)
    getLoanProducts(dispatch)

    if (brokerProducts && brokerProducts.length > 0) {
      setSelectedProducts(brokerProducts)
      formik.setFieldValue(
        'productIds',
        brokerProducts.map((p) => p.id)
      )
    }
    formik.validateForm()
  }, [])

  const formik = useFormik({
    initialValues: {
      ...selectedBroker,
      productIds: Array.isArray(brokerDetails.productIds)
        ? brokerDetails.productIds
        : [],
      status: brokerDetails.status || 'Active',
      callBackUrl: selectedBroker.callBackUrl || '',
    },
    validationSchema: brokerValidation,
    onSubmit: async (values) => {
      if (HasAccessToRights(['SUPER_UPDATE_BROKER'])) {
        await updateBroker(
          selectedBroker.id ? selectedBroker.id : '',
          dispatch,
          values,
          'super'
        )
      } else {
        await updateBroker(
          selectedBroker.id ? selectedBroker.id : '',
          dispatch,
          values,
          'make'
        )
      }
      router.push('/settings/brokers/')
    },
  })
  const handlePhoneChange = (value: string) => {
    setPhone(value)
    formik.setFieldTouched('mobile', true, false)
    if (!matchIsValidTel(value)) {
      formik.setFieldError('mobile', 'Invalid mobile number')
    }
    formik.setFieldValue('mobile', value.replace(/\s/g, ''))
  }
  const { handleSubmit, errors } = formik

  const handleProductChange = (
    _event: unknown,
    newSelectedProducts: ILoanProduct[]
  ) => {
    const selectedProductIds = newSelectedProducts.map((product) => product.id)
    setSelectedProducts(newSelectedProducts)
    formik.setFieldValue('productIds', selectedProductIds)
  }
  return (
    <Stack
      sx={{
        px: '2%',
        background: '#F7F7F7',
        minHeight: '92vh',
        justifyContent: 'center',
        py: '2%',
        flexDirection: 'column',
        gap: '3vh',
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        mb={2}
      >
        <IconButton
          sx={{
            background: '#FFFFFF',
            borderRadius: '8px',
            border: '1px solid #D0D5DD',
            width: '3vw',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          onClick={() => window.history.back()}
        >
          <ArrowBackIosNewOutlinedIcon />
        </IconButton>

        <Stack flexGrow={1}>
          <Typography variant="subtitle1" align="center" fontWeight="bold">
            Edit Details
          </Typography>
          <Typography variant="subtitle2" align="center" mt={2}>
            You are updating broker details
          </Typography>
        </Stack>
      </Stack>

      <Stack
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '80vh',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'column',
            width: '50vw',
            background: '#FFFFFF',
            padding: '2%',
            borderRadius: '12px',
            alignItems: 'center',
          }}
        >
          <FormikProvider value={formik}>
            <Form onSubmit={handleSubmit} noValidate>
              <Stack
                direction="row"
                justifyContent={'space-between'}
                gap={'2%'}
              >
                <TextField
                  autoComplete="name"
                  type="text"
                  label="Broker’s Name"
                  margin={'normal'}
                  {...formik.getFieldProps('name')}
                  fullWidth
                  error={Boolean(formik.touched.name && formik.errors.name)}
                  helperText={formik.touched.name && formik.errors.name}
                />
                <FormControl fullWidth margin="normal">
                  <InputLabel id="status-label">Status</InputLabel>
                  <Select
                    labelId="status-label"
                    id="status"
                    {...formik.getFieldProps('status')}
                    value={formik.values.status || 'Active'}
                    label="Status"
                  >
                    <MenuItem value="Active">Active</MenuItem>
                    <MenuItem value="Inactive">Inactive</MenuItem>
                    <MenuItem value="Blocked">Blocked</MenuItem>
                  </Select>
                  {formik.touched.status && formik.errors.status && (
                    <FormHelperText error>
                      {formik.errors.status}
                    </FormHelperText>
                  )}
                </FormControl>
              </Stack>
              <TextField
                autoComplete="email"
                type="email"
                label="Broker’s Email address"
                margin={'normal'}
                {...formik.getFieldProps('email')}
                fullWidth
                error={Boolean(formik.touched.email && formik.errors.email)}
                helperText={formik.touched.email && formik.errors.email}
              />
              <FormControl
                fullWidth
                sx={{
                  marginTop: '16px',
                }}
                error={Boolean(formik.touched.mobile && formik.errors.mobile)}
              >
                <MuiTelInput
                  value={phone}
                  name="mobile"
                  label="Broker’s Phone number"
                  defaultCountry="KE"
                  onlyCountries={['KE', 'UG', 'TZ', 'BI']}
                  onChange={handlePhoneChange}
                />
                <FormHelperText>
                  {formik.touched.mobile && formik.errors.mobile && (
                    <FormHelperText error>
                      {formik.errors.mobile}
                    </FormHelperText>
                  )}
                </FormHelperText>
              </FormControl>
              <FormControl fullWidth margin="normal">
                <TextField
                  id="bankName"
                  autoComplete="bankName"
                  label="Bank Name"
                  {...formik.getFieldProps('bankName')}
                  // value={formik.values.bankName || ''}
                  error={
                    formik.touched.bankName && Boolean(formik.errors.bankName)
                  }
                  helperText={formik.touched.bankName && formik.errors.bankName}
                />
              </FormControl>

              <Stack
                direction="row"
                justifyContent={'space-between'}
                gap={'2%'}
              >
                <TextField
                  autoComplete="bankCode"
                  type="text"
                  label="Bank Code"
                  margin={'normal'}
                  {...formik.getFieldProps('bankCode')}
                  fullWidth
                  //   inputProps={{ readOnly: true }}
                  error={Boolean(
                    formik.touched.bankCode && formik.errors.bankCode
                  )}
                  helperText={formik.touched.bankCode && formik.errors.bankCode}
                />
                <TextField
                  autoComplete="swiftCode"
                  type="text"
                  label="Swift Code"
                  margin={'normal'}
                  {...formik.getFieldProps('swiftCode')}
                  fullWidth
                  //   inputProps={{ readOnly: true }}
                  error={Boolean(
                    formik.touched.swiftCode && formik.errors.swiftCode
                  )}
                  helperText={
                    formik.touched.swiftCode && formik.errors.swiftCode
                  }
                />
              </Stack>
              <TextField
                autoComplete="bankAccountNumber"
                type="text"
                label="Bank Account Number"
                margin={'normal'}
                {...formik.getFieldProps('bankAccountNumber')}
                fullWidth
                error={Boolean(
                  formik.touched.bankAccountNumber &&
                    formik.errors.bankAccountNumber
                )}
                helperText={
                  formik.touched.bankAccountNumber &&
                  formik.errors.bankAccountNumber
                }
              />
              <Stack
                direction="row"
                justifyContent={'space-between'}
                gap={'2%'}
              >
                <TextField
                  autoComplete="branchCode"
                  type="text"
                  label="Branch Code"
                  margin={'normal'}
                  {...formik.getFieldProps('branchCode')}
                  fullWidth
                  //   inputProps={{ readOnly: true }}
                  error={Boolean(
                    formik.touched.branchCode && formik.errors.branchCode
                  )}
                  helperText={
                    formik.touched.branchCode && formik.errors.branchCode
                  }
                />
                <FormControl fullWidth margin="normal">
                  <Autocomplete
                    disablePortal
                    id="bankBranches"
                    options={bankBranches}
                    getOptionLabel={(option) => option.branchName}
                    value={bankBranches.find(
                      (branch) => formik.values.branchCode === branch.branchCode
                    )}
                    onChange={(_event, newValue) => {
                      formik.setFieldValue(
                        'accountBranchName',
                        newValue?.branchName
                      )
                      formik.setFieldValue('branchCode', newValue?.branchCode)
                    }}
                    renderInput={(params) => (
                      <TextField {...params} label={'Account Branch Name'} />
                    )}
                  />
                  {formik.touched.accountBranchName &&
                    formik.errors.accountBranchName && (
                      <FormHelperText error>
                        {formik.errors.accountBranchName}
                      </FormHelperText>
                    )}
                </FormControl>
              </Stack>
              <Stack
                direction="row"
                justifyContent={'space-between'}
                gap={'2%'}
              >
                <TextField
                  autoComplete="country"
                  type="text"
                  label="Country"
                  margin="normal"
                  {...formik.getFieldProps('physicalAddress.country')}
                  fullWidth
                  error={Boolean(
                    formik.touched.physicalAddress?.country &&
                      formik.errors.physicalAddress?.country
                  )}
                  helperText={
                    formik.touched.physicalAddress?.country &&
                    formik.errors.physicalAddress?.country
                  }
                />
                <TextField
                  autoComplete="town"
                  type="text"
                  label="Town"
                  margin="normal"
                  {...formik.getFieldProps('physicalAddress.town')}
                  fullWidth
                  error={Boolean(
                    formik.touched.physicalAddress?.town &&
                      formik.errors.physicalAddress?.town
                  )}
                  helperText={
                    formik.touched.physicalAddress?.town &&
                    formik.errors.physicalAddress?.town
                  }
                />
                <TextField
                  autoComplete="physicalAddress"
                  type="text"
                  label="physicalAddress"
                  margin="normal"
                  {...formik.getFieldProps('physicalAddress.physicalAddress')}
                  fullWidth
                  error={Boolean(
                    formik.touched.physicalAddress?.physicalAddress &&
                      formik.errors.physicalAddress?.physicalAddress
                  )}
                  helperText={
                    formik.touched.physicalAddress?.physicalAddress &&
                    formik.errors.physicalAddress?.physicalAddress
                  }
                />
              </Stack>
              <TextField
                autoComplete="bankAccountNumber"
                type="text"
                label="Callback URL"
                margin={'normal'}
                {...formik.getFieldProps('callBackUrl')}
                fullWidth
                error={Boolean(
                  formik.touched.callBackUrl && formik.errors.callBackUrl
                )}
                helperText={
                  formik.touched.callBackUrl && formik.errors.callBackUrl
                }
              />

              <MultiSelectAutocomplete
                label="Select a product"
                options={loanProducts}
                selectedItems={selectedProducts}
                getOptionLabel={(option) => option.name}
                onChange={handleProductChange}
                onDelete={async (item) => {
                  const updatedProducts = selectedProducts.filter(
                    (p) => p.id !== item.id
                  )
                  setSelectedProducts(updatedProducts)
                  const updatedProductIds = updatedProducts.map(
                    (product) => product.id
                  )
                  if (selectedBroker && selectedBroker.id) {
                    await removeProductFromBroker(
                      selectedBroker.id,
                      item.id,
                      dispatch
                    )
                  }
                  formik.setFieldValue('productIds', updatedProductIds)
                }}
                isOptionEqualToValue={(option, value) => option.id === value.id}
              />
              {isLoadingCreateBroker ? (
                <LoadingButton />
              ) : (
                <Button
                  variant="contained"
                  type="submit"
                  disabled={Object.keys(errors).length > 0}
                  fullWidth
                  sx={{
                    my: '1%',
                  }}
                >
                  Save
                </Button>
              )}
            </Form>
          </FormikProvider>
        </Stack>
      </Stack>
    </Stack>
  )
}

export default EditBrokerDetails
