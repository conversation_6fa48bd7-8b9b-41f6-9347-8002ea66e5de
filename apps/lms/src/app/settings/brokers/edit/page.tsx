import React from 'react'



import EditBrokerDetails from './EditBrokerDetails'
import { IBrokerCreate } from '@/store/interfaces'

function EditBrokerpage() {
  const initialBrokerDetails: IBrokerCreate = {
    name: '',
    email: '',
    mobile: '',
    bankName: '',
    bankCode: '',
    swiftCode: '',
    bankAccountNumber: '',
    branchCode: '',
    accountBranchName: '',
    callBackUrl: '',
    physicalAddress: {
      country: '',
      town: '',
      physicalAddress: '',
    },
    productIds: [''],
    status: '',
  }

  return (
    <>
      <EditBrokerDetails brokerDetails={initialBrokerDetails} />
    </>
  )
}

export default EditBrokerpage
