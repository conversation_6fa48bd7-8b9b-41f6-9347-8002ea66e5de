'use client'
import {
  Box,
  Button,
  DialogContent,
  DialogTitle,
  IconButton,
  Menu,
  MenuItem,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { Form, FormikProvider, useFormik } from 'formik'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import CloseIcon from '@mui/icons-material/Close'
import { ArrowForwardIos, MoreVertOutlined } from '@mui/icons-material'
import {
  createProductCategory,
  updateProductCategory,
} from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { CustomDialog } from '@dtbx/ui/components/Dialogs'
import { LoadingButton } from '@dtbx/ui/components/Loading'
import { IProductCategory } from '@/store/interfaces'

export const CreateCategory = ({
  fromEmptyState,
}: {
  fromEmptyState?: boolean
}) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState(false)
  const formik = useFormik({
    initialValues: {
      name: '',
      code: '',
    },
    onSubmit: async (values) => {
      await createProductCategory(dispatch, values)
      setOpen(false)
    },
  })
  const handleClose = (event: unknown, reason: string) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  const { handleSubmit, getFieldProps } = formik
  const { isLoadingCreateCategory } = useAppSelector((state) => state.loans)
  return (
    <>
      <Button
        variant={fromEmptyState ? 'outlined' : 'contained'}
        endIcon={fromEmptyState ? null : <AddOutlinedIcon />}
        startIcon={fromEmptyState ? <AddOutlinedIcon /> : null}
        onClick={() => setOpen(!open)}
      >
        {fromEmptyState ? 'Create New Product Category' : 'Create Category'}
      </Button>
      <CustomDialog open={open} onClose={handleClose} maxWidth="xs" fullWidth>
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <DialogTitle
            sx={{
              fontSize: '18px',
              fontWeight: '700',
            }}
          >
            Add New Category
          </DialogTitle>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <DialogContent>
          <FormikProvider value={formik}>
            <Form noValidate onSubmit={handleSubmit}>
              <Stack direction="column">
                <TextField
                  {...getFieldProps('name')}
                  margin={'normal'}
                  name={'name'}
                  label={'Enter Category Name'}
                />
                <TextField
                  {...getFieldProps('code')}
                  margin={'normal'}
                  name={'code'}
                  label={'Enter Category Code'}
                />
                <Typography variant="body2" color={'primary.main'} mb={'2vh'}>
                  Once you click save changes, your updates will be submitted to
                  your manager for verification.
                </Typography>
                {isLoadingCreateCategory ? (
                  <LoadingButton />
                ) : (
                  <Button
                    variant="contained"
                    endIcon={<ArrowForwardIos />}
                    type={'submit'}
                  >
                    Save
                  </Button>
                )}
              </Stack>
            </Form>
          </FormikProvider>
        </DialogContent>
      </CustomDialog>
    </>
  )
}

export const CategoriesMoreMenu = ({
  category,
}: {
  category: IProductCategory
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }
  const handleClose = () => {
    setAnchorEl(null)
  }
  return (
    <>
      <IconButton
        id="demo-customized-button"
        aria-controls={open ? 'demo-customized-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
      >
        <MoreVertOutlined />
      </IconButton>
      <Menu anchorEl={anchorEl} onClose={handleClose} open={open}>
        <UpdateCategory category={category} />
        {/*<MenuItem>Delete Category</MenuItem>*/}
      </Menu>
    </>
  )
}

export const UpdateCategory = ({
  category,
}: {
  category: IProductCategory
}) => {
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState(false)
  const formik = useFormik({
    initialValues: {
      name: category.name,
    },
    onSubmit: async (values) => {
      await updateProductCategory(dispatch, values, category.id)
      setOpen(false)
    },
  })
  const handleClose = (event: unknown, reason: string) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  const { handleSubmit, getFieldProps } = formik
  const { isLoadingUpdateCategory } = useAppSelector((state) => state.loans)
  return (
    <>
      <MenuItem onClick={() => setOpen(!open)}>Update Category</MenuItem>
      <CustomDialog open={open} onClose={handleClose} maxWidth="xs" fullWidth>
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <DialogTitle
            sx={{
              fontSize: '18px',
              fontWeight: '700',
            }}
          >
            Update Category
          </DialogTitle>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <DialogContent>
          <FormikProvider value={formik}>
            <Form noValidate onSubmit={handleSubmit}>
              <Stack direction="column">
                <TextField
                  {...getFieldProps('name')}
                  margin={'normal'}
                  name={'name'}
                  label={'Enter Category Name'}
                />
                <Typography variant="body2" color={'primary.main'} mb={'2vh'}>
                  Once you click save changes, your updates will be submitted to
                  your manager for verification.
                </Typography>
                {isLoadingUpdateCategory ? (
                  <LoadingButton />
                ) : (
                  <Button
                    variant="contained"
                    endIcon={<ArrowForwardIos />}
                    type={'submit'}
                  >
                    Save
                  </Button>
                )}
              </Stack>
            </Form>
          </FormikProvider>
        </DialogContent>
      </CustomDialog>
    </>
  )
}
