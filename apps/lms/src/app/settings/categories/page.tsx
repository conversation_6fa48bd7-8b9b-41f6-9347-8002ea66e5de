'use client'
import { Divider, IconButton, Paper, Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import { useAppDispatch, useAppSelector } from '@/store'
import { getLoanProductCategories } from '@/store/actions'

import { CategoriesList } from '../../settings/categories/CategoriesList'
import { CreateCategory } from '../../settings/categories/MoreMenu'
import { EmptyCategoriesList } from '../../settings/categories/EmptyCategoriesList'

const CategoriesPage = () => {
  const dispatch = useAppDispatch()
  const { productCategories } = useAppSelector((state) => state.loans)
  useEffect(() => {
    getLoanProductCategories(dispatch, 1, 10)
  }, [])
  return (
    <Paper
      elevation={0}
      sx={{
        px: '2%',
        background: '#F7F7F7',
        height: '92vh',
        py: '2%',
      }}
    >
      <IconButton
        sx={{
          background: '#FFFFFF',
          borderRadius: '8px',
          border: '1px solid #D0D5DD',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
        }}
        onClick={() => window.history.back()}
      >
        <ArrowBackIosNewOutlinedIcon />
      </IconButton>
      <Typography
        variant="h5"
        sx={{
          textAlign: 'left',
          py: '1%',
        }}
      >
        Manage Product Categories
      </Typography>
      {productCategories.length < 1 ? (
        <>
          <Divider />
          <EmptyCategoriesList />
        </>
      ) : (
        <>
          <Stack direction="row" justifyContent={'flex-end'}>
            {/*<CustomSearchInput*/}
            {/*  placeholder={'Search Category'}*/}
            {/*  endAdornment={*/}
            {/*    <SearchRounded*/}
            {/*      sx={{*/}
            {/*        color: 'black',*/}
            {/*      }}*/}
            {/*    />*/}
            {/*  }*/}
            {/*  sx={{*/}
            {/*    width: '30%',*/}
            {/*    background: '#FFFFFF',*/}
            {/*    borderRadius: '8px',*/}
            {/*    border: '1px solid #D0D5DD',*/}
            {/*    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',*/}
            {/*  }}*/}
            {/*/>*/}
            <CreateCategory />
          </Stack>
          <CategoriesList />
        </>
      )}
    </Paper>
  )
}
export default CategoriesPage
