import { Stack, Typography } from '@mui/material'
import { EmptyFolder } from '@dtbx/ui/icons'

import { CreateCategory } from '../../settings/categories/MoreMenu'

export const EmptyCategoriesList = () => {
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        alignItems: 'center',
        alignContent: 'center',
        gap: '10px',
        paddingTop: '10%',
        height: '80vh',
        borderRadius: '4px',
      }}
    >
      <EmptyFolder />
      <Typography
        sx={{
          fontWeight: 700,
        }}
      >
        There are no records to show.
      </Typography>
      <Typography>
        Please create a loan product category to get started.
      </Typography>
      <CreateCategory fromEmptyState={true} />
    </Stack>
  )
}
