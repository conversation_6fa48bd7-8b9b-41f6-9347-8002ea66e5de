'use client'

import { But<PERSON>, Icon<PERSON>utton, Stack, Typography } from '@mui/material'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import { ShieldOutlined } from '@mui/icons-material'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import {
  fetchDropdownData,
  getAllValidations,
  getChecksByCustomerId,
  getLoanCustomerProfile,
  getChecksByOptInId,
  updateCustomerProfile,
  initiateCancelOptinRequest,
} from '@/store/actions'
import { LMSCheckerRequestsApiHandler } from '@/app/CheckerRequestsApiHandler'
import { setIsCheckerViewProfileOpen, setOpenDialog } from '@/store/reducers'
import { ReasonsDialog } from '@dtbx/ui/components/Dialogs'
import {
  CustomAccordionWithDropdown,
  CustomSkeleton,
} from '@dtbx/ui/components'
import { CheckerView } from '@/app/loans/optinRequest/CheckerView'
import { OptinMakerView } from '@/app/loans/optinRequest/MakerView'
import { CancelOptinRequestDialog } from '@/app/loans/optinRequest/OptinCancelDialog'
import { OptinRequestCheckerRejectApproveView } from '@/app/loans/optinRequest/CheckerRejectApproveView'

const CustomTypography = ({
  title,
  value,
}: {
  title: string
  value: string
}) => {
  return (
    <Stack>
      <Typography variant="subtitle2">{title}</Typography>
      <Typography
        variant="subtitle2"
        sx={{
          color: '#101828',
          fontWeight: 600,
        }}
      >
        {value}
      </Typography>
    </Stack>
  )
}
const OptinRequestPage = () => {
  const dispatch = useAppDispatch()
  const {
    selectedOptinRequest,
    currentProduct,
    optinCustomerChecks,
    customerProfile,
    customerDocuments,
    isCheckerViewProfileOpen,
    isLoadingCustomerProfile,
  } = useAppSelector((state) => state.loans)
  const router = useCustomRouter()
  const { openDialog } = useAppSelector((state) => state.approvalRequests)

  const handleUpdateProfile = async (
    newValue: {
      id: number
      name: string
    },
    selectedOption: string | number
  ) => {
    let data
    if (selectedOption === 'sourceOfWealth') {
      data = { sourceOfWealth: newValue.name }
    } else if (selectedOption === 'sourceOfFunds') {
      data = { sourceOfFunds: newValue.name }
    } else if (selectedOption === 'occupation') {
      data = { occupation: newValue.name }
    }

    if (data) {
      if (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])) {
        await updateCustomerProfile(
          data,
          selectedOptinRequest.customer?.id,
          dispatch,
          'super'
        )
        await getLoanCustomerProfile(
          dispatch,
          selectedOptinRequest.customer?.id,
          selectedOptinRequest.id
        )
      } else if (HasAccessToRights(['MAKE_UPDATE_CUSTOMER_KYC'])) {
        await updateCustomerProfile(
          data,
          selectedOptinRequest.customer?.id,
          dispatch,
          'make'
        )
        await getLoanCustomerProfile(
          dispatch,
          selectedOptinRequest.customer?.id,
          selectedOptinRequest.id
        )
      }
    }
  }

  type Category = {
    id: string
    label: string
    options: { id: number; name: string; description?: string }[]
  }

  const [categories, setCategories] = useState<Category[]>([])
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  useEffect(() => {
    const loadDropDownData = async () => {
      const wealthData = await fetchDropdownData(
        'lms/customers/source-of-wealth'
      )
      const fundsData = await fetchDropdownData('lms/customers/source-of-funds')
      const occupationData = await fetchDropdownData(
        'lms/customers/occupations'
      )

      setCategories([
        {
          id: 'sourceOfWealth',
          label: 'Source of Wealth',
          options: wealthData,
        },
        { id: 'sourceOfFunds', label: 'Source of Funds', options: fundsData },
        { id: 'occupation', label: 'Occupation', options: occupationData },
      ])
    }

    loadDropDownData()
  }, [customerDocuments])

  useEffect(() => {
    getLoanCustomerProfile(
      dispatch,
      selectedOptinRequest.customer?.id,
      selectedOptinRequest.id,
      true
    )
    getAllValidations(dispatch)
    getChecksByOptInId(dispatch, selectedOptinRequest.id)
  }, [selectedOptinRequest])

  const rejectCancelRequest = async (reasons: string) => {
    if (selectedApprovalRequest && selectedApprovalRequest.id) {
      await LMSCheckerRequestsApiHandler(
        selectedApprovalRequest,
        dispatch,
        router,
        `REJECT_${selectedApprovalRequest.makerCheckerType.type}`,
        reasons
      )
    }
  }
  const setOpen = (val: boolean) => {
    dispatch(setOpenDialog(val))
  }
  return (
    <>
      <Stack
        direction="column"
        sx={{
          gap: '20px',
          background: '#F7F7F7',
        }}
      >
        <Stack
          sx={{
            gap: '20px',
            px: '2%',
            py: '1%',
          }}
        >
          <Stack justifyContent="space-between" direction="row">
            <Stack justifyContent="flex-start">
              <IconButton
                sx={{
                  padding: '8px',
                  border: '1px solid #667085',
                  borderRadius: '8px',
                  width: '50px',
                }}
                onClick={() =>
                  dispatch(setIsCheckerViewProfileOpen(false)) &&
                  window.history.back()
                }
              >
                <ArrowBackIcon />
              </IconButton>
            </Stack>

            <Stack>
              {isCheckerViewProfileOpen ? (
                <OptinRequestCheckerRejectApproveView
                  request={selectedOptinRequest}
                />
              ) : (
                <CancelOptinRequestDialog request={selectedOptinRequest} />
              )}
            </Stack>
          </Stack>
          <Stack
            direction="row"
            sx={{
              gap: '10%',
            }}
          >
            <Stack direction="column">
              <Typography variant="body1">Customer Reference</Typography>
              <Typography variant="h6">
                {selectedOptinRequest.reference}
              </Typography>
            </Stack>
            <Stack direction="column">
              <Typography
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                }}
                variant="body1"
              >
                <ShieldOutlined /> Provider
              </Typography>
              <Typography variant="h6">{currentProduct.name}</Typography>
            </Stack>
            <Stack direction="column">
              <Typography
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                }}
                variant="body1"
              >
                <ShieldOutlined /> Customer
              </Typography>
              <Typography variant="h6">
                {selectedOptinRequest?.customer?.firstName}{' '}
                {selectedOptinRequest?.customer?.lastName}
              </Typography>
            </Stack>
          </Stack>
        </Stack>
        <Stack direction="column">
          <Stack
            sx={{
              flexDirection: 'row',
              background: '#F7F7F7',
              px: '2%',
              py: '1%',
            }}
          >
            <ReasonsDialog
              open={openDialog}
              setOpen={setOpen}
              onClick={rejectCancelRequest}
              title={'Reject Cancellation'}
              buttonText={'Save changes'}
              isLoading={false}
              buttonProps={{
                color: '#EB0045',
              }}
            />
            {isLoadingCustomerProfile ? (
              <Stack
                sx={{
                  flexDirection: 'row',
                  gap: '1vw',
                  width: '100%',
                  px: '3%',
                }}
              >
                <CustomSkeleton
                  animation="wave"
                  variant="rectangular"
                  width="30%"
                  height="60vh"
                />
                <CustomSkeleton
                  animation="wave"
                  variant="rectangular"
                  width="70%"
                  height="60vh"
                />
              </Stack>
            ) : (
              <Stack
                sx={{
                  flexDirection: 'row',
                  background: '#FFF',
                  px: '3%',
                  py: '2%',
                  width: '100%',
                  borderRadius: '12px',
                }}
              >
                <Stack
                  sx={{
                    width: '30%',
                    flexDirection: 'column',
                    gap: '20px',
                  }}
                >
                  <Stack
                    sx={{
                      width: '100%',
                      border: '1px solid #EAECF0',
                      borderRadius: '8px',
                      padding: '1%',
                      boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                      flexDirection: 'column',
                      gap: '20px',
                    }}
                  >
                    <Typography
                      variant="subtitle2"
                      sx={{
                        color: '#101828',
                        fontWeight: 700,
                      }}
                    >
                      {customerProfile.firstName} {customerProfile.lastName}
                    </Typography>
                    <CustomTypography
                      title={'Date of Birth'}
                      value={customerProfile.dateOfBirth}
                    />
                    <CustomTypography
                      title={'Email'}
                      value={customerProfile.email}
                    />
                    <CustomTypography
                      title={'KRA Pin'}
                      value={customerProfile.kraPinNumber}
                    />
                    <CustomTypography
                      title={'Mobile No'}
                      value={customerProfile.mobile}
                    />
                    <CustomTypography
                      title={'Nationality'}
                      value={customerProfile.nationality}
                    />
                    <CustomTypography
                      title={'National ID No'}
                      value={customerProfile.idDocumentNumber}
                    />
                    <CustomTypography
                      title={'Address'}
                      value={`${customerProfile.physicalAddress?.street}, ${customerProfile.physicalAddress?.city}, ${customerProfile.physicalAddress?.country}`}
                    />
                    {customerProfile.branch && (
                      <CustomTypography
                        title={'Branch'}
                        value={customerProfile.branch}
                      />
                    )}
                    {customerProfile.cif && (
                      <CustomTypography
                        title={'CIF'}
                        value={customerProfile.cif}
                      />
                    )}
                    {customerProfile.customerType && (
                      <CustomTypography
                        title={'Customer Type'}
                        value={customerProfile.customerType}
                      />
                    )}
                    {customerProfile.pepPipDeclarationStatus && (
                      <CustomTypography
                        title={'Pep Pip Declaration Status'}
                        value={customerProfile.pepPipDeclarationStatus}
                      />
                    )}
                    {customerProfile.pepPipRemarks && (
                      <CustomTypography
                        title={'Pep Pip Remarks'}
                        value={customerProfile.pepPipRemarks}
                      />
                    )}
                    {customerProfile.pipPepCategory && (
                      <CustomTypography
                        title={'Pip Pep Category'}
                        value={customerProfile.pipPepCategory}
                      />
                    )}
                    {customerProfile.ssn && (
                      <CustomTypography
                        title={'SSN'}
                        value={customerProfile.ssn}
                      />
                    )}
                    <CustomTypography
                      title={'Is US Citizen'}
                      value={customerProfile.usCitizen ? 'YES' : 'NO'}
                    />
                    {customerProfile.sourceOfFunds?.value && (
                      <CustomTypography
                        title={'Source of Funds'}
                        value={customerProfile.sourceOfFunds?.value}
                      />
                    )}
                    {customerProfile.sourceOfWealth?.value && (
                      <CustomTypography
                        title={'Source of Wealth'}
                        value={customerProfile.sourceOfWealth?.value}
                      />
                    )}
                    {customerProfile.occupation?.description && (
                      <CustomTypography
                        title={'Occupation'}
                        value={customerProfile.occupation?.description}
                      />
                    )}
                  </Stack>
                  <Stack
                    sx={{
                      flexDirection: 'column',
                      gap: '20px',
                      // border: '2px solid red',
                      border: '1px solid #EAECF0',
                      borderRadius: '8px',
                      boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                    }}
                  >
                    <CustomAccordionWithDropdown
                      title="Update Financial Information"
                      variant="subtitle2"
                      color="#101828"
                      fontWeight="700"
                      categories={categories}
                      onChange={handleUpdateProfile}
                    />
                  </Stack>
                </Stack>
                <Stack
                  sx={{
                    width: '70%',
                    px: '2%',
                    flexDirection: 'column',
                    gap: '20px',
                  }}
                >
                  {isCheckerViewProfileOpen ? (
                    <CheckerView
                      kycData={optinCustomerChecks}
                      customerDocuments={customerDocuments}
                      requestId={selectedOptinRequest.id}
                    />
                  ) : (
                    <OptinMakerView
                      kycData={optinCustomerChecks}
                      customerDocuments={customerDocuments}
                      requestId={selectedOptinRequest.id}
                      request={selectedOptinRequest}
                    />
                  )}
                </Stack>
              </Stack>
            )}
          </Stack>
        </Stack>
      </Stack>
    </>
  )
}
export default OptinRequestPage
