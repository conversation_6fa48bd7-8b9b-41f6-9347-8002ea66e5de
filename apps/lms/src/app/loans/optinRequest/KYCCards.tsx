'use client'
import { ArrowForwardIosOutlined, QueryBuilder } from '@mui/icons-material'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import {
  Button,
  DialogActions,
  DialogTitle,
  Stack,
  styled,
  TextField,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import {
  IApprovalRequest,
  ICustomerCheck,
  ILoanOptinRequest,
  ILoanRequest,
} from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  completeValidationOverride,
  initiateRejectResolveOptinRequest,
  rerunCheck,
} from '@/store/actions'
import { HasAccessToRights } from '@dtbx/store/utils'
import { LoadingButton } from '@dtbx/ui/components/Loading'

import {
  ApproveChangesDrawer,
  RejectOverride,
} from '@/app/loans/request/KYCStages'
import { CustomDialog } from '@dtbx/ui/components/Dialogs'
import {
  ApproveOptinChangesDrawer,
  RejectOptinValidationOverride,
} from '@/app/loans/optinRequest/KYCStages'

const StyledTitle = styled(Typography)({
  color: '#101828',
  fontWeight: 500,
})
const StyledButton = styled(Button)({
  padding: 0,
  fontWeight: 500,
})
export const OptinKYCValidationCard = ({
  key,
  check,
  request,
  isChecker,
  selectedApprovalRequest,
}: {
  key: string
  check: ICustomerCheck
  request: ILoanOptinRequest
  isChecker: boolean
  selectedApprovalRequest?: IApprovalRequest
}) => {
  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)

  const dispatch = useAppDispatch()
  const handleRecheck = async () => {
    await rerunCheck(request.id, { checkId: check?.id || '' }, dispatch)
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject',
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveValidationOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve',
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleRejectValidation = async (comments: string) => {
    const data = {
      comments: comments || '',
      status: 'Failed',
    }
    if (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])) {
      await initiateRejectResolveOptinRequest(
        dispatch,
        request.id,
        'super',
        'reject',
        data,
        check?.id
      )
    } else if (HasAccessToRights(['MAKE_UPDATE_CUSTOMER_KYC'])) {
      await initiateRejectResolveOptinRequest(
        dispatch,
        request.id,
        'maker',
        'reject',
        data,
        check?.id
      )
    }
  }
  const handleResolveValidation = async (comments: string) => {
    const data = {
      comments: comments || '',
      status: 'Passed',
    }
    if (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])) {
      await initiateRejectResolveOptinRequest(
        dispatch,
        request.id,
        'super',
        'resolve',
        data,
        check?.id
      )
    } else if (HasAccessToRights(['MAKE_UPDATE_CUSTOMER_KYC'])) {
      await initiateRejectResolveOptinRequest(
        dispatch,
        request.id,
        'maker',
        'resolve',
        data,
        check?.id
      )
    }
  }
  return (
    <Stack
      key={key}
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${check?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">{check.code}</StyledTitle>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '10px',
          }}
        >
          {check?.status === 'Failed' && !request.cancelled && !isChecker && (
            <Stack
              sx={{
                flexDirection: 'row',
              }}
            >
              <RejectValidationDialog
                title={`${check.code} Check Failed`}
                description={check.message}
                isChecker={isChecker}
                handleOverride={handleRejectValidation}
              />
              <ResolveValidationDialog
                title={`${check.code} Check Failed`}
                description={check.message}
                isChecker={isChecker}
                handleOverride={handleResolveValidation}
              />
            </Stack>
          )}
          {check?.status === 'Failed' &&
            isChecker &&
            selectedApprovalRequest &&
            selectedApprovalRequest.status === 'PENDING' &&
            selectedApprovalRequest.makerCheckerType.type ===
              'LMS_UPDATE_REQUEST' &&
            selectedApprovalRequest.entityId === check.id && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOptinValidationOverride
                  title={check.code}
                  handleReject={handleRejectOverride}
                  fromActivityLog={false}
                  selectedApprovalRequest={selectedApprovalRequest}
                />
                <ApproveOptinChangesDrawer
                  handleOverride={handleApproveValidationOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanOptinRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={check.code}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            check?.status === 'Failed' &&
            !isChecker && (
              <StyledButton
                variant="text"
                onClick={handleRecheck}
                sx={{ display: 'none' }}
              >
                {'Check Again'}
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      <Typography variant="subtitle2">{check?.message}</Typography>
      {check.code === 'WORLD_CHECK_INDIVIDUAL' && check.providerReference && (
        <Typography variant="subtitle2">
          Case ID: {check.providerReference}
        </Typography>
      )}
      <Typography
        variant="subtitle3"
        sx={{
          color: check?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {check?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed {check.code} check
          </>
        ) : check?.status === 'Pending' || check?.status === 'NotRun' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            {check?.status === 'Pending' ? 'Pending' : 'Not Run'}
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed {check.code} check
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}
interface IResolveProps {
  title: string
  description: string
  isChecker: boolean
  handleOverride: (comments: string) => void
}
export const ResolveValidationDialog = ({
  title,
  description,
  isChecker,
  handleOverride,
}: IResolveProps) => {
  const [open, setOpen] = useState<boolean>(false)
  const [comments, setComments] = useState<string>('')
  const [error, setError] = useState<boolean>(false)
  const { isLoadingOverrideValidationCheck } = useAppSelector(
    (state) => state.loans
  )
  const handleClose = (
    event: React.MouseEvent<HTMLElement> | null,
    reason: string
  ) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  const handleSaveChanges = () => {
    if (comments === '') {
      setError(true)
      return
    }
    handleOverride(comments)
    handleClose(null, 'close')
  }
  return (
    <>
      <StyledButton
        variant="text"
        color="success"
        disabled={isChecker}
        onClick={() => setOpen(!open)}
      >
        Resolve
      </StyledButton>
      <CustomDialog open={open} maxWidth="xs" fullWidth>
        <DialogTitle
          sx={{
            py: '1%',
            px: '3%',
          }}
        >
          <Typography variant="subtitle2" color="text.primary">
            {title}
          </Typography>
        </DialogTitle>
        <Stack
          sx={{
            flexDirection: 'column',
            px: '3%',
            gap: '0.5vh',
          }}
        >
          <Typography variant="subtitle3">
            Reason for failure: {description}. <br />
            Leave a reason for resolving the request below:
          </Typography>
          <TextField
            multiline
            minRows={3}
            label="Comments"
            error={error}
            onChange={(e) => {
              setComments(e.target.value)
              e.target.value.length < 1 ? setError(true) : setError(false)
            }}
          />
          <Typography variant="label1">
            Once you click save changes, your updates will be submitted to your
            manager for verification.
          </Typography>
          <Typography
            variant="subtitle3"
            sx={{
              color: 'error.main',
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            <InfoOutlinedIcon />
            Failed {title}
          </Typography>
        </Stack>
        <DialogActions sx={{ justifyContent: 'space-between' }}>
          <Button
            variant="contained"
            sx={{
              backgroundColor: '#D92D20 !important',
              width: '40%',
            }}
            onClick={(e) => handleClose(e, 'close')}
          >
            Cancel
          </Button>
          {isLoadingOverrideValidationCheck ? (
            <LoadingButton width="40%" />
          ) : (
            <Button
              variant="contained"
              endIcon={<ArrowForwardIosOutlined />}
              sx={{
                width: '40%',
                textWrap: 'nowrap',
                gap: 0,
              }}
              onClick={handleSaveChanges}
            >
              Save Changes
            </Button>
          )}
        </DialogActions>
      </CustomDialog>
    </>
  )
}
export const RejectValidationDialog = ({
  title,
  description,
  isChecker,
  handleOverride,
}: IResolveProps) => {
  const [open, setOpen] = useState<boolean>(false)
  const [comments, setComments] = useState<string>('')
  const [error, setError] = useState<boolean>(false)
  const { isLoadingOverrideValidationCheck } = useAppSelector(
    (state) => state.loans
  )
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleClose = (
    event: React.MouseEvent<HTMLElement> | null,
    reason: string
  ) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  const handleSaveChanges = () => {
    if (comments === '') {
      setError(true)
      return
    }
    handleOverride(comments)
    handleClose(null, 'close')
  }
  return (
    <>
      <StyledButton
        disabled={isChecker}
        variant="text"
        color="error"
        onClick={() => setOpen(!open)}
      >
        Reject
      </StyledButton>
      <CustomDialog open={open} maxWidth="xs" fullWidth>
        <DialogTitle
          sx={{
            py: '1%',
            px: '3%',
          }}
        >
          <Typography variant="subtitle2" color="text.primary">
            {title}
          </Typography>
        </DialogTitle>
        <Stack
          sx={{
            flexDirection: 'column',
            px: '3%',
            gap: '0.5vh',
          }}
        >
          <Typography variant="subtitle3">
            Reason for failure: {description}. <br />
            Leave a reason for rejecting the request below:
          </Typography>
          <TextField
            multiline
            minRows={3}
            label="Comments"
            error={error}
            onChange={(e) => {
              setComments(e.target.value)
              e.target.value.length < 1 ? setError(true) : setError(false)
            }}
          />
          <Typography variant="label1">
            Once you click save changes, your updates will be submitted to your
            manager for verification.
          </Typography>
          <Typography
            variant="subtitle3"
            sx={{
              color: 'error.main',
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            <InfoOutlinedIcon />
            Failed {title}
          </Typography>
        </Stack>
        <DialogActions sx={{ justifyContent: 'space-between' }}>
          <Button
            variant="contained"
            sx={{
              backgroundColor: '#D92D20 !important',
              width: '40%',
            }}
            onClick={(e) => handleClose(e, 'close')}
          >
            Cancel
          </Button>
          {isLoadingOverrideValidationCheck ? (
            <LoadingButton width="40%" />
          ) : (
            <Button
              variant="contained"
              endIcon={<ArrowForwardIosOutlined />}
              sx={{
                width: '40%',
                textWrap: 'nowrap',
                gap: 0,
              }}
              onClick={handleSaveChanges}
            >
              Save Changes
            </Button>
          )}
        </DialogActions>
      </CustomDialog>
    </>
  )
}
